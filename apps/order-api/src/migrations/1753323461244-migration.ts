import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1753323461244 implements MigrationInterface {
  name = 'migration1753323461244';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "call_group" ADD "zalo_oa_id" TEXT`);
    await queryRunner.query(`ALTER TABLE "call_group" ADD "hotline_zalo_oa_id" TEXT`);
    await queryRunner.query(`ALTER TABLE "call_center_extensions" ADD "type" INT2`);
    
    await queryRunner.query(`
      CREATE TABLE "call_permission_request" (
        "id" SERIAL PRIMARY KEY,
        "created_at" TIMESTAMPTZ DEFAULT now(),
        "updated_at" TIMESTAMPTZ DEFAULT now(),
        "phone" VARCHAR,
        "leadId" INT,
        "userId" INT,
        "status" INT DEFAULT 0
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "call_permission_request"`);
    await queryRunner.query(`ALTER TABLE "call_center_extensions" DROP COLUMN "type"`);
    await queryRunner.query(`ALTER TABLE "call_group" DROP COLUMN "hotline_zalo_oa_id"`);
    await queryRunner.query(`ALTER TABLE "call_group" DROP COLUMN "zalo_oa_id"`);
  }
}
