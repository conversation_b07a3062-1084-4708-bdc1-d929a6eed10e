import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1752122862303 implements MigrationInterface {
    name = 'migration1752122862303'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "landing_pages" ADD "product_type" smallint NOT NULL DEFAULT '1'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "landing_pages" DROP COLUMN "product_type"`);
    }

}
