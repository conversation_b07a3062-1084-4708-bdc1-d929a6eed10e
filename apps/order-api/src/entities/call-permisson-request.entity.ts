import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../../../core/entities/base/base-entity.entity';
import { Expose } from 'class-transformer';
import {
  CallPermissonRequestStatus,
  CallPermissonRequestType,
} from '../enums/call-permisson-request.enum';

@Entity('call_permission_request')
export class CallPermissionRequest extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id: number;

  @Column({
    name: 'phone_number',
    type: 'varchar',
    length: 255,
  })
  @Expose()
  phoneNumber: string;

  @Column({
    name: 'lead_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  leadId: number;

  @Column({
    name: 'user_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  userId: number;

  @Column({
    name: 'status',
    type: 'int',
    default: CallPermissonRequestStatus.awaiting_response,
  })
  @Expose()
  status: CallPermissonRequestStatus;

  @Column({
    name: 'type',
    type: 'int',
    default: CallPermissonRequestType.request,
  })
  @Expose()
  type: CallPermissonRequestType;
}
