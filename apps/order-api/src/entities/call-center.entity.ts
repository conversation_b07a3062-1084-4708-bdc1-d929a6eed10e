import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../../../core/entities/base/base-entity.entity';
import { Expose } from 'class-transformer';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { CallGroup } from './call-group.entity';
import { CallRelatedStatus } from '../enums/call-related-status.enum';

@Entity('call_centers')
export class CallCenter extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id: number;

  @Column({
    name: 'customer_id',
    type: 'varchar',
    length: 225,
    nullable: true,
  })
  @Expose()
  customerId?: string;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 128,
    nullable: false,
  })
  @Expose()
  name: string;

  @Column({
    name: 'email',
    type: 'text',
    nullable: true,
  })
  @Expose()
  email: string;

  @Column({
    name: 'phone',
    type: 'text',
    nullable: true,
  })
  @Expose()
  phone: string;

  @Column({
    name: 'sip_wss_url',
    type: 'varchar',
    default: '',
  })
  @Expose()
  sipWssUrl: string;

  @Column({
    name: 'sip_domain',
    type: 'varchar',
    default: '',
  })
  @Expose()
  sipDomain: string;

  @Column({
    name: 'country_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  countryId?: number;

  @Column({
    name: 'company_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  companyId?: number;

  @Column({
    name: 'is_active',
    type: 'boolean',
    default: true,
  })
  @Expose()
  isActive: boolean;

  @Column({
    name: 'created_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  createdBy?: number;

  @Column({
    name: 'updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  @NonEmptyTransform()
  updatedBy?: number;

  @Column({
    name: 'api_key',
    type: 'text',
    nullable: true,
  })
  @Expose()
  apiKey?: string;

  @Column({
    name: 'hotline_id',
    type: 'text',
    nullable: true,
  })
  @Expose()
  hotlineId?: string;

  @Column({
    name: 'metadata',
    type: 'text',
    nullable: true,
  })
  @Expose()
  metadata?: string;

  @Column({
    name: 'status',
    type: 'int',
    default: CallRelatedStatus.connected,
  })
  status: CallRelatedStatus;

  @OneToMany(
    () => CallGroup,
    callGroup => callGroup.callCenter,
  )
  @Expose()
  callGroups: CallGroup[];

  @Column({
    name: 'zalo_oa_id',
    type: 'text',
    nullable: true,
  })
  @Expose()
  zaloOaId?: string;

  @Column({
    name: 'hotline_zalo_oa_id',
    type: 'text',
    nullable: true,
  })
  @Expose()
  hotlineZaloOaId?: string;
}
