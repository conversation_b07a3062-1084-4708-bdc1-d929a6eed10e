import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateCustomerDto } from 'apps/order-api/src/dtos/create-customer.dto';
import { UpdateCustomerDto } from 'apps/order-api/src/dtos/update-customer.dto';
import { Customer } from 'apps/order-api/src/entities/customer.entity';
import { Order } from 'apps/order-api/src/entities/order.entity';
import { TagStatus, TagType } from 'apps/order-api/src/enums/tag.enum';
import { CustomersFilter } from 'apps/order-api/src/filters/customers.filter';
import { plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { difference, identity, isEmpty, isNil, omit, pickBy } from 'lodash';
import { getConnection, In, Repository } from 'typeorm';
import { TagsService } from './tags.service';
import { LocationsService } from './locations.service';
import { AuthUser } from 'core/interfaces/auth-user.interface';
import { UncensorLog, UncensorType } from 'apps/order-api/src/entities/uncensored-log.entity';
import { UncensorLogsFilter } from 'apps/order-api/src/filters/uncensor-logs.filter';
import { OrderStatus } from 'core/enums/order-status.enum';
import { Lead } from 'apps/order-api/src/entities/lead.entity';
import { LeadCareItem } from 'apps/order-api/src/entities/lead-care-item.entity';
import { AddCustomerNoteDto } from 'apps/order-api/src/dtos/add-customer-note.dto';
import { CustomerNotes } from 'apps/order-api/src/entities/customer-notes.entity';
import ExcelUtils from 'core/utils/ExcelUtils';
import StringUtils from 'core/utils/StringUtils';
import { CustomerGender } from 'apps/order-api/src/enums/customer.enum';
import * as moment from 'moment-timezone';
import { LeadCare } from 'apps/order-api/src/entities/lead-care.entity';
import { Tag } from 'apps/order-api/src/entities/tag.entity';
import { CustomerOrdersFilter } from 'apps/order-api/src/filters/customer-orders.filter';
import { AddCustomerTagByNameDto } from 'apps/order-api/src/dtos/add-customer-tag-by-name.dto';

@Injectable()
export class CustomersService {
  constructor(
    @InjectRepository(Customer, orderConnection)
    private readonly customerRepository: Repository<Customer>,

    @InjectRepository(Order, orderConnection)
    private readonly orderRepo: Repository<Order>,

    @InjectRepository(Lead, orderConnection)
    private leadsRepo: Repository<Lead>,
    @InjectRepository(LeadCare, orderConnection)
    private leadCareRepo: Repository<LeadCare>,
    @InjectRepository(LeadCareItem, orderConnection)
    private leadCareItemsRepo: Repository<LeadCareItem>,
    @InjectRepository(CustomerNotes, orderConnection)
    private customerNotesRepo: Repository<CustomerNotes>,
    @InjectRepository(Lead, orderConnection)
    private readonly leadRepo: Repository<Lead>,
    @InjectRepository(Tag, orderConnection)
    private tagRepo: Repository<Tag>,
    private locationService: LocationsService,
    private tagService: TagsService,
  ) {}

  async getCustomers(
    pagination: PaginationOptions,
    filter: CustomersFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ) {
    const companyId = request?.user?.companyId;

    const mQuery = this.customerRepository.createQueryBuilder('customer');

    if (pagination) {
      mQuery.take(pagination.limit).skip(pagination.skip);
    }
    const { phone, query, status, gender, fbId, scopedUserId, getOrder, ids } = filter;
    if (phone) mQuery.andWhere(`customer.phone ILIKE :phone`, { phone: `%${phone}%` });
    if (query)
      mQuery.andWhere(`(customer.name ~* :query or customer.phone ~* :query)`, {
        query,
      });
    if (fbId) {
      mQuery.andWhere('customer.fbGlobalId = :fbId', { fbId });
    }
    if (scopedUserId) {
      const [, psid] = scopedUserId.split('_');
      mQuery.andWhere('customer.fbScopedUserId = :scopedUserId', {
        scopedUserId: psid || scopedUserId,
      });
    }
    if (status) mQuery.andWhere('customer.status IN (:...status)', { status });
    if (gender) mQuery.andWhere('customer.gender IN (:...gender)', { gender });
    if (companyId) mQuery.andWhere('customer.companyId = :companyId', { companyId });
    if (!isEmpty(ids)) mQuery.andWhere('customer.id IN (:...ids)', { ids });

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds))
        mQuery.andWhere('customer.country_id IN (:...countryIds)', {
          countryIds,
        });
    }

    mQuery.leftJoinAndSelect('customer.tags', 'tags', 'tags.status = :tStatus', {
      tStatus: TagStatus.active,
    });
    mQuery.orderBy('customer.id', 'DESC');
    let [customers, count] = await mQuery.getManyAndCount();
    let customersWithOrder = [];
    if (getOrder && customers.length > 0) {
      const phones = customers.map(it => it.phone);
      const ordersOfCustomer = await this.orderRepo
        .createQueryBuilder('o')
        .where('o.customer_phone IN (:...phones)', { phones })
        .addOrderBy('o.created_at', 'DESC')
        .getMany();
      const hashMapOrdersCustomer: Record<string, any> = {};
      phones.forEach(it => {
        hashMapOrdersCustomer[it] = ordersOfCustomer.filter(o => o.customerPhone === it);
      });
      customersWithOrder = customers.map(c => {
        return {
          ...c,
          orders: hashMapOrdersCustomer[c.phone] || [],
        };
      });
    }
    return [plainToInstance(Customer, customersWithOrder), count];
  }

  async getCustomer(phone: string, request?: Record<string, any>) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const qb = this.customerRepository.createQueryBuilder('customer');
    qb.andWhere('customer.phone = :phone AND customer.companyId = :companyId', {
      phone,
      companyId,
    })
      .leftJoinAndSelect('customer.tags', 'tags')
      // .leftJoinAndMapMany(
      //   'customer.orders',
      //   'orders',
      //   'orders',
      //   'orders.customer_phone = customer.phone',
      // )
      .leftJoinAndSelect('customer.notes', 'notes')
      .leftJoin('wards', 'w', 'w.id = customer.wardId')
      .addSelect('w.name', 'ward')
      .leftJoin('districts', 'd', 'd.id = customer.districtId')
      .addSelect('d.name', 'district')
      .leftJoin('provinces', 'p', 'p.id = customer.provinceId')
      .addSelect('p.name', 'province');

    const customer = await qb.getOne();
    if (!customer) throw new BadRequestException('Customer not found');
    const ordersOfCustomer = await this.orderRepo
      .createQueryBuilder('o')
      .where('o.customer_phone = :phone', { phone: customer.phone })
      .addOrderBy('o.created_at', 'DESC')
      .getMany();
    const customerWithOrder = {
      ...customer,
      orders: ordersOfCustomer,
    };
    return plainToInstance(Customer, customerWithOrder);
  }

  async mobileGetCustomer(phone: string, request?: Record<string, any>): Promise<Customer> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const qb = this.customerRepository.createQueryBuilder('customer');
    qb.andWhere('customer.phone = :phone AND customer.companyId = :companyId', {
      phone,
      companyId,
    })
      .leftJoinAndSelect('customer.tags', 'tags')
      // .leftJoinAndMapMany(
      //   'customer.orders',
      //   'orders',
      //   'orders',
      //   `orders.customer_phone = customer.phone AND orders.status != ${OrderStatus.Draft}`,
      // )
      .leftJoinAndSelect('customer.notes', 'notes')
      .leftJoin('wards', 'w', 'w.id = customer.wardId')
      .addSelect('w.name', 'ward')
      .leftJoin('districts', 'd', 'd.id = customer.districtId')
      .addSelect('d.name', 'district')
      .leftJoin('provinces', 'p', 'p.id = customer.provinceId')
      .addSelect('p.name', 'province')
      .orderBy('notes.created_at', 'DESC');

    const customer = await qb.getOne();
    if (!customer) throw new BadRequestException('Customer not found');
    const ordersOfCustomer = await this.orderRepo
      .createQueryBuilder('o')
      .where('o.customer_phone = :phone', { phone: customer.phone })
      .andWhere('o.status != :status', { status: OrderStatus.Draft })
      .addOrderBy('o.created_at', 'DESC')
      .getMany();
    const customerWithOrder = {
      ...customer,
      orders: ordersOfCustomer,
    };
    return plainToInstance(Customer, customerWithOrder);
  }

  async getCustomerOrders(
    phone: string,
    request?: Record<string, any>,
    filter?: CustomerOrdersFilter,
  ) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const mQuery = this.orderRepo.createQueryBuilder('o');
    mQuery
      .leftJoinAndSelect('o.products', 'products')
      .andWhere('o.customer_phone =:phone', { phone })
      .andWhere('o.company_id =:companyId', { companyId });
    if (filter.orderBy) {
      mQuery.orderBy(`o.${filter.orderBy}`, filter.sort);
    }
    const [orders, count] = await mQuery.getManyAndCount();
    if (orders.length === 0) {
      return [[], 0];
    }
    const orderIds = orders.map(o => o.id);
    const leadCares = await this.leadCareRepo
      .createQueryBuilder('lc')
      .innerJoin('leads', 'l', 'l.id = lc.leadId')
      .innerJoin('lead_care_items', 'lci', 'lci.leadCareId = lc.id')
      .where('l.orderId IN (:...orderIds)', { orderIds })
      .andWhere(`(lci.note != 'from system' OR lci.note IS NULL)`)
      .groupBy('lc.leadId')
      .select(['lc.leadId', 'COUNT(*) as count'])
      .getRawMany();
    const leads = await this.leadRepo.find({ where: { orderId: In(orderIds) } });
    const leadHashMap = leads.reduce((prev, next) => {
      prev[next.orderId] = next;
      return prev;
    }, {});

    const ordersWithCare = await Promise.all(
      orders.map(async o => {
        let leadCare;
        const lead = await this.leadRepo.findOne({ orderId: o.id });
        if (lead) {
          leadCare = leadCares.find(lc => lc.lc_lead_id === lead.id);
        }
        return {
          ...o,
          leadId: leadHashMap[o.id]?.id,
          status: OrderStatus[o.status],
          leadCareCount: Number(leadCare?.count) || 0,
        };
      }),
    );

    return [ordersWithCare, count];
  }

  async mobileGetCustomerOrders(phone: string, request?: Record<string, any>) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const mQuery = this.orderRepo.createQueryBuilder('o');
    mQuery
      .leftJoinAndSelect('o.products', 'products')
      .andWhere('o.customer_phone =:phone', { phone })
      .andWhere('o.company_id =:companyId', { companyId })
      .orderBy(`o.created_at`, 'DESC');

    const [orders, count] = await mQuery.getManyAndCount();

    if (orders.length === 0) {
      return [[], 0];
    }

    const orderIds = orders.map(o => o.id);

    const leadCares = await this.leadCareRepo
      .createQueryBuilder('lc')
      .innerJoin('leads', 'l', 'l.id = lc.leadId')
      .innerJoin('lead_care_items', 'lci', 'lci.leadCareId = lc.id')
      .where('l.orderId IN (:...orderIds)', { orderIds })
      .andWhere(`(lci.note != 'from system' OR lci.note IS NULL)`)
      .groupBy('lc.leadId')
      .select(['lc.leadId', 'COUNT(*) as count'])
      .getRawMany();
    const leads = await this.leadRepo.find({ where: { orderId: In(orderIds) } });
    const leadHashMap = leads.reduce((prev, next) => {
      prev[next.orderId] = next;
      return prev;
    }, {});

    const ordersWithCare = await Promise.all(
      orders.map(async o => {
        let leadCare;
        const lead = await this.leadRepo.findOne({ orderId: o.id });
        if (lead) {
          leadCare = leadCares.find(lc => lc.lc_lead_id === lead.id);
        }
        return {
          ...o,
          leadId: leadHashMap[o.id]?.id,
          leadCareCount: Number(leadCare?.count) || 0,
        };
      }),
    );
    return [ordersWithCare, count];
  }

  async saveCustomers(customers: Customer[]): Promise<Customer[]> {
    return this.customerRepository.save(customers);
  }

  async updateCustomer(
    data: UpdateCustomerDto,
    countryId: number,
    request?: Record<string, any>,
  ): Promise<Customer> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const query = this.customerRepository
      .createQueryBuilder('o')
      .andWhere('o.phone = :phone', { phone: data.phone })
      .andWhere('o.country_id = :countryId', { countryId })
      .andWhere('o.company_id = :companyId', { companyId });
    const customer = await query.getOne();
    if (!customer) throw new NotFoundException('Không tìm thấy khách hàng');

    const updateData = omit(data, ['createdAt', 'updatedAt', 'phone']);

    let mCustomer = plainToInstance(Customer, {
      countryId,
      ...customer,
      ...updateData,
    });
    if (!isNil(data.tagIds)) {
      const mTags = await this.tagService.findByIds(data.tagIds, TagType.order);
      mCustomer.tags = mTags;
    }

    mCustomer = await this.getDetailAddress(mCustomer, data);

    return await this.customerRepository.save(mCustomer);
  }

  async createCustomer(
    data: CreateCustomerDto,
    countryId: number,
    request?: Record<string, any>,
  ): Promise<Customer> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const check = await this.customerRepository.findOne({
      phone: data?.phone,
      countryId,
      companyId,
    });
    if (check) throw new BadRequestException('Số điện thoại đã được sử dụng');
    let mCustomer = plainToInstance(Customer, {
      countryId,
      ...data,
      companyId,
      creatorId: request?.user?.id,
    });
    if (!isNil(data.tagIds)) {
      const mTags = await this.tagService.findByIds(data.tagIds, TagType.order);
      mCustomer.tags = mTags;
    }
    mCustomer = await this.getDetailAddress(mCustomer, data);
    const result = await this.customerRepository.save(mCustomer);
    if (data.note) {
      await this.customerNotesRepo.save({
        note: data.note,
        customerId: mCustomer.id,
        creatorId: request?.user?.id,
      });
    }
    return result;
  }

  async getDetailAddress(
    mCustomer: Customer,
    data: CreateCustomerDto | UpdateCustomerDto,
  ): Promise<Customer> {
    const { wardId, districtId, provinceId, address } = data;
    mCustomer.fullAddress = `${address || ''}`;
    if (districtId && provinceId) {
      if (wardId) {
        const ward = await this.locationService.getWard(wardId, {
          where: { districtId },
        });
        if (ward) {
          mCustomer.fullAddress += `, ${ward.name}`;
        }
      }
      const district = await this.locationService.getDistrict(districtId);
      const province = await this.locationService.getProvince(provinceId);
      mCustomer.fullAddress += `, ${district.name}, ${province.name}`;
    }
    return mCustomer;
  }

  async uncensorCustomer(
    id: number,
    type: UncensorType,
    user: AuthUser,
  ): Promise<Partial<Customer>> {
    const qb = this.customerRepository
      .createQueryBuilder('c')
      .where('c.id = :id', { id })
      .select('c.id');
    if (type === UncensorType.address) {
      qb.addSelect(['c.address', 'c.fullAddress']);
    } else {
      qb.addSelect(['c.phone']);
    }
    const customer = await qb.getOne();
    if (!customer) {
      throw new NotFoundException('Customer is not exists');
    }

    const result = await getConnection(orderConnection)
      .createQueryBuilder()
      .insert()
      .into(UncensorLog)
      .values({
        customerId: customer.id,
        userId: user.id,
        type,
      })
      .execute();

    console.log(`insert uncensor log result`, result);

    return pickBy(customer, identity);
  }

  async fetchUncensorLogs(filter: UncensorLogsFilter, pagination?: PaginationOptions) {
    const qb = getConnection(orderConnection).createQueryBuilder(UncensorLog, 'l');

    if (pagination) qb.take(pagination?.limit).skip(pagination?.skip);

    const { customerId, userId, type } = filter;
    if (customerId) {
      qb.andWhere('l.customerId = :customerId', { customerId });
      if (userId) {
        qb.andWhere('l.userId = :userId', { userId });
      }
      if (type) {
        qb.andWhere('l.type = :type', { type });
      }
      return qb.orderBy('l.createdAt', 'DESC').getManyAndCount();
    }
  }

  async addCustomerNote(reqBody: AddCustomerNoteDto, request: Record<string, any>) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const customer = await this.customerRepository
      .createQueryBuilder('customer')
      .andWhere('customer.phone = :phone AND customer.companyId = :companyId', {
        phone: reqBody.customerPhone,
        companyId,
      })
      .getOne();
    if (!customer) throw new NotFoundException('Không tìm thấy khách hàng');
    return await this.customerNotesRepo.save({
      note: reqBody.note,
      customerId: customer.id,
      creatorId: request?.user?.id,
    });
  }

  async deleteCustomerNote(noteId: number, request: Record<string, any>) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const note = await this.customerNotesRepo.findOne({ where: { id: noteId } });
    if (!note) throw new NotFoundException('Không tìm thấy ghi chú');
    await this.customerNotesRepo.delete({ id: noteId });
    return true;
  }

  async importCustomer(buffer: Buffer, request: Record<string, any>, countryId: number) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();
    const userId = request?.user?.id;
    const data = ExcelUtils.read(buffer, 0, 'NO');

    const date = new Date();
    const results = [];
    const customers = data.map(item => {
      if (Object.keys(item).length === 0) return undefined;
      return {
        no: item['No.'] || '',
        name: item['Customer name *'] || '',
        phone: item['Phone number *'] || '',
        gender: item['Gender'] || '',
        dateOfBirth: item['Data of birth'] || '',
        address: item['Address'] || '',
        province: item['Province'] || '',
        district: item['District'] || '',
        ward: item['Ward'] || '',
        tag: item['Tag'] || '',
        note: item['Note'] || '',
      };
    });
    for (const each of customers) {
      if (each === undefined) continue;
      try {
        const result = await this.createCustomerFromImport(each, userId, companyId, countryId);
        results.push(result);
      } catch (error) {
        results.push({
          message: 'Error when import landing page order',
          error: StringUtils.getString(error),
          data: each,
        });
      }
    }
    return results;
  }

  async createCustomerFromImport(
    customer: {
      no: string;
      name: string;
      phone: string;
      gender: string;
      dateOfBirth: string;
      address: string;
      province: string;
      district: string;
      ward: string;
      tag: string;
      note: string;
    },
    userId: number,
    companyId: number,
    countryId: number,
  ) {
    const connection = getConnection(orderConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    const mapGender: Record<string, any> = {
      Nam: CustomerGender.male,
      Nữ: CustomerGender.female,
      Khác: CustomerGender.others,
    };
    const errors: Array<string> = [];
    try {
      if (!customer.phone) {
        errors.push('Phone number trống');
      }

      if (!customer.name) {
        errors.push('Customer name trống');
      }
      if (customer.gender && !mapGender[customer.gender]) errors.push('Nhập chính xác Gender');
      const check = await queryRunner.manager
        .getRepository(Customer)
        .findOne({ where: { phone: customer.phone, companyId, countryId }, relations: ['tags'] });
      let mCustomer = plainToInstance(Customer, {
        countryId,
        companyId,
        name: customer.name,
        phone: customer.phone,
        gender: mapGender[customer.gender] || CustomerGender.others,
        address: customer.address || '',
      });
      if (check) mCustomer.id = check.id;
      if (customer.dateOfBirth) {
        const dob = moment.tz(customer.dateOfBirth, 'DD/MM/YYYY', 'Asia/Ho_Chi_Minh');
        if (dob.isValid()) mCustomer.dateOfBirth = dob.toDate();
        else errors.push('Date of birth sai định dạng dd/mm/yyyy hoặc phải trước ngày hiện tại');
      }
      const phonePattern = /^\d{8,13}$/;
      if (customer.phone && !phonePattern.test(customer.phone)) {
        errors.push('Phone number sai định dạng');
      }
      if (customer.province) {
        try {
          const location = await this.locationService.getLocationByName(
            customer.province,
            customer.district,
            customer.ward,
            countryId,
          );
          if (location) {
            mCustomer.provinceId = location.provinces_id;
            mCustomer.districtId = location.districts_id;
            mCustomer.wardId = location.wards_id;
            mCustomer.province = customer.province;
            mCustomer.district = customer.district;
            mCustomer.ward = customer.ward;
            if (customer.address) {
              mCustomer.fullAddress = `${customer.address}, ${mCustomer.district}, ${mCustomer.province}`;
            }
          } else {
            errors.push('Address không đúng');
          }
        } catch {
          errors.push('Address không đúng');
        }
      }
      if (errors.length > 0) {
        throw new BadRequestException(errors.join(', '));
      }
      if (customer.tag) {
        if (check) {
          const oldTags = check.tags.map(t => t.name);
          const tagsNew = difference(
            customer.tag.split(',').map(it => it.trim()),
            oldTags,
          );
          let tagsNewInDB = [];
          if (tagsNew.length > 0) {
            tagsNewInDB = await this.tagService.findByNames(tagsNew, companyId, TagType.order);
          }
          const nTags = [];
          tagsNew.forEach(tag => {
            if (tagsNewInDB.findIndex(t => t.name === tag) === -1) {
              nTags.push(
                plainToInstance(Tag, {
                  name: tag,
                  companyId,
                }),
              );
            }
          });
          if (tagsNew.length > 0) {
            const nTagsInsert = await queryRunner.manager.getRepository(Tag).save(nTags);
            mCustomer.tags = [...check.tags, ...nTagsInsert, ...tagsNewInDB];
          } else {
            mCustomer.tags = check.tags;
          }
        } else {
          const tags = customer.tag.split(',').map(it => it.trim());
          const mTags = await this.tagService.findByNames(tags, companyId, TagType.order);
          const nTags = [];
          tags.forEach(tag => {
            if (mTags.findIndex(t => t.name === tag) === -1) {
              nTags.push(
                plainToInstance(Tag, {
                  name: tag,
                  companyId,
                }),
              );
            }
          });
          if (nTags.length > 0) {
            const nTagsInsert = await queryRunner.manager.getRepository(Tag).save(nTags);
            mCustomer.tags = [...mTags, ...nTagsInsert];
          } else {
            mCustomer.tags = mTags;
          }
        }
      }
      mCustomer.creatorId = userId;
      const result = await queryRunner.manager.getRepository(Customer).save(mCustomer);
      if (customer.note) {
        await queryRunner.manager.getRepository(CustomerNotes).save({
          note: customer.note,
          customerId: mCustomer.id,
          creatorId: userId,
        });
      }
      await queryRunner.commitTransaction();
      return result;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async addCustomerTagsByName(
    phone: string,
    reqBody: AddCustomerTagByNameDto,
    request: Record<string, any>,
  ) {
    if (reqBody.tags.length === 0) {
      throw new BadRequestException('Tags is required');
    }
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();
    const customer = await this.customerRepository
      .createQueryBuilder('customer')
      .leftJoinAndSelect('customer.tags', 't')
      .andWhere('customer.phone = :phone AND customer.companyId = :companyId', {
        phone: phone,
        companyId,
      })
      .getOne();
    if (!customer) throw new BadRequestException('Không tìm thấy khách hàng');
    const customerExistTag = customer.tags.map(t => t.name);
    const tagsInDB = await this.tagService.findByNames(reqBody.tags, companyId, TagType.customer);

    const tagsNeedCreatedBeforeLinkToCustomer = difference(
      difference(reqBody.tags, customerExistTag),
      tagsInDB.map(t => t.name),
    ).map(it => {
      return plainToInstance(Tag, {
        name: it,
        companyId,
        type: TagType.customer,
      });
    });
    const tagsReadyToLinkToCustomer = tagsInDB.filter(t => {
      return !customerExistTag.includes(t.name);
    });
    if (tagsNeedCreatedBeforeLinkToCustomer.length > 0) {
      const nTagsInserted = await this.tagRepo.save(tagsNeedCreatedBeforeLinkToCustomer);
      customer.tags = [...customer.tags, ...nTagsInserted];
    }
    if (tagsReadyToLinkToCustomer.length > 0) {
      customer.tags = [...customer.tags, ...tagsReadyToLinkToCustomer];
    }
    await this.customerRepository.save(customer);
    return '';
  }
}
