import { OrderStatus } from 'core/enums/order-status.enum';
import { UpdateOrderDto } from '../dtos/update-order.dto';

// Allowed next status action by user
export const NEXT_STATUS_BY_USER = {
  [OrderStatus.Draft]: [
    OrderStatus.Draft,
    OrderStatus.New,
    OrderStatus.Confirmed,
    OrderStatus.Canceled,
  ],
  [OrderStatus.New]: [
    OrderStatus.New,
    // OrderStatus.Reconfirm,
    OrderStatus.Confirmed,
    OrderStatus.Canceled,
  ],
  [OrderStatus.AwaitingStock]: [
    OrderStatus.AwaitingStock,
    OrderStatus.Reconfirm,
    OrderStatus.Confirmed,
    OrderStatus.Canceled,
  ],
  [OrderStatus.Reconfirm]: [OrderStatus.Reconfirm, OrderStatus.Confirmed, OrderStatus.Canceled],
  [OrderStatus.Confirmed]: [OrderStatus.Confirmed, OrderStatus.Preparing, OrderStatus.Canceled],
  [OrderStatus.Preparing]: [OrderStatus.Preparing, OrderStatus.HandlingOver, OrderStatus.Canceled],
  [OrderStatus.HandlingOver]: [
    OrderStatus.HandlingOver,
    OrderStatus.InTransit,
    OrderStatus.Damaged,
    OrderStatus.Lost,
    OrderStatus.Canceled,
  ],
  [OrderStatus.InTransit]: [OrderStatus.InTransit, OrderStatus.InDelivery, OrderStatus.Lost],
  [OrderStatus.InDelivery]: [
    OrderStatus.InDelivery,
    OrderStatus.Delivered,
    OrderStatus.FailedDelivery,
  ],
  [OrderStatus.Delivered]: [OrderStatus.Delivered, OrderStatus.DeliveredCompleted],
  [OrderStatus.DeliveredCompleted]: [OrderStatus.DeliveredCompleted],
  [OrderStatus.FailedDelivery]: [
    OrderStatus.Delivered,
    OrderStatus.FailedDelivery,
    OrderStatus.AwaitingReturn,
    OrderStatus.InReturn,
    OrderStatus.Lost,
  ],
  [OrderStatus.AwaitingReturn]: [
    OrderStatus.AwaitingReturn,
    OrderStatus.InReturn,
    OrderStatus.Lost,
    OrderStatus.InDelivery,
  ],
  [OrderStatus.InReturn]: [OrderStatus.InReturn, OrderStatus.ReturnedStocked, OrderStatus.Lost],
  [OrderStatus.ReturnedStocked]: [
    OrderStatus.ReturnedStocked,
    OrderStatus.ReturnedCompleted,
    OrderStatus.Damaged,
    OrderStatus.Lost,
  ],
  [OrderStatus.Damaged]: [OrderStatus.Damaged, OrderStatus.DamagedCompleted],
  [OrderStatus.DamagedCompleted]: [OrderStatus.DamagedCompleted],
  [OrderStatus.Lost]: [OrderStatus.Lost, OrderStatus.LostCompleted],
  [OrderStatus.LostCompleted]: [OrderStatus.LostCompleted],
  [OrderStatus.Canceled]: [OrderStatus.Canceled],
};

// Allowed next status action by system
export const NEXT_STATUS_BY_SYSTEM = {
  [OrderStatus.Draft]: [OrderStatus.New, OrderStatus.Canceled],
  [OrderStatus.New]: [OrderStatus.Reconfirm, OrderStatus.Confirmed, OrderStatus.Canceled],
  [OrderStatus.AwaitingStock]: [OrderStatus.Reconfirm, OrderStatus.Confirmed, OrderStatus.Canceled],
  [OrderStatus.Reconfirm]: [OrderStatus.AwaitingStock, OrderStatus.Confirmed, OrderStatus.Canceled],
  [OrderStatus.Confirmed]: [OrderStatus.AwaitingStock, OrderStatus.Preparing, OrderStatus.Canceled],
  [OrderStatus.Preparing]: [OrderStatus.HandlingOver, OrderStatus.Canceled],
  [OrderStatus.HandlingOver]: [OrderStatus.InTransit, OrderStatus.Canceled],
  [OrderStatus.InTransit]: [OrderStatus.InDelivery],
  [OrderStatus.InDelivery]: [OrderStatus.Delivered, OrderStatus.FailedDelivery],
  [OrderStatus.Delivered]: [OrderStatus.DeliveredCompleted],
  [OrderStatus.FailedDelivery]: [OrderStatus.AwaitingReturn, OrderStatus.InReturn],
  [OrderStatus.AwaitingReturn]: [OrderStatus.InReturn],
  [OrderStatus.InReturn]: [OrderStatus.ReturnedStocked],
  [OrderStatus.ReturnedStocked]: [OrderStatus.ReturnedCompleted, OrderStatus.Damaged],
  [OrderStatus.Damaged]: [OrderStatus.DamagedCompleted],
  [OrderStatus.Lost]: [OrderStatus.LostCompleted],
};

export const ORDER_STATUSES_CAN_BE_AUTO_UPDATE_TO_WAIT_TO_PACK = [
  // OrderStatus.New,
  // OrderStatus.WaitingRestock,
  OrderStatus.Confirmed,
];

export const ORDER_STATUSES_CAN_BE_CHANGE_PACKING_TIME = [OrderStatus.Confirmed];

// List of statuses that allow properties of order can be updated
export const CHANGEABLE_STATUSES = [
  OrderStatus.Draft,
  OrderStatus.New,
  OrderStatus.AwaitingStock,
  OrderStatus.Reconfirm,
  OrderStatus.Confirmed,
];

export const UPDATABLE_PROPS: Record<OrderStatus, (keyof UpdateOrderDto)[]> = {
  [OrderStatus.Draft]: [
    'products',
    'saleId',
    'carePageId',
    'marketerId',
    'teamInCharge',
    'customerName',
    'customerPhone',
    'addressText',
    'addressNote',
    'addressWardId',
    'addressDistrictId',
    'addressProvinceId',
    'sourceType',
    'sourceId',
    'discount',
    'discountType',
    'surcharge',
    'shippingFee',
    'paid',
    'postCode',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'status',
    'note',
    'printNoteText',
    'printNoteIds',
    'cancelReasonText',
    'cancelReasonIds',
    'carrier',
    'ignoreDuplicateWarning',
    'type',
  ],
  [OrderStatus.New]: [
    'products',
    'saleId',
    'carePageId',
    'marketerId',
    'teamInCharge',
    'customerName',
    'customerPhone',
    'addressText',
    'addressNote',
    'addressWardId',
    'addressDistrictId',
    'addressProvinceId',
    'sourceType',
    'sourceId',
    'discount',
    'discountType',
    'surcharge',
    'shippingFee',
    'paid',
    'postCode',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'status',
    'note',
    'printNoteText',
    'printNoteIds',
    'cancelReasonText',
    'cancelReasonIds',
    'carrier',
    'ignoreDuplicateWarning',
    'ignoreDuplicateIds',
    'type',
  ],
  [OrderStatus.AwaitingStock]: [
    'products',
    'saleId',
    'carePageId',
    'marketerId',
    'teamInCharge',
    'customerName',
    'customerPhone',
    'addressText',
    'addressNote',
    'addressWardId',
    'addressDistrictId',
    'addressProvinceId',
    'sourceType',
    'sourceId',
    'discount',
    'discountType',
    'surcharge',
    'shippingFee',
    'paid',
    'postCode',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'status',
    'note',
    'printNoteText',
    'printNoteIds',
    'cancelReasonText',
    'cancelReasonIds',
    'carrier',
    'ignoreDuplicateWarning',
    'type',
  ],
  [OrderStatus.Reconfirm]: [
    'saleId',
    'carePageId',
    'marketerId',
    'teamInCharge',
    'customerName',
    'customerPhone',
    'addressText',
    'addressNote',
    'addressWardId',
    'addressDistrictId',
    'addressProvinceId',
    'sourceType',
    'sourceId',
    'postCode',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'status',
    'note',
    'printNoteText',
    'printNoteIds',
    'cancelReasonText',
    'cancelReasonIds',
    'carrier',
    'ignoreDuplicateWarning',
    'type',
  ],
  [OrderStatus.Confirmed]: [
    'saleId',
    'carePageId',
    'marketerId',
    'teamInCharge',
    'customerName',
    'customerPhone',
    'addressText',
    'addressNote',
    'addressWardId',
    'addressDistrictId',
    'addressProvinceId',
    'sourceType',
    'sourceId',
    'postCode',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'status',
    'note',
    'printNoteText',
    'printNoteIds',
    'cancelReasonText',
    'cancelReasonIds',
    'carrier',
    'ignoreDuplicateWarning',
    'type',
  ],
  [OrderStatus.Preparing]: [
    'status',
    'cancelReasonIds',
    'cancelReasonText',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
    'type',
  ],
  [OrderStatus.HandlingOver]: [
    'status',
    'cancelReasonIds',
    'cancelReasonText',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
    'type',
  ],
  [OrderStatus.InTransit]: [
    'status',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
    'cancelReasonText',
    'cancelReasonIds',
    'type',
  ],
  [OrderStatus.InDelivery]: [
    'status',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
    'cancelReasonText',
    'cancelReasonIds',
    'type',
  ],
  [OrderStatus.Delivered]: [
    'status',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
  ],
  [OrderStatus.DeliveredCompleted]: [
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
  ],
  [OrderStatus.FailedDelivery]: [
    'status',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
    'cancelReasonIds',
    'cancelReasonText',
    'type',
  ],
  [OrderStatus.AwaitingReturn]: [
    'status',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
    'type',
  ],
  [OrderStatus.InReturn]: [
    'status',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
    'type',
  ],
  [OrderStatus.ReturnedStocked]: [
    'status',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
  ],
  [OrderStatus.ReturnedCompleted]: [
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
  ],
  [OrderStatus.Damaged]: [
    'status',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
  ],
  [OrderStatus.DamagedCompleted]: [
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
  ],
  [OrderStatus.Lost]: [
    'status',
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
  ],
  [OrderStatus.LostCompleted]: [
    'tagIds',
    'additionalTagIds',
    'removeTagIds',
    'ignoreDuplicateWarning',
  ],
  [OrderStatus.Canceled]: ['tagIds', 'additionalTagIds', 'removeTagIds', 'ignoreDuplicateWarning'],
};

export const AG_TO_FFM_SYNCHRONIZE_PROPS: Record<OrderStatus, (keyof UpdateOrderDto)[]> = {
  [OrderStatus.Draft]: [],
  [OrderStatus.New]: [
    'customerName',
    'customerPhone',
    'addressText',
    'addressNote',
    'addressWardId',
    'addressDistrictId',
    'addressProvinceId',
    'discount',
    'surcharge',
    'shippingFee',
    'paid',
    'products',
    'note',
    'printNoteIds',
    'printNoteText',
    'carrier',
    'tagIds',
    'additionalTagIds',
  ],
  [OrderStatus.AwaitingStock]: [
    'customerName',
    'customerPhone',
    'addressText',
    'addressNote',
    'addressWardId',
    'addressDistrictId',
    'addressProvinceId',
    'discount',
    'surcharge',
    'shippingFee',
    'paid',
    'products',
    'note',
    'printNoteIds',
    'printNoteText',
    'carrier',
    'tagIds',
    'additionalTagIds',
  ],
  [OrderStatus.Reconfirm]: [
    'customerName',
    'customerPhone',
    'addressText',
    'addressNote',
    'addressWardId',
    'addressDistrictId',
    'addressProvinceId',
    'note',
    'printNoteIds',
    'printNoteText',
    'carrier',
    'tagIds',
    'additionalTagIds',
  ],
  [OrderStatus.Confirmed]: [
    'customerName',
    'customerPhone',
    'addressText',
    'addressNote',
    'addressWardId',
    'addressDistrictId',
    'addressProvinceId',
    'note',
    'printNoteIds',
    'printNoteText',
    'carrier',
    'tagIds',
    'additionalTagIds',
  ],
  [OrderStatus.Preparing]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.HandlingOver]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.InTransit]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.InDelivery]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.Delivered]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.DeliveredCompleted]: [],
  [OrderStatus.FailedDelivery]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.AwaitingReturn]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.InReturn]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.ReturnedStocked]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.ReturnedCompleted]: [],
  [OrderStatus.Damaged]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.DamagedCompleted]: [],
  [OrderStatus.Lost]: [
    // 'tagIds',
    // 'additionalTagIds'
  ],
  [OrderStatus.LostCompleted]: [],
  [OrderStatus.Canceled]: [],
};

export const ORDER_STATUSES_CAN_BE_SYNC_TO_PFG = [
  OrderStatus.AwaitingStock,
  OrderStatus.Reconfirm,
  OrderStatus.Confirmed,
];

export const ORDER_STATUSES_CAN_BE_SEND_FACEBOOK_RECEIPT = [
  OrderStatus.AwaitingStock,
  OrderStatus.Confirmed,
];

export const PlainOrderStatus = {
  [OrderStatus.Draft]: 'Đơn nháp',
  [OrderStatus.New]: 'Đơn mới',
  [OrderStatus.AwaitingStock]: 'Chờ hàng',
  [OrderStatus.Reconfirm]: 'Xác nhận lại',
  [OrderStatus.Confirmed]: 'Đã xác nhận',
  [OrderStatus.Preparing]: 'Đang chuẩn bị hàng',
  [OrderStatus.HandlingOver]: 'Đang bàn giao',
  [OrderStatus.InTransit]: 'Đang vận chuyển',
  [OrderStatus.InDelivery]: 'Đang giao hàng',
  [OrderStatus.Delivered]: 'Đã giao (chờ đối soát)',
  [OrderStatus.DeliveredCompleted]: 'Đã giao (Hoàn tất)',
  [OrderStatus.FailedDelivery]: 'Giao thất bại',
  [OrderStatus.AwaitingReturn]: 'Chờ hoàn',
  [OrderStatus.InReturn]: 'Đang hoàn',
  [OrderStatus.ReturnedStocked]: 'Đã hoàn (chờ đối soát)',
  [OrderStatus.ReturnedCompleted]: 'Đã hoàn (Hoàn tất)',
  [OrderStatus.Damaged]: 'Hư hỏng (Chờ xử lý)',
  [OrderStatus.DamagedCompleted]: 'Hư hỏng (Hoàn tất)',
  [OrderStatus.Lost]: 'Thất lạc (Chờ xử lý)',
  [OrderStatus.LostCompleted]: 'Thất lạc (Hoàn tất)',
  [OrderStatus.Canceled]: 'Huỷ',
};

export const ALLOWED_STATUSES_TO_UPDATE_WHEN_USING_FFM = {
  [OrderStatus.AwaitingStock]: [OrderStatus.Canceled],
  [OrderStatus.Reconfirm]: [OrderStatus.Canceled, OrderStatus.Confirmed],
};

export const NOT_SCAN_FOR_POSSIBLE_DUPLICATES_ORDER_STATUSES = [
  OrderStatus.Draft,
  OrderStatus.Delivered,
  OrderStatus.DeliveredCompleted,
  // OrderStatus.AwaitingReturn,
  OrderStatus.InReturn,
  OrderStatus.ReturnedStocked,
  OrderStatus.ReturnedCompleted,
  OrderStatus.Damaged,
  OrderStatus.DamagedCompleted,
  OrderStatus.Lost,
  OrderStatus.LostCompleted,
  OrderStatus.Canceled,
];

export const NOT_SCAN_FOR_POSSIBLE_DUPLICATES_STATUSES = [
  OrderStatus.Delivered,
  OrderStatus.DeliveredCompleted,
  // OrderStatus.AwaitingReturn,
  OrderStatus.InReturn,
  OrderStatus.ReturnedStocked,
  OrderStatus.ReturnedCompleted,
  OrderStatus.Damaged,
  OrderStatus.DamagedCompleted,
  OrderStatus.Lost,
  OrderStatus.LostCompleted,
  OrderStatus.Canceled,
];

export const CONFIRM_ORDER_STATUSES = [
  OrderStatus.Confirmed,
  OrderStatus.Reconfirm,
  OrderStatus.AwaitingStock,
];
