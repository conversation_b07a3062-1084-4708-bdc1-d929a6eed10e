import { CreateOrderDto } from 'apps/order-api/src/dtos/create-order.dto';

export type TNewOrderRequiredProp = keyof CreateOrderDto | Array<keyof CreateOrderDto>;

export type TExportExcelOrderColumnKey =
  | 'index'
  | 'status'
  | 'displayId'
  | 'ffmDisplayId'
  | 'waybillNumber'
  | 'customerName'
  | 'customerPhone'
  | 'addressText'
  | 'addressProvince'
  | 'addressDistrict'
  | 'addressWard'
  | 'postCode'
  | 'productName'
  | 'productSKU'
  | 'variantSKU'
  | 'quantity'
  | 'price'
  | 'shippingFee'
  | 'discount'
  | 'surcharge'
  | 'paid'
  | 'cod'
  | 'revenue'
  | 'tags'
  | 'createdAt'
  | 'confirmedAt'
  | 'inTransitAt'
  | 'note'
  | 'carrierNote'
  | 'sourceId'
  | 'marketerId'
  | 'marketerEmail'
  | 'carePageId'
  | 'carePageEmail'
  | 'saleId'
  | 'saleEmail'
  | 'page'
  | 'pageId'
  | 'conversationCreatedAt'
  | 'statusChangeReason'
  | 'carrier'
  | 'customerEDD'
  | 'originalProject'
  | 'project'
  | 'createNewAt'
  | 'type';

export type TExportExcelOrderColumn = {
  title: string;
  titleEn: string;
  key: TExportExcelOrderColumnKey;
};

export const NEW_ORDER_REQUIRED_PROPS: TNewOrderRequiredProp[] = [
  'products',
  'customerName',
  'customerPhone',
  'addressText',
  'addressDistrictId',
  'addressProvinceId',
  ['saleId', 'carePageId'],
];

export const EXPORT_EXCEL_ORDER_COLUMNS: TExportExcelOrderColumn[] = [
  {
    title: 'STT',
    titleEn: 'STT',
    key: 'index',
  },
  {
    title: 'Trạng thái',
    titleEn: 'Status',
    key: 'status',
  },
  {
    title: 'Dự án',
    titleEn: 'Project',
    key: 'project',
  },
  {
    title: 'Dự án gốc',
    titleEn: 'Original project',
    key: 'originalProject',
  },
  {
    title: 'Mã đơn hàng',
    titleEn: 'Order Code',
    key: 'displayId',
  },
  {
    title: 'SO Code',
    titleEn: 'SO Code',
    key: 'ffmDisplayId',
  },
  {
    title: 'Loại đơn',
    titleEn: 'Order Type',
    key: 'type',
  },
  {
    title: 'Đơn vị vận chuyển',
    titleEn: 'Carrier',
    key: 'carrier',
  },
  {
    title: 'Mã vận đơn',
    titleEn: 'Bill of lading code',
    key: 'waybillNumber',
  },
  {
    title: 'Người nhận',
    titleEn: 'Customer',
    key: 'customerName',
  },
  {
    title: 'Số điện thoại',
    titleEn: 'Phone number',
    key: 'customerPhone',
  },
  {
    title: 'Địa chỉ',
    titleEn: 'Address',
    key: 'addressText',
  },
  {
    title: 'Tỉnh/Thành phố',
    titleEn: 'Province/City',
    key: 'addressProvince',
  },
  {
    title: 'Quận/Huyện',
    titleEn: 'District',
    key: 'addressDistrict',
  },
  {
    title: 'Phường/Xã',
    titleEn: 'Commune/Ward',
    key: 'addressWard',
  },
  {
    title: 'Mã bưu chính',
    titleEn: 'Post code',
    key: 'postCode',
  },
  {
    title: 'Sản phẩm',
    titleEn: 'Product',
    key: 'productName',
  },
  {
    title: 'Mã sản phẩm',
    titleEn: 'Product SKU',
    key: 'productSKU',
  },
  {
    title: 'Mã mẫu mã',
    titleEn: 'Variant SKU',
    key: 'variantSKU',
  },
  {
    title: 'Số lượng',
    titleEn: 'Quantity',
    key: 'quantity',
  },
  {
    title: 'Giá',
    titleEn: 'Unit price',
    key: 'price',
  },
  {
    title: 'Phí VC thu của khách',
    titleEn: 'Shipping fee',
    key: 'shippingFee',
  },
  {
    title: 'Giảm giá',
    titleEn: 'Discount',
    key: 'discount',
  },
  {
    title: 'Phụ thu',
    titleEn: 'Surcharge',
    key: 'surcharge',
  },
  {
    title: 'Khách trả trước',
    titleEn: 'Prepaid customers',
    key: 'paid',
  },
  {
    title: 'Tổng COD',
    titleEn: 'Total COD',
    key: 'cod',
  },
  {
    title: 'Giá trị đơn hàng',
    titleEn: 'Order Revenue',
    key: 'revenue',
  },
  {
    title: 'Thẻ',
    titleEn: 'Tag',
    key: 'tags',
  },
  {
    title: 'Thời điểm tạo đơn',
    titleEn: 'Created At',
    key: 'createdAt',
  },
  {
    title: 'Thời điểm xác nhận',
    titleEn: 'Confirmation time',
    key: 'confirmedAt',
  },
  {
    title: 'Thời điểm bắt đầu vận chuyển',
    titleEn: 'In transit time',
    key: 'inTransitAt',
  },
  {
    title: 'Ngày nhận mong muốn',
    titleEn: 'Customer EDD',
    key: 'customerEDD',
  },
  {
    title: 'Ghi chú cho kho',
    titleEn: 'Warehouse notes',
    key: 'note',
  },
  {
    title: 'Ghi chú in đơn',
    titleEn: 'Waybill note',
    key: 'carrierNote',
  },
  {
    title: 'Nguồn đơn',
    titleEn: 'Order source',
    key: 'sourceId',
  },
  {
    title: 'Marketer',
    titleEn: 'Marketer',
    key: 'marketerId',
  },
  {
    title: 'Marketer (email)',
    titleEn: 'Marketer (email)',
    key: 'marketerEmail',
  },
  {
    title: 'CarePage',
    titleEn: 'CarePage',
    key: 'carePageId',
  },
  {
    title: 'CarePage (email)',
    titleEn: 'CarePage (email)',
    key: 'carePageEmail',
  },
  {
    title: 'Người phụ trách',
    titleEn: 'Person in charge',
    key: 'saleId',
  },
  {
    title: 'Người phụ trách (email)',
    titleEn: 'Person in charge (email)',
    key: 'saleEmail',
  },
  {
    title: 'Page',
    titleEn: 'Page',
    key: 'page',
  },
  {
    title: 'Page Id',
    titleEn: 'Page ID',
    key: 'pageId',
  },
  {
    title: 'Thời điểm tạo hội thoại',
    titleEn: 'Conversation Created At',
    key: 'conversationCreatedAt',
  },
  {
    title: 'Lý do giao',
    titleEn: 'Reason delivery',
    key: 'statusChangeReason',
  },
];

export const EXPORT_EXCEL_ORDER_COLUMNS_DEFAULT: TExportExcelOrderColumn[] = [
  {
    title: 'STT',
    titleEn: 'STT',
    key: 'index',
  },
  {
    title: 'Trạng thái',
    titleEn: 'Status',
    key: 'status',
  },
  {
    title: 'Mã đơn hàng',
    titleEn: 'Order Code',
    key: 'displayId',
  },
  {
    title: 'SO Code',
    titleEn: 'SO Code',
    key: 'ffmDisplayId',
  },
  {
    title: 'Loại đơn',
    titleEn: 'Order Type',
    key: 'type',
  },
  {
    title: 'Mã vận đơn',
    titleEn: 'Bill of lading code',
    key: 'waybillNumber',
  },
  {
    title: 'Người nhận',
    titleEn: 'Customer',
    key: 'customerName',
  },
  {
    title: 'Số điện thoại',
    titleEn: 'Phone number',
    key: 'customerPhone',
  },
  {
    title: 'Địa chỉ',
    titleEn: 'Address',
    key: 'addressText',
  },
  {
    title: 'Tỉnh/Thành phố',
    titleEn: 'Province/City',
    key: 'addressProvince',
  },
  {
    title: 'Quận/Huyện',
    titleEn: 'District',
    key: 'addressDistrict',
  },
  {
    title: 'Phường/Xã',
    titleEn: 'Commune/Ward',
    key: 'addressWard',
  },
  {
    title: 'Mã bưu chính',
    titleEn: 'Post code',
    key: 'postCode',
  },
  {
    title: 'Sản phẩm',
    titleEn: 'Product',
    key: 'productName',
  },
  {
    title: 'Mã sản phẩm',
    titleEn: 'Product SKU',
    key: 'productSKU',
  },
  {
    title: 'Mã mẫu mã',
    titleEn: 'Variant SKU',
    key: 'variantSKU',
  },
  {
    title: 'Số lượng',
    titleEn: 'Quantity',
    key: 'quantity',
  },
  {
    title: 'Giá',
    titleEn: 'Unit price',
    key: 'price',
  },
  {
    title: 'Phí VC thu của khách',
    titleEn: 'Shipping fee',
    key: 'shippingFee',
  },
  {
    title: 'Giảm giá',
    titleEn: 'Discount',
    key: 'discount',
  },
  {
    title: 'Phụ thu',
    titleEn: 'Surcharge',
    key: 'surcharge',
  },
  {
    title: 'Khách trả trước',
    titleEn: 'Prepaid customers',
    key: 'paid',
  },
  {
    title: 'Tổng COD',
    titleEn: 'Total COD',
    key: 'cod',
  },
  {
    title: 'Giá trị đơn hàng',
    titleEn: 'Order Revenue',
    key: 'revenue',
  },
  {
    title: 'Thẻ',
    titleEn: 'Tag',
    key: 'tags',
  },
  {
    title: 'Thời điểm tạo đơn',
    titleEn: 'Created At',
    key: 'createdAt',
  },
  {
    title: 'Thời điểm xác nhận',
    titleEn: 'Confirmation time',
    key: 'confirmedAt',
  },
  {
    title: 'Thời điểm bắt đầu vận chuyển',
    titleEn: 'In transit time',
    key: 'inTransitAt',
  },
  {
    title: 'Ghi chú cho kho',
    titleEn: 'Warehouse notes',
    key: 'note',
  },
  {
    title: 'Ghi chú in đơn',
    titleEn: 'Waybill note',
    key: 'carrierNote',
  },
  {
    title: 'Nguồn đơn',
    titleEn: 'Order source',
    key: 'sourceId',
  },
  {
    title: 'Marketer',
    titleEn: 'Marketer',
    key: 'marketerId',
  },
  {
    title: 'Marketer (email)',
    titleEn: 'Marketer (email)',
    key: 'marketerEmail',
  },
  {
    title: 'CarePage',
    titleEn: 'CarePage',
    key: 'carePageId',
  },
  {
    title: 'CarePage (email)',
    titleEn: 'CarePage (email)',
    key: 'carePageEmail',
  },
  {
    title: 'Người phụ trách',
    titleEn: 'Person in charge',
    key: 'saleId',
  },
  {
    title: 'Người phụ trách (email)',
    titleEn: 'Person in charge (email)',
    key: 'saleEmail',
  },
  {
    title: 'Page',
    titleEn: 'Page',
    key: 'page',
  },
  {
    title: 'Page Id',
    titleEn: 'Page ID',
    key: 'pageId',
  },
  {
    title: 'Thời điểm tạo hội thoại',
    titleEn: 'Conversation Created At',
    key: 'conversationCreatedAt',
  },
  {
    title: 'Lý do giao',
    titleEn: 'Reason delivery',
    key: 'statusChangeReason',
  },
  {
    title: 'Thời điểm tạo mới',
    titleEn: 'Create new time',
    key: 'createNewAt',
  },
];

export const EXPORT_EXCEL_COLUMN_KEYS: TExportExcelOrderColumnKey[] = EXPORT_EXCEL_ORDER_COLUMNS.map(
  col => col.key,
);
