import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber } from 'class-validator';

export class AddCallGroupExtensionDto {
  @ApiProperty({
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  numberExtension: number;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsNotEmpty()
  @IsNumber()
  addByUserId: number;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsNotEmpty()
  @IsNumber()
  type: number;
}
