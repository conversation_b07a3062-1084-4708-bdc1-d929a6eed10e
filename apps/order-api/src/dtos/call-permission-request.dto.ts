import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { CallPermissonRequestType } from '../enums/call-permisson-request.enum';
import { Transform, Type } from 'class-transformer';

export class CallPermissionRequestDto {
  @ApiProperty({ required: true })
  @IsNumber()
  @IsOptional()
  callCenterId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  @Transform(({ value }) =>
    Array.isArray(value) ? value.map(Number) : [Number(value)],
  )
  leadIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString({ each: true })
  @Type(() => String)
  @Transform(({ value }) =>
    Array.isArray(value) ? value.map(String) : [String(value)],
  )
  phoneNumber?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(CallPermissonRequestType)
  type?: CallPermissonRequestType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  @Transform(({ value }) =>
    Array.isArray(value) ? value.map(Number) : [Number(value)],
  )
  userIds?: number[];
}
