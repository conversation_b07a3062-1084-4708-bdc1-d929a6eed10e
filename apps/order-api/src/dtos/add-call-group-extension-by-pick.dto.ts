import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';

export class AddCallGroupExtensionByPickDto {
  @ApiProperty({
    type: [Number],
    required: true,
  })
  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  extensionIds: number[];

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsNotEmpty()
  @IsNumber()
  addByUserId: number;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsNotEmpty()
  @IsNumber()
  type: number;
}
