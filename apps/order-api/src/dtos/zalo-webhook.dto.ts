import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { RequestType, UserConsentType, ZaloEventName } from '../enums/zalo-webhook.enum';

export class FollowerDto {
  @ApiProperty()
  @IsString()
  id: string;
}

export class ZaloWebhookDto {
  @ApiProperty()
  @IsString()
  @Transform(({ obj }) => obj['oa_id'])
  oaId: string;

  @ApiProperty()
  @IsString()
  @Transform(({ obj }) => obj['app_id'])
  appId: string;

  @ApiProperty()
  @IsString()
  @IsEnum(ZaloEventName)
  eventName: ZaloEventName;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @IsEnum(RequestType)
  @Transform(({ obj }) => obj['request_type'])
  requestType?: RequestType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Transform(({ obj }) => obj['create_time'])
  createTime?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Transform(({ obj }) => obj['expired_time'])
  expiredTime?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Transform(({ obj }) => obj['confirmed_time'])
  confirmedTime?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(UserConsentType)
  @Transform(({ obj }) => obj['user_consent'])
  userConsent?: UserConsentType;

  @ApiProperty({ required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => FollowerDto)
  follower?: FollowerDto;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  timestamp: number;
}
