import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { CommonStatus } from 'core/enums/common-status.enum';
import { OrderSourceType } from 'core/enums/order-source-type.enum';
import { OrderType } from '../enums/order-type.enum';

export class CreateLandingPageDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  link?: string;

  @ApiProperty()
  @IsNumber()
  countryId: number;

  @ApiProperty()
  @IsNumber()
  projectId: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  productId?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  userId?: number;

  @ApiProperty({ required: false, enum: CommonStatus })
  @IsOptional()
  @IsEnum(CommonStatus)
  @EnumTransform(CommonStatus)
  status: CommonStatus;

  @ApiProperty({ required: false, enum: OrderSourceType })
  @IsOptional()
  @IsEnum(OrderSourceType)
  @EnumTransform(OrderSourceType)
  sourceType?: OrderSourceType;

  @ApiProperty({ required: false, enum: OrderType })
  @IsOptional()
  @IsEnum(OrderType)
  @EnumTransform(OrderType)
  orderType?: OrderType;
}

export class UpdateLandingPageDto extends OmitType(PartialType(CreateLandingPageDto), [
  'countryId',
  'projectId',
]) {}
