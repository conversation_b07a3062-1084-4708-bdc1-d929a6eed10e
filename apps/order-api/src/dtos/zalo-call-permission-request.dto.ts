import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';
import { CallPermissonRequestType } from '../enums/call-permisson-request.enum';

export class ZaloCallPermissionRequest {
  @ApiProperty({ required: true })
  @IsNumber()
  callCenterId?: number;

  @ApiProperty({ required: true })
  @IsNumber()
  leadId?: number;

  @ApiProperty({ required: true })
  @IsString()
  phoneNumber: string;

  @ApiProperty({ required: true })
  @IsNumber()
  type?: CallPermissonRequestType;

  @ApiProperty({ required: true })
  @IsNumber()
  userId: number;
}
