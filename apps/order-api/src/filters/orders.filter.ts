import { ArgsType, Field, Int } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsIn, IsNotEmpty, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { OrderStatus } from 'core/enums/order-status.enum';
import { Sort } from 'core/enums/sort.enum';
import { CareState } from '../enums/care-state.enum';
import { SourceEntity } from '../enums/source-entity.enum';
import { TeamInCharge } from '../enums/team-in-charge.enum';

const DATE_RANGE_TYPES = ['creationTime', 'lastUpdateStatusTime', 'lastUpdateTime'];

export enum TagMethodType {
  Include = 'Include',
  Exclude = 'Exclude',
}

export enum TagOperatorType {
  And = 1,
  Or = 2,
}

@ArgsType()
export class OrdersFilter {
  @ApiProperty({ required: false, enum: OrderStatus, isArray: true })
  @IsOptional()
  @EnumTransform(OrderStatus)
  @ArrayTransform()
  @Field(() => [OrderStatus], { nullable: true })
  status?: OrderStatus[];

  @ApiProperty({ required: false, enum: OrderStatus, isArray: true })
  @IsOptional()
  @EnumTransform(OrderStatus)
  @ArrayTransform()
  @Field(() => [OrderStatus], { nullable: true })
  excludesStatus?: OrderStatus[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Field(() => [Int], { nullable: true })
  ids?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Field(() => [String], { nullable: true })
  displayIds?: string[];

  @ApiProperty({
    required: false,
    description: 'Kiểu lọc thời gian',
    enum: DATE_RANGE_TYPES,
    default: 'creationTime',
  })
  @IsIn(DATE_RANGE_TYPES)
  dateRangeType?: typeof DATE_RANGE_TYPES[number] = 'creationTime';

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  @Field(() => Int, { nullable: true })
  from?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  @Field(() => Int, { nullable: true })
  to?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  @Field(() => Int, { nullable: true })
  confirmedFrom?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  @Field(() => Int, { nullable: true })
  confirmedTo?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  @Field(() => Int, { nullable: true })
  lastUpdateStatusFrom?: Date;

  @ApiProperty({ required: false, type: 'number' })
  @IsOptional()
  @DateTransform()
  @Field(() => Int, { nullable: true })
  lastUpdateStatusTo?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @Field(() => String, { nullable: true })
  query?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  @Field(() => [Int], { nullable: true })
  saleIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  @Field(() => [Int], { nullable: true })
  carePageIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  @Field(() => [Int], { nullable: true })
  userInChargeIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  @Field(() => [Int], { nullable: true })
  marketerIds?: number[];

  @ApiProperty({ required: false, enum: SourceEntity })
  @IsOptional()
  @EnumTransform(SourceEntity)
  @Field(() => SourceEntity, { nullable: true })
  sourceType?: SourceEntity;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Field(() => [Int], { nullable: true })
  sourceIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  @Field(() => [Int], { nullable: true })
  tagIds?: number[];

  @ApiProperty({
    required: false,
    type: 'enum',
    enum: TagMethodType,
  })
  @IsOptional()
  @IsEnum(TagMethodType)
  @EnumTransform(TagMethodType)
  tagMethod?: TagMethodType;

  @ApiProperty({
    required: false,
    type: 'enum',
    enum: TagOperatorType,
  })
  @IsOptional()
  @IsEnum(TagOperatorType)
  @EnumTransform(TagOperatorType)
  tagOperator?: TagOperatorType;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Field(() => [Int], { nullable: true })
  productIds?: number[];

  @ApiProperty({ required: false })
  @Type(() => Boolean)
  @Field(() => Boolean, { nullable: true })
  isCheckedAfterReturn?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  @Field(() => [Int], { nullable: true })
  countryIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Type(() => Number)
  @Field(() => [Int], { nullable: true })
  projectIds?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  orderBy?: string;

  @ApiProperty({ required: false, enum: Sort })
  @IsOptional()
  @EnumTransform(Sort)
  sort?: Sort;

  @ApiProperty({ required: false, enum: TeamInCharge, isArray: true })
  @IsOptional()
  @EnumTransform(TeamInCharge)
  @ArrayTransform()
  @Field(() => [TeamInCharge], { nullable: true })
  teamsInCharge?: TeamInCharge[];

  @ApiProperty({ required: false })
  @IsOptional()
  @Field(() => Int, { nullable: true })
  fbId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  @Field(() => [String], { nullable: true })
  pageIds?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  scopedUserId?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  exceptDraft?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  hasPossibleDuplicates?: boolean;

  @ApiProperty({ required: false, enum: CareState, isArray: true })
  @IsOptional()
  @EnumTransform(CareState)
  @ArrayTransform()
  @Field(() => [CareState], { nullable: true })
  leadState?: CareState[];

  @ApiProperty({ required: false, enum: CareState, isArray: true })
  @IsOptional()
  @EnumTransform(CareState)
  @ArrayTransform()
  @Field(() => [CareState], { nullable: true })
  excludesLeadState?: CareState[];

  @ApiProperty({ required: false })
  @IsOptional()
  customerPhone?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @EnumTransform(OrderStatus)
  @ArrayTransform()
  passedStatuses?: OrderStatus[];

  companyId?: number;

  _distributableLeads?: boolean;

  @ApiProperty({ required: false, enum: OrderStatus })
  @IsOptional()
  @EnumTransform(OrderStatus)
  @Field(() => OrderStatus, { nullable: true })
  selectedTabStatus?: OrderStatus;
  _byPassSeftData?: boolean;
  _byPassCheckReps?: boolean;

  @IsOptional()
  @ApiProperty({ required: false })
  @BooleanTransform()
  isCountCancelDueToCustomer?: boolean;

  @IsOptional()
  @ApiProperty({ required: false })
  @Type(() => Number)
  type?: number;
}

export class OrdersQuery {
  @ApiProperty({ isArray: true })
  @ArrayTransform()
  @IsNotEmpty()
  @Type(() => Number)
  orderIds?: number[];
}

export class CountOrdersFilter extends OrdersFilter {
  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  getRevenue?: boolean;

  @IsOptional()
  @ApiProperty({ required: false })
  @BooleanTransform()
  countAllOrderOfCustomer?: boolean;
}
