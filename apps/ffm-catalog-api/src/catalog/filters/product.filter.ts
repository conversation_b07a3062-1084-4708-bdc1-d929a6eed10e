import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { ArrayTransform } from 'core/decorators/array-transform.decorator';
import { BooleanTransform } from 'core/decorators/boolean-transform/boolean-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { ProductStatus } from '../enum/product-status.enum';
import { ProductAdditional } from '../../enums/product-additional.enum';
import { SortType, TypeSortProduct } from '../enum/product-sort.enum';
export class FilterProduct {
  @ApiProperty({ required: false, enum: ProductStatus, isArray: true })
  @IsOptional()
  @EnumTransform(ProductStatus)
  @ArrayTransform()
  status?: ProductStatus[];

  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  @ArrayTransform()
  variantIds: number[];

  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  @ArrayTransform()
  ids: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  clientIds: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  productIds: string;

  @ApiProperty({ required: false })
  @IsOptional()
  getAll: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  allStatus: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isCombo?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @BooleanTransform()
  isNormal?: boolean;

  @IsOptional()
  type?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  cateIds?: number[];

  @IsOptional()
  countryId?: number;

  @IsOptional()
  name?: string;

  @IsOptional()
  query?: string;

  @IsOptional()
  @ArrayTransform()
  names: string[];

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  from?: Date;

  @IsOptional()
  @ApiProperty({ required: false, type: 'number' })
  @DateTransform()
  to?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @ArrayTransform()
  warehouseIds: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  clientId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  warehouseId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  search: boolean;

  @ApiProperty({ required: false, enum: ProductAdditional, isArray: true })
  @IsEnum(ProductAdditional, { each: true })
  @IsOptional()
  @ArrayTransform()
  @EnumTransform(ProductAdditional)
  additional?: ProductAdditional[];

  @ApiProperty({ required: false, enum: TypeSortProduct })
  @IsOptional()
  @EnumTransform(TypeSortProduct)
  sortBy?: TypeSortProduct;

  @ApiProperty({ required: false, enum: SortType })
  @IsOptional()
  @EnumTransform(SortType)
  sortType: SortType;

  @ApiProperty({ required: false })
  @IsOptional()
  image: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isDifferenceMarket?: boolean;
}

export class FilterVariant {
  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  @ArrayTransform()
  clientIds: number[];

  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  @ArrayTransform()
  ids: number[];

  @ApiProperty({ required: false, isArray: true })
  @IsOptional()
  @ArrayTransform()
  names?: string[];

  @IsOptional()
  name?: string;

  @ApiProperty({ required: false, default: true })
  @IsOptional()
  extraData?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  // @IsBoolean()
  isDifferenceMarket?: boolean;

  @IsOptional()
  countryId?: number;

  @ApiProperty({ required: false, default: true })
  @IsOptional()
  excludedCombo?: boolean;
}
