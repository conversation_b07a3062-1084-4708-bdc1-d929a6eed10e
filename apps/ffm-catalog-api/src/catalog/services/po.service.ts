/* eslint-disable @typescript-eslint/no-floating-promises */
import { AmqpConnection, Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { catalogConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import ExcelUtils from 'core/utils/ExcelUtils';
import HistoryLogUtils from 'core/utils/HistoryLogUtils';
import { find, isArray, isEmpty, isNil, pick, reduce, sumBy, uniq } from 'lodash';
import { ILike, In, Repository } from 'typeorm';
import { PoDto, PoImportDto } from '../../dtos/po.dto';
import { Logs } from '../../entities/logs.entity';
import { ProductVariation } from '../../entities/product-variation.entity';
import { PurchaseOrderItem } from '../../entities/purchase-order-item.entity';
import { PurchaseOrder } from '../../entities/purchase-order.entity';
import { SlotWarehouses } from '../../entities/warehouses';
import {
  NEXT_PO_STATUS,
  PO_SAVE_ONLY_STATUS,
  PO_STATUS_CLIENT,
  PO_STATUS_CLIENT_CURRENT,
  PO_STATUS_EDIT,
  PO_STATUS_SEARCH,
  PO_STATUS_STOCK_PRODUCT,
} from '../constants/po-statuses.constant';
import { PoStatus, TypeFilter } from '../enum/po-status.enum';
import { WarehouseType } from '../enum/warehouse-type.enum';
import { FilterPo } from '../filters/filterPo.filter';
import xlsx, { WorkSheet } from 'node-xlsx';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { Users } from '../../read-entities/identity-entities/Users';
import * as moment from 'moment-timezone';
import { $enum } from 'ts-enum-util';
import { UserType } from 'core/enums/user-type.enum';
import { StockInventoryService } from './stock-inventory.service';
import * as ExcelJS from 'exceljs';
import { saveImageToExcel } from 'core/utils/ImageUtils';

const mapCol = {
  uniq: 'STT',
  clientCode: 'Client Code',
  warehouseCode: 'Warehouse Code',
  name: 'Name',
  contactPhone: 'Contact Phone',
  boxes: 'Boxes|Pallet',
  note: 'Note',
  sku: 'Variant SKU',
  estimate: 'Estimate',
  good: 'Good',
  damaged: 'Damaged',
  prefix: 'Variant Prefix',
};
@Injectable()
export class PoService {
  constructor(
    @InjectRepository(SlotWarehouses, catalogConnection)
    private whRepository: Repository<SlotWarehouses>,
    @InjectRepository(PurchaseOrder, catalogConnection)
    private poRepository: Repository<PurchaseOrder>,
    @InjectRepository(PurchaseOrderItem, catalogConnection)
    private poiRepository: Repository<PurchaseOrderItem>,
    @InjectRepository(Logs, catalogConnection)
    private logRepository: Repository<Logs>,
    @InjectRepository(ProductVariation, catalogConnection)
    private variantRepository: Repository<ProductVariation>,
    private readonly amqpConnection: AmqpConnection,
    private inventoryService: StockInventoryService,
  ) {}

  async countPo(pagination: PaginationOptions, query: FilterPo, request, header): Promise<any> {
    const mQuery = await this.queryPo(pagination, query, header, request);
    mQuery.leftJoin('po.items', 'items');
    mQuery.select('po.status as status');
    mQuery.addSelect('po.id as id');
    mQuery.addGroupBy('po.id');

    const selectSql = [],
      statusParse = {};

    Object.entries(PoStatus).forEach(([key, value]) => {
      if (Number(key) >= PoStatus.new) {
        selectSql.push(`SUM(CASE WHEN ${key} = countPo.status THEN 1 ELSE 0 END) as ${value} `);
        statusParse[value.toString().toLowerCase()] = value;
      }
    });

    const data = await this.poRepository.query(
      `SELECT ${selectSql.join(',')} FROM (${mQuery.getQueryAndParameters()[0]}) as countPo;`,
      mQuery.getQueryAndParameters()[1],
    );
    const result = {};
    if (!!data[0])
      for (const [key, value] of Object.entries(data[0])) {
        result[statusParse[key]] = value;
      }

    return result;
  }

  async findAll(
    pagination: PaginationOptions,
    query: FilterPo,
    request,
    header,
  ): Promise<[PurchaseOrder[], number]> {
    const mQuery = await this.queryPo(pagination, query, header, request);
    mQuery.leftJoinAndSelect('po.items', 'items');
    mQuery.leftJoinAndSelect('items.variant', 'variant');
    mQuery.leftJoinAndSelect('variant.product', 'product');
    // console.log("🚬 ~ PoService ~ mQuery:", mQuery?.getQueryAndParameters());
    return mQuery.getManyAndCount();
  }

  async searchPo(
    pagination: PaginationOptions,
    query: FilterPo,
    request,
    header,
  ): Promise<[PurchaseOrder[], number]> {
    const { name } = query;
    let { warehouses } = request?.user;
    const { type, companyId, isAdmin } = request?.user;
    warehouses = !!warehouses ? warehouses : [];
    const data = await this.poRepository.findAndCount({
      take: pagination?.limit,
      skip: pagination?.skip,
      where: qb => {
        if (name)
          qb.andWhere([
            {
              code: ILike(`%${name}%`),
            },
          ]);
        qb.andWhere({
          status: In(PO_STATUS_SEARCH),
        });

        if (companyId)
          qb.andWhere({
            bizId: companyId,
          });

        if (!!header['country-ids']) {
          qb.andWhere('PurchaseOrder__warehouse.countryCode = :countryCode', {
            countryCode: header['country-ids'],
          });
        }

        if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
          qb.andWhere({
            clientId: request?.user?.id,
          });
          qb.andWhere('warehouse.type = :typeWarehouse', { typeWarehouse: WarehouseType.main });
        } else if (type == $enum(UserType).getKeyOrDefault(UserType.employee, null) && !isAdmin) {
          qb.andWhere({
            warehouseId: In(warehouses),
          });
        }
      },
      order: {
        createdAt: 'DESC',
      },
      relations: ['warehouse', 'items', 'items.variant', 'items.variant.product'],
    });
    return data;
  }

  async importExcel(request: Record<string, any>, buffer: Buffer, po: PoImportDto) {
    const data = ExcelUtils.read(buffer, 0, mapCol.uniq);

    for (const item of data) {
      if (
        !!item?.[mapCol.clientCode] &&
        !!item?.[mapCol.warehouseCode] &&
        !!item?.[mapCol.name] &&
        !!item?.[mapCol.contactPhone] &&
        !!item?.[mapCol.sku]
      ) {
        await this.amqpConnection.publish('ffm-catalog-service', 'ffm-order-import-po', {
          purchase: item,
          user: request?.user,
          status: po?.status,
        });
      }
    }
    return {
      code: 200,
    };
  }

  @RabbitRPC({
    exchange: 'ffm-catalog-service',
    routingKey: 'ffm-order-import-po',
    queue: 'queue-ffm-order-import-po',
    errorHandler: rmqErrorsHandler,
  })
  async importPo(payload) {
    console.log('payload', 'queue-ffm-order-import-po', payload);
    const { user, purchase, status } = payload;
    const { companyId, id } = user;

    let client: Users;
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-users',
        routingKey: 'find-user-by-code',
        payload: { code: purchase?.[mapCol.clientCode] },
        timeout: 20000,
      });
      // console.log(data);
      client = data as Users;
    } catch (error) {
      console.log(error);
      return new Nack();
    }

    const sku = isArray(purchase?.[mapCol.sku]) ? purchase?.[mapCol.sku] : [purchase?.[mapCol.sku]];
    // const prefix = isArray(purchase?.[mapCol.prefix]) ? purchase?.[mapCol.prefix] : [purchase?.[mapCol.prefix]];
    const estimate = isArray(purchase?.[mapCol.estimate])
      ? purchase?.[mapCol.estimate]
      : [purchase?.[mapCol.estimate]];
    const good = isArray(purchase?.[mapCol.good])
      ? purchase?.[mapCol.good]
      : [purchase?.[mapCol.good]];
    const damaged = isArray(purchase?.[mapCol.damaged])
      ? purchase?.[mapCol.damaged]
      : [purchase?.[mapCol.damaged]];

    const sql = this.variantRepository
      .createQueryBuilder('var')
      .leftJoinAndSelect('var.product', 'product')
      .andWhere('product.clientId = :clientId', { clientId: client?.id })
      .andWhere('var.sku in (:...sku)', {
        sku,
      });

    // let condition = [];
    // for (const key in sku) {
    //   condition.push({
    //     sku: sku[key],
    //     prefix: prefix[key]
    //   })
    // }

    // if(condition?.length > 0) sql.andWhere(condition);

    const [wh, products] = await Promise.all([
      this.whRepository.findOne({
        where: {
          displayId: purchase?.[mapCol.warehouseCode],
          bizId: user?.companyId,
        },
      }),
      sql.getMany(),
    ]);

    if (!client || !wh || products?.length != sku?.length) {
      console.log('Data invalid');
      return new Nack();
    }

    const lookupProduct = [];
    products.forEach((prod: ProductVariation) => {
      lookupProduct[prod?.originSku] = prod?.id;
    });

    // const increment = await this.poiRepository.query(
    //   `select nextval('po_seq') as id`,
    // );
    const po = new PurchaseOrder();

    po.note = !!purchase?.[mapCol.note] ? purchase?.[mapCol.note] : '';
    po.contactName = !!purchase?.[mapCol.name] ? purchase?.[mapCol.name] : '';
    po.contactPhone = !!purchase?.[mapCol.contactPhone] ? purchase?.[mapCol.contactPhone] : '';
    po.clientId = client?.id;
    po.warehouseId = Number(wh?.id);
    po.estimateDate = null;
    po.quantity = 0;
    po.boxes = !!purchase?.[mapCol.boxes] ? purchase?.[mapCol.boxes] : 0;
    po.status = status;
    po.bizId = companyId;
    po.lastEditorId = id;
    po.creatorId = id;

    const params = [];
    for (const key in sku) {
      const poi = new PurchaseOrderItem();

      poi.creatorId = id;
      poi.lastEditorId = id;
      poi.po = po;
      poi.variantId = lookupProduct[sku[key]];
      poi.estimate = estimate[key] ?? 0;
      poi.reality = Number(good[key] ?? 0) + Number(damaged[key] ?? 0);
      poi.good = good[key] ?? 0;
      poi.damaged = damaged[key] ?? 0;

      params.push(poi);
    }

    const res = await this.poiRepository.save(params).catch(err => {
      if (err?.driverError) console.log(err?.driverError?.detail ?? err);
      return new Nack();
    });

    // console.log(res);

    if (!!status && PoStatus.success == status && !!po?.id)
      await this.amqpConnection.publish('stock-inventory-service', 'purchase-order-stock', {
        id: po?.id,
        user,
      });

    return new Nack();
  }

  async createPo(request, data: PoDto): Promise<PurchaseOrder> {
    const { warehouses, type, isAdmin } = request?.user;
    const uid = request?.user?.id;
    console.log(request?.user);

    if (
      (!!data?.warehouseId &&
        !warehouses.includes(data?.warehouseId) &&
        type == $enum(UserType).getKeyOrDefault(UserType.employee, null) &&
        !isAdmin) ||
      !data?.warehouseId
    )
      throw new BadRequestException('Not found warehouse');

    await this.poiRepository.query(`CREATE SEQUENCE IF NOT EXISTS po_seq;`);
    // const increment = await this.poiRepository.query(
    //   `select nextval('po_seq') as id`,
    // );

    let po = new PurchaseOrder();
    po = {
      ...po,
      ...data,
      estimateDate: !!data?.estimateDate
        ? new Date(moment(data?.estimateDate).format('MM/DD/YYYY'))
        : null,
      quantity: 0,
      boxes: !!data?.boxes ? data?.boxes : 0,
      // code: DisplayUtils.generate('', increment[0]?.id ?? 1),
      status: PoStatus.new,
    } as PurchaseOrder;

    po.creatorId = uid;
    po.lastEditorId = uid;
    po.bizId = request?.user?.companyId;

    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      const wh = await this.whRepository.findOne({
        where: qb => {
          qb.where({
            id: data?.warehouseId,
            type: WarehouseType.main,
          });
        },
      });
      if (!wh?.id) throw new BadRequestException('Warehouse invalid');
      po.warehouseId = Number(wh?.id);
      po.clientId = uid;
    }

    const params = [];
    data?.variations.forEach(e => {
      if (e?.variantId) {
        let poi = new PurchaseOrderItem();
        poi = {
          ...poi,
          ...e,
          good: e?.newestGood,
          damaged: e?.newestDamaged,
        } as PurchaseOrderItem;

        poi.po = po;
        poi.creatorId = uid;
        poi.lastEditorId = uid;
        params.push(poi);
        if (e?.estimate <= 0)
          throw new BadRequestException({ code: 'IP_0010', variantId: e?.variantId });
      }
    });

    await this.poiRepository.save(params);
    return po;
  }

  async updatePoV1(data: PoDto, id, request): Promise<PurchaseOrder> {
    const { warehouses, type, isAdmin } = request?.user;

    if (
      (!!data?.warehouseId &&
        !warehouses.includes(data?.warehouseId) &&
        type == $enum(UserType).getKeyOrDefault(UserType.employee, null) &&
        !isAdmin) ||
      !data?.warehouseId
    )
      throw new BadRequestException('Not found warehouse');

    let po = await this.poRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
        }),
          qb.andWhere({
            bizId: request?.user?.companyId,
          });
      },
      relations: ['items'],
    });
    const oldStatus = po?.status;

    // Them thoi gian update trang thai
    if (data?.status && oldStatus != data?.status) {
      po.lastUpdateStatus = new Date();
    }
    // Chi cap nhat trang thai
    if (PO_SAVE_ONLY_STATUS.includes(data?.status)) {
      po.status = data?.status;
      await this.poRepository.save(po);
      return po;
    }

    // Chuyen trang thai check hop le
    if (
      !!data?.status &&
      data?.status != oldStatus &&
      (!NEXT_PO_STATUS[po?.status] ||
        (!!NEXT_PO_STATUS[po?.status] && !NEXT_PO_STATUS[po?.status].includes(data?.status)))
    )
      throw new BadRequestException('Status invalid');
    if (
      type == $enum(UserType).getKeyOrDefault(UserType.customer, null) &&
      (!PO_STATUS_CLIENT.includes(data?.status) || !PO_STATUS_CLIENT_CURRENT.includes(oldStatus))
    )
      throw new BadRequestException('Status invalid. Client view');

    const poItemLookup: Record<string, PurchaseOrderItem> = reduce(
      po?.items,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    // if(data?.status) po.status = data?.status;

    // Trang thai sang nhap kho hoan tat
    if (PO_STATUS_STOCK_PRODUCT.includes(data?.status) && oldStatus != PoStatus.delivery) {
      let valid = true;

      data?.variations.forEach(e => {
        const reality =
          Number(e?.newestGood) +
          Number(poItemLookup[e?.id]?.good ?? 0) +
          Number(e?.newestDamaged) +
          Number(poItemLookup[e?.id]?.damaged ?? 0);
        if (reality < Number(e?.estimate) || reality <= 0) valid = false;
      });

      // let newStatus = (valid) ? PoStatus.success : PoStatus.reStock;
      // fix bug UAT bo tu dong chuyen ve reStock
      const newStatus = valid
        ? PoStatus.success
        : data?.status == PoStatus.success
        ? PoStatus.success
        : PoStatus.reStock;
      po.status = newStatus;
    } else if (data?.status) {
      po.status = data?.status;
    }

    // Trang thai duoc cap nhat thong tin phieu
    if (PO_STATUS_EDIT.includes(data?.status)) {
      po = {
        ...po,
        ...data,
        estimateDate: !!data?.estimateDate
          ? new Date(moment(data?.estimateDate).format('MM/DD/YYYY'))
          : null,
        quantity: 0,
        boxes: !!data?.boxes ? data?.boxes : 0,
      } as PurchaseOrder;

      if (!warehouses || (!isAdmin && !warehouses.includes(data?.warehouseId)))
        delete po.warehouseId;
    }

    if (data?.packageImages) po.packageImages = data?.packageImages;
    if (data?.defectiveImages) po.defectiveImages = data?.defectiveImages;

    po.lastEditorId = request?.user?.id;
    delete po.createdAt;
    delete po.updatedAt;

    const params = [];
    const ids = [];

    // San pham trong phieu
    data?.variations.forEach(e => {
      if (e?.variantId) {
        let poi = new PurchaseOrderItem();
        poi = {
          ...poi,
          ...e,
          good: Number(e?.newestGood) + Number(poItemLookup[e?.id]?.good ?? 0),
          damaged: Number(e?.newestDamaged) + Number(poItemLookup[e?.id]?.damaged ?? 0),
        } as PurchaseOrderItem;

        poi.po = po;

        if (!e?.id) {
          poi.creatorId = request?.user?.id;
          poi.lastEditorId = request?.user?.id;
        } else {
          const item = find(po?.items, function(o) {
            return o.id == e?.id;
          });
          if (
            !!item &&
            (item?.estimate != e?.estimate ||
              item?.good != Number(e?.newestGood) + Number(poItemLookup[e?.id]?.good ?? 0) ||
              item?.damaged != Number(e?.newestDamaged) + Number(poItemLookup[e?.id]?.damaged ?? 0))
          )
            poi.lastEditorId = request?.user?.id;
          ids.push(e?.id);
        }

        delete poi.createdAt;
        delete poi.updatedAt;
        params.push(poi);
      }
    });

    // Bo san pham
    if (params?.length > 0 && PO_STATUS_EDIT.includes(data?.status)) {
      const poiSQL = this.poiRepository
        .createQueryBuilder()
        .delete()
        .from(PurchaseOrderItem)
        .where('po_id = :id', { id });

      if (ids?.length > 0)
        poiSQL.andWhere('id not in (:...ids)', {
          ids,
        });
      await poiSQL.execute();
    }

    await this.poiRepository.save(params).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });

    // Cong ton kho
    if (PO_STATUS_STOCK_PRODUCT.includes(data?.status)) {
      console.log('purchase-order-stock', data?.status);
      await this.amqpConnection.publish('stock-inventory-service', 'purchase-order-stock', {
        id,
        user: request?.user,
      });
    }

    return po;
  }

  async updatePo(data: PoDto, id, request): Promise<PurchaseOrder> {
    const { warehouses, type, isAdmin } = request?.user;

    if (
      (!!data?.warehouseId &&
        !warehouses.includes(data?.warehouseId) &&
        type == $enum(UserType).getKeyOrDefault(UserType.employee, null) &&
        !isAdmin) ||
      !data?.warehouseId
    )
      throw new BadRequestException('Not found warehouse');

    let po = await this.poRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
        }),
          qb.andWhere({
            bizId: request?.user?.companyId,
          });
      },
      relations: ['items'],
    });
    const oldStatus = po?.status;

    // Them thoi gian update trang thai
    if (data?.status && oldStatus != data?.status) {
      po.lastUpdateStatus = new Date();
    }

    // Chi cap nhat trang thai
    // if (PO_SAVE_ONLY_STATUS.includes(data?.status)) {
    //   po.status = data?.status;
    //   await this.poRepository.save(po);
    //   return po;
    // }

    // Chuyen trang thai check hop le
    if (
      !!data?.status &&
      data?.status != oldStatus &&
      (!NEXT_PO_STATUS[po?.status] ||
        (!!NEXT_PO_STATUS[po?.status] && !NEXT_PO_STATUS[po?.status].includes(data?.status)))
    )
      throw new BadRequestException('Status invalid');
    if (
      type == $enum(UserType).getKeyOrDefault(UserType.customer, null) &&
      (!PO_STATUS_CLIENT.includes(data?.status) || !PO_STATUS_CLIENT_CURRENT.includes(oldStatus))
    )
      throw new BadRequestException('Status invalid. Client view');

    const poItemLookup: Record<string, PurchaseOrderItem> = reduce(
      po?.items,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );

    // Trang thai sang nhap kho hoan tat
    if (PO_STATUS_STOCK_PRODUCT.includes(data?.status) && oldStatus != PoStatus.delivery) {
      let valid = true;

      data?.variations.forEach(e => {
        const reality =
          Number(e?.newestGood) +
          Number(poItemLookup[e?.id]?.good ?? 0) +
          Number(e?.newestDamaged) +
          Number(poItemLookup[e?.id]?.damaged ?? 0);
        if (reality < Number(e?.estimate) || reality <= 0) valid = false;
        if (
          Number(e?.newestGood) + Number(e?.newestDamaged) <= 0 &&
          data?.status == PoStatus.success
        )
          throw new BadRequestException({
            code: 'IP_0011',
            message: 'Import reciept cannot be success if Total imported equal 0',
          });
      });

      const newStatus = valid
        ? PoStatus.success
        : data?.status == PoStatus.success
        ? PoStatus.success
        : PoStatus.reStock;
      po.status = newStatus;
    } else if (data?.status) {
      po.status = data?.status;
    }

    // Trang thai duoc cap nhat thong tin phieu
    let dataUpdate = {};
    data = {
      ...data,
      boxes: !!data?.boxes ? data?.boxes : 0,
      estimateDate: !!data?.estimateDate
        ? new Date(moment(data?.estimateDate).format('MM/DD/YYYY'))
        : null,
    };
    if (po.status == PoStatus.new) dataUpdate = data;
    else if (po.status == PoStatus.confirmed)
      dataUpdate = pick(data, ['note', 'boxes', 'estimateDate']);
    else dataUpdate = pick(data, ['note', 'boxes']);

    po = {
      ...po,
      ...dataUpdate,
    } as PurchaseOrder;

    if (!warehouses || (!isAdmin && !warehouses.includes(data?.warehouseId))) delete po.warehouseId;

    if (data?.packageImages) po.packageImages = data?.packageImages;
    if (data?.defectiveImages) po.defectiveImages = data?.defectiveImages;

    po.lastEditorId = request?.user?.id;
    delete po.createdAt;
    delete po.updatedAt;

    const params = [];
    const ids = [];

    let statusChangeQty = false;

    // San pham trong phieu
    data?.variations.forEach(e => {
      if (e?.variantId) {
        let poi = new PurchaseOrderItem();
        poi = {
          ...poi,
          ...e,
          good: Number(e?.newestGood) + Number(poItemLookup[e?.id]?.good ?? 0),
          damaged: Number(e?.newestDamaged) + Number(poItemLookup[e?.id]?.damaged ?? 0),
        } as PurchaseOrderItem;
        if (poi.good != poItemLookup[e?.id]?.good || poi.damaged != poItemLookup[e?.id]?.damaged)
          statusChangeQty = true;
        poi.po = po;

        if (!e?.id) {
          poi.creatorId = request?.user?.id;
          poi.lastEditorId = request?.user?.id;
        } else {
          const item = find(po?.items, function(o) {
            return o.id == e?.id;
          });
          if (
            !!item &&
            (item?.estimate != e?.estimate ||
              item?.good != Number(e?.newestGood) + Number(poItemLookup[e?.id]?.good ?? 0) ||
              item?.damaged != Number(e?.newestDamaged) + Number(poItemLookup[e?.id]?.damaged ?? 0))
          )
            poi.lastEditorId = request?.user?.id;
          ids.push(e?.id);
        }

        delete poi.createdAt;
        delete poi.updatedAt;
        params.push(poi);
      }
    });
    console.log('purchase-order', params, statusChangeQty);
    // Bo san pham
    if (params?.length > 0 && PO_STATUS_EDIT.includes(data?.status)) {
      const poiSQL = this.poiRepository
        .createQueryBuilder()
        .delete()
        .from(PurchaseOrderItem)
        .where('po_id = :id', { id });

      if (ids?.length > 0)
        poiSQL.andWhere('id not in (:...ids)', {
          ids,
        });
      await poiSQL.execute();
    }

    // Cong ton kho
    if (
      [PoStatus.reStock, PoStatus.success].includes(data?.status) ||
      (PoStatus.stock == data?.status && oldStatus != PoStatus.delivery)
    ) {
      console.log('purchase-order', data?.status);
      const res = await this.inventoryService.importStockInventory(
        {
          ...po,
          items: params,
        },
        request?.user,
        moment()?.valueOf(),
      );
      console.log('purchase-order', res);

      if (res.status == 500) throw new BadRequestException(res?.message);
    }

    await this.poiRepository
      .save(
        statusChangeQty
          ? params?.map((it: PurchaseOrderItem) => {
              return {
                ...it,
                po: {
                  ...it?.po,
                  lastImportStock: statusChangeQty ? new Date() : po.lastImportStock,
                },
              };
            })
          : params,
      )
      .catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });

    return po;
  }

  async detailHistories(id: any): Promise<any> {
    const sql = this.logRepository.createQueryBuilder('log');

    sql.select('log.recordId as id');
    sql.addSelect('log.changes as changes');
    sql.addSelect('log.beforeChanges as before_changes');
    sql.addSelect('log.afterChanges as after_changes');
    sql.addSelect('log.action as action');
    sql.addSelect('log.creatorId as uid');
    sql.addSelect('log.tableName as name');
    sql.addSelect('log.createdAt as time');

    sql.where('(log.tableName = :tableName and log.recordId = :id)', {
      tableName: 'purchase_order',
      id,
    });

    sql.orWhere('(log.tableName = :tableItemName and log.parentId = :parentId)', {
      tableItemName: 'purchase_order_item',
      parentId: id,
    });

    sql.orderBy('log.createdAt', 'DESC');
    const data = await sql.getRawMany();

    const result = await HistoryLogUtils.purchaseOrder(data, PoStatus, 'STATUS', [
      'last_editor_id',
      'last_update_status',
    ]);

    console.log(result);
    return result;
  }

  async detail(id: any, request, _header): Promise<PurchaseOrder> {
    return (
      this.poRepository.findOneOrFail({
        where: qb => {
          qb.orWhere({
            id,
            bizId: request?.user?.companyId,
          });
          const { warehouses, type, isAdmin } = request?.user;

          if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
            qb.andWhere([
              {
                creatorId: request?.user?.id,
              },
              {
                clientId: request?.user?.id,
              },
            ]);
          } else if (type == $enum(UserType).getKeyOrDefault(UserType.employee, null) && !isAdmin) {
            qb.andWhere({
              warehouseId: In(warehouses),
            });
          }
        },
        relations: ['warehouse', 'items', 'items.variant', 'items.variant.product'],
      }) ?? null
    );
  }

  async findProductPo(id: any, query: FilterPo): Promise<PurchaseOrderItem[]> {
    const { name } = query;
    return this.poiRepository.find({
      where: qb => {
        qb.andWhere({
          poId: id,
        });

        if (!!name) qb.andWhere('PurchaseOrderItem__variant.barcode ILIKE :from', { from: name });
      },
      relations: ['variant', 'variant.product'],
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async queryPo(
    pagination: PaginationOptions,
    query: FilterPo,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const {
      status,
      clientId,
      warehouseId,
      from,
      to,
      name,
      clientIds,
      statusArr,
      skuIds,
      typeFilter,
      sortBy,
      sortType,
    } = query;
    console.log('🚬 ~ PoService ~ statusArr:', statusArr);
    let { warehouses } = request.user;
    const { type, companyId, isAdmin } = request.user;

    warehouses = !!warehouses ? warehouses : [];

    const mQuery = this.poRepository.createQueryBuilder('po');
    if (pagination) {
      mQuery.take(pagination.limit).skip(pagination.skip);
    }

    if (name)
      mQuery.andWhere([
        {
          code: ILike(`%${name}%`),
        },
        {
          contactName: ILike(`%${name}%`),
        },
        {
          shippingCode: ILike(`%${name}%`),
        },
      ]);

    if (companyId)
      mQuery.andWhere({
        bizId: companyId,
      });

    if (clientId)
      mQuery.andWhere({
        clientId,
      });

    if (!!clientIds)
      mQuery.andWhere({
        clientId: In(clientIds),
      });

    if (!!skuIds) mQuery.andWhere('items.variantId IN (:...skuIds)', { skuIds });

    if (warehouseId)
      mQuery.andWhere({
        warehouseId,
      });

    if (!isNil(headers)) {
      const whIds = headers['warehouse-ids']?.split(',');
      const countryIds = headers['country-ids']?.split(',');

      if (!isEmpty(whIds)) mQuery.andWhere('po.warehouseId IN (:...whIds)', { whIds });
      if (!isEmpty(countryIds))
        mQuery.andWhere('warehouse.countryCode IN (:...countryIds)', { countryIds });
    }

    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      mQuery.andWhere({
        clientId: request?.user?.id,
      });
      mQuery.andWhere('warehouse.type = :typeWarehouse', { typeWarehouse: WarehouseType.main });
    } else if (type == $enum(UserType).getKeyOrDefault(UserType.employee, null) && !isAdmin) {
      mQuery.andWhere({
        warehouseId: In(warehouses),
      });
    }
    if (typeFilter == TypeFilter.changeStatus) {
      mQuery.leftJoin(
        Logs,
        'logs',
        "(logs.recordId = cast(po.id as text) and logs.tableName = 'purchase_order')",
      );
      mQuery.andWhere("logs.action = 'STATUS'");
      if (from) mQuery.andWhere('logs.createdAt >= :from', { from });
      if (to) mQuery.andWhere('logs.createdAt <= :to', { to });
      if (statusArr) mQuery.andWhere('logs.changes && :values::text[]', { values: statusArr });
    } else {
      if (from) mQuery.andWhere('po.createdAt >= :from', { from });
      if (to) mQuery.andWhere('po.createdAt <= :to', { to });
      if (status)
        mQuery.andWhere({
          status,
        });

      if (statusArr) mQuery.andWhere('po.status IN (:...statusArr)', { statusArr });
    }
    mQuery.leftJoinAndSelect('po.warehouse', 'warehouse');
    if (sortBy && sortType) {
      mQuery.addOrderBy(`po.${sortBy}`, sortType, `NULLS LAST`);
    } else {
      mQuery.orderBy('po.updatedAt', 'DESC');
    }
    return mQuery;
  }

  async exportExcel(query: FilterPo, request, header) {
    const sql = await this.queryPo(null, query, header, request);
    sql.leftJoinAndSelect('po.items', 'items');
    sql.leftJoinAndSelect('items.variant', 'variant');

    if (!!query?.ids && query?.ids?.length > 0)
      sql.andWhere('po.id IN (:...ids)', { ids: query?.ids });

    const result = await sql.getMany();

    let userIds = [];
    for (const item of result) {
      userIds.push(Number(item?.clientId));
      userIds.push(Number(item?.creatorId));
    }
    userIds = uniq(userIds);
    const { data: uData } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: userIds },
      timeout: 10000,
    });
    const users = uData as Users[];
    const userLookup: Record<string, Users> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    let columns = [
      'STT',
      'Import Code',
      'Client',
      'Shipping Code',
      'Receipt Name',
      'Warehouse Code',
      'Warehouse Name',
      'Contact Phone',
      'Boxes|Pallet',
      'Status',
      'Note',
      'Qtt. Of SKU',
      'Creator',
      'ETA',
      'Created',
      'Image',
      'Prefix SKU',
      'Variant SKU',
      'Estimated Qtt.',
      'Remaining',
      'Exceed',
      'Total Good Imported',
      'Total Damaged Imported',
    ];
    if (!query.image) {
      columns = columns.filter(col => col !== 'Image');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Import Receipt');

    worksheet.columns = columns.map(header => ({ header }));

    const arrWareHouse = [];

    let i = 1;
    const imagesArray = [];
    const rows = [];
    for (let index = 0; index < result.length; index++) {
      const po = result[index];

      if (!arrWareHouse.includes(po?.warehouse?.name)) arrWareHouse.push(po?.warehouse?.name);
      const arrSku = [];

      for (const item of po?.items) {
        if (!arrSku.includes(item?.variant?.sku)) arrSku.push(item?.variant?.sku);
        try {
          const rowData = [
            i,
            po?.code,
            po?.clientId ? userLookup[po?.clientId]?.name : null,
            po?.shippingCode,
            po?.contactName,
            po?.warehouse?.displayId,
            po?.warehouse?.name,
            po?.contactPhone,
            po?.boxes,
            po?.status
              ? String($enum(PoStatus).getKeyOrDefault(po.status, ''))
                  .charAt(0)
                  .toUpperCase() + String($enum(PoStatus).getKeyOrDefault(po.status, '')).slice(1)
              : null,
            po?.note,
            arrSku.length,
            po?.clientId ? userLookup[po?.creatorId]?.name : null,
            po?.estimateDate
              ? moment(po?.estimateDate)
                  .add(7, 'hours')
                  .format('DD/MM/YY - HH:mm A')
              : '',
            po?.createdAt ? moment(po?.createdAt).format('DD/MM/YY - HH:mm A') : '',
            '', // image
            item?.variant?.prefix,
            item?.variant?.sku,
            item?.estimate,
            item?.estimate - item?.good - item?.damaged >= 0
              ? item?.estimate - item?.good - item?.damaged
              : 0,
            item?.good + item?.damaged - item?.estimate >= 0
              ? item?.good + item?.damaged - item?.estimate
              : 0,
            item?.good,
            item?.damaged,
          ];

          if (!query?.image) {
            rowData.splice(15, 1);
          }
          rows.push(rowData);
          // Add image if available
          if (item?.variant?.images && item?.variant?.images.length > 0 && query?.image) {
            imagesArray.push({
              image: item?.variant?.images[0],
              rowIndex: i + 1,
            });
          }
          i++;
        } catch (error) {
          console.log(`error at`, index, result);
          console.log(`error reason`, error);
        }
      }
    }

    worksheet.addRows(rows);

    // Add images to the worksheet
    const imageColumn = 16;
    if (imagesArray.length > 0) {
      await saveImageToExcel(imagesArray, workbook, worksheet, imageColumn);
    }
    worksheet.getColumn(imageColumn).width = 15;

    const buffer = await workbook.xlsx.writeBuffer();

    return { buffer, arrWareHouse };
  }

  // async importExcelx(request: Record<string, any>, buffer: Buffer, po: PoImportDto) {
  //   console.log('xxx');
  //   const data = ExcelUtils.read(buffer, 0, mapCol.uniq);
  //   const skuObjects = [];

  //   for (const item of data) {
  //     const skuObject = {
  //       'Order Id': item['Order Id'],
  //       'Warehouse ID *': item['Warehouse ID *'],
  //       'Recipient Name *': item['Recipient Name *'],
  //       'Recipient Phone Number *': item['Recipient Phone Number *'],
  //       'Recipient Address *': item['Recipient Address *'],
  //       'Recipient Province Name *': item['Recipient Province Name *'],
  //       'Recipient District Name *': item['Recipient District Name *'],
  //       'Zip Code': item['Zip Code'],
  //     };

  //     for (const key in item) {
  //       if (key.startsWith("SKU")) {
  //         const endC = key.slice(-3);
  //         skuObject['SKU'] = item[key];
  //         skuObject['Quantity'] = item[`Quantity${endC}`];
  //         skuObject['Price'] = item[`Price${endC}`];
  //         skuObjects.push({ ...skuObject });
  //       }
  //     }
  //   }
  //   let params = [];
  //   let idx = 0;
  //   params = reduce(
  //     skuObjects,
  //     (prev: (string | number)[][], next) => {
  //       if (!isNil(next)) {
  //         prev.push([
  //           idx++,
  //           'INHR',
  //           '6ZBDNP',
  //           // next['Warehouse ID *'],
  //           next['Recipient Name *'],
  //           next['Recipient Phone Number *'],
  //           next['Recipient Address *'],
  //           next['Recipient Province Name *'],
  //           next['Recipient District Name *'],
  //           '',
  //           next['Zip Code'],
  //           next['SKU'],
  //           '0.1',
  //           next['Quantity'],
  //           next['Price'],
  //           next['Order Id'],
  //         ]);
  //       }
  //       return prev;
  //     },
  //     [
  //       [
  //         'No',
  //         'ClientID*',
  //         'Warehouse ID *',
  //         'Recipient Name *',
  //         'Recipient Phone Number *',
  //         'Recipient Address *',
  //         'Recipient Province Name *',
  //         'Recipient District Name *',
  //         'Recipient Commune Name *',
  //         'Postcode *',
  //         'SKU *',
  //         'Weight (kg) *',
  //         'Quantity *',
  //         'Retail Price *',
  //         'External Order ID',
  //       ],
  //     ],
  //   );
  //   const xlsxSheets: WorkSheet[] = [];
  //   xlsxSheets.push({
  //     name: 'Template - Import SO',
  //     data: params,
  //     options: {},
  //   });
  //   const bufferx = xlsx.build(xlsxSheets);
  //   return bufferx;
  // }

  async importExcelx(request: Record<string, any>, buffer: Buffer, po: PoImportDto) {
    console.log('xxx');
    const data = ExcelUtils.read(buffer, 0, mapCol.uniq);
    let skuObjects = [];
    let idx = 1;

    for (const item of data) {
      const skuObject = {
        No: null,
        'ClientID*': item['ClientID*'],
        'Warehouse ID *': item['Warehouse ID *'],
        'Recipient Name *': item['Recipient Name *'],
        'Recipient Phone Number *': item['Recipient Phone Number *'],
        'Recipient Address *': item['Recipient Address *'],
        'Recipient Province Name *': item['Recipient Province Name *'],
        'Recipient District Name *': item['Recipient District Name *'],
        'Recipient Commune Name *': item['Recipient Commune Name *'],
        'Postcode *': item['Postcode *'],
        'SKU *': `IN-INHR-${item[`SKU *`]}`,
        'Weight (kg) *': item['Weight (kg) *'],
        'Quantity *': item['Quantity *'],
        'Retail Price *': item['Retail Price *'],
        'External Order ID': item['External Order ID'],
      };
      // if (skuObjects.length == 0) {
      //   skuObject.No = idx;
      //   idx++;
      // } else {
      //   if (
      //     skuObjects[skuObjects.length - 1][`External Order ID`] &&
      //     skuObjects[skuObjects.length - 1][`External Order ID`] != item[`External Order ID`]
      //   ) {
      //     skuObject.No = idx;
      //     idx++;
      //   }
      // }
      skuObjects.push({ ...skuObject });
    }
    skuObjects = skuObjects.sort((a, b) => a[`External Order ID`] - b[`External Order ID`]);

    // for (const item of skuObjects.sort((a, b) => a[`External Order ID`] - b[`External Order ID`])) {
    // if (
    //   skuObjects[skuObjects.length - 1][`External Order ID`] &&
    //   skuObjects[skuObjects.length - 1][`External Order ID`] != item[`External Order ID`]
    // ) {
    //   skuObject.No = idx;
    //   idx++;
    // }
    // }

    for (let index = 0; index < skuObjects.length; index++) {
      const item = skuObjects[index];
      if (index > 0) {
        if (
          skuObjects[index - 1][`External Order ID`] &&
          skuObjects[index - 1][`External Order ID`] != item[`External Order ID`]
        ) {
          item.No = idx;
          idx++;
        }
      } else {
        item.No = idx;
        idx++;
      }
    }

    let params = [];
    params = reduce(
      skuObjects,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            next['No'],
            next['ClientID*'],
            next['Warehouse ID *'],
            next['Recipient Name *'],
            next['Recipient Phone Number *'],
            next['Recipient Address *'],
            next['Recipient Province Name *'],
            null,
            next['Recipient District Name *'],
            null,
            null,
            null,
            next['Postcode *'],
            next['SKU *'],
            next['Weight (kg) *'],
            next['Quantity *'],
            next['Retail Price *'],
            next['External Order ID'],
          ]);
        }
        return prev;
      },
      [
        [
          'No',
          'Client ID *',
          'Warehouse ID *',
          'Recipient Name *',
          'Recipient Phone Number *',
          'Recipient Address *',
          'Recipient Province Name *',
          'Recipient Province ID',
          'Recipient District Name *',
          'Recipient District ID',
          'Recipient Commune Name',
          'Recipient Commune ID',
          'Postcode',
          'SKU *',
          'Weight (kg) *',
          'Quantity *',
          'Retail Price *',
          'External Order ID',
        ],
      ],
    );
    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push({
      name: 'Template - Import SO',
      data: params,
      options: {},
    });
    const bufferx = xlsx.build(xlsxSheets);
    return bufferx;
  }
}
