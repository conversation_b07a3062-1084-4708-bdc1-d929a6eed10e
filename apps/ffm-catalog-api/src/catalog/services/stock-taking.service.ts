import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import {
  BadGatewayException,
  BadRequestException,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { catalogConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { UserType } from 'core/enums/user-type.enum';
import HistoryLogUtils from 'core/utils/HistoryLogUtils';
import {
  cloneDeep,
  compact,
  reduce,
  find,
  intersection,
  isEmpty,
  isNil,
  omit,
  reverse,
  sortBy,
  uniq,
} from 'lodash';
import { $enum } from 'ts-enum-util';
import { Brackets, ILike, In, Repository } from 'typeorm';
import {
  ExportStockTakingDetailDto,
  StockTakingDto,
  StockTakingLogDto,
  StockTakingV2Dto,
} from '../../dtos/stock-taking.dto';
import { WarehouseDto } from '../../dtos/warehouse.dto';
import { Logs } from '../../entities/logs.entity';
import { StockTakingItem } from '../../entities/stock-taking-item.entity';
import { StockTaking } from '../../entities/stock-taking.entity';
import { SlotWarehouses } from '../../entities/warehouses';
import {
  CLIENT_STOCK_TAKING_STATUS_EDIT,
  NEXT_STOCK_TAKING_STATUS,
  STOCK_TAKING_STATUS,
} from '../constants/stock-taking-statuses.constant';
import { StockTakingStatus } from '../enum/stock-taking-status.enum';
import { WarehouseType } from '../enum/warehouse-type.enum';
import { FilterStockTaking } from '../filters/filterStockTaking.filter';
import { FilterWarehouse } from '../filters/warehouse.filter';
import { StockInventoryService } from './stock-inventory.service';
import * as moment from 'moment-timezone';
import { Users } from '../../read-entities/identity-entities/Users';
import { ProductVariation } from '../../entities/product-variation.entity';
import { AttributesValue } from '../../entities/attributes-value.entity';
import xlsx, { WorkSheet } from 'node-xlsx';
import * as ExcelJS from 'exceljs';
import { saveImageToExcel } from 'core/utils/ImageUtils';

export interface IStockTakingItem {
  id?: number;
  stockTakingId?: number;
  lastEditorId?: string;
  variantId: number;
  currentSellable: number;
  currentDamaged: number;
  correctedSellable: number;
  correctedDamaged: number;
  variant: ProductVariation;
  stockTaking?: StockTaking;
  phyGood: number;
  goodOutShelf: number;
  inProgress: number;
}
export interface IStockTaking {
  quantity?: number;
  id?: number;
  warehouseId: number;
  code: string;
  bizId: string;
  lastEditorId: string;
  creatorId: string;
  clientId: string;
  warehouse: SlotWarehouses;
  dueDate: Date;
  status: StockTakingStatus | string;
  name: string;
  note: string;
  items: IStockTakingItem[];
}

@Injectable()
export class StockTakingService {
  constructor(
    @InjectRepository(ProductVariation, catalogConnection)
    private variantRepository: Repository<ProductVariation>,
    @InjectRepository(SlotWarehouses, catalogConnection)
    private whRepository: Repository<SlotWarehouses>,
    @InjectRepository(StockTaking, catalogConnection)
    private stRepository: Repository<StockTaking>,
    @InjectRepository(StockTakingItem, catalogConnection)
    private stiRepository: Repository<StockTakingItem>,
    @InjectRepository(Logs, catalogConnection)
    private logRepository: Repository<Logs>,
    private readonly amqpConnection: AmqpConnection,
    private inventoryService: StockInventoryService,
  ) {}

  async findAll(
    pagination: PaginationOptions,
    query: FilterStockTaking,
    request,
    header,
  ): Promise<[StockTaking[], number]> {
    const { warehouses, type, companyId, isAdmin } = request?.user;
    const {
      status,
      clientId,
      warehouseId,
      from,
      to,
      name,
      clientIds,
      statuses,
      warehouseIds,
      variantIds,
    } = query;
    // const data = await this.stRepository.findAndCount({
    //   take: pagination?.limit,
    //   skip: pagination?.skip,
    //   where: qb => {
    //     if (name)
    //       qb.andWhere(
    //         new Brackets(qr => {
    //           qr.where('StockTaking.name ILIKE :name', {
    //             name: `%${name}%`,
    //           })
    //             .orWhere('cast(StockTaking.id as text) ILIKE :name', {
    //               name: `%${name}%`,
    //             })
    //             .orWhere('StockTaking.code ILIKE :name', {
    //               name: `%${name}%`,
    //             });
    //         }),
    //       );

    //     if (status)
    //       qb.andWhere({
    //         status,
    //       });

    //     if (!!warehouseIds)
    //       qb.andWhere({
    //         warehouseId: In(warehouseIds),
    //       });

    //     if (!!statuses)
    //       qb.andWhere({
    //         status: In(statuses),
    //       });

    //     if (!!clientId && type != $enum(UserType).getKeyOrDefault(UserType.customer, null))
    //       qb.andWhere({
    //         clientId,
    //       });

    //     if (warehouseId)
    //       qb.andWhere({
    //         warehouseId,
    //       });

    //     if (!isNil(header)) {
    //       const whIds = header['warehouse-ids']?.split(',');
    //       const countryIds = header['country-ids']?.split(',');

    //       if (!isEmpty(whIds)) qb.andWhere('StockTaking.warehouseId IN (:...whIds)', { whIds });
    //       if (!isEmpty(countryIds))
    //         qb.andWhere('StockTaking__warehouse.countryCode IN (:...countryIds)', { countryIds });
    //     }

    //     if (companyId)
    //       qb.andWhere({
    //         bizId: companyId,
    //       });

    //     if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
    //       qb.andWhere({
    //         clientId: request?.user?.id,
    //       });
    //       qb.andWhere('StockTaking__warehouse.type = :typeWarehouse', {
    //         typeWarehouse: WarehouseType.main,
    //       });
    //     } else if (type == $enum(UserType).getKeyOrDefault(UserType.employee, null) && !isAdmin) {
    //       qb.andWhere({
    //         warehouseId: In(warehouses),
    //       });
    //     }

    //     if (!!clientIds)
    //       qb.andWhere({
    //         clientId: In(clientIds),
    //       });

    //     if (from) qb.andWhere('StockTaking.created_at >= :from', { from });
    //     if (to) qb.andWhere('StockTaking.created_at <= :to', { to });
    //   },
    //   relations: ['warehouse'],
    //   order: {
    //     updatedAt: 'DESC',
    //   },
    // });
    const qb = await this.getSQLQueryStockTakingInventory(query, request, header, pagination);
    qb.leftJoin('st.items', 'items');

    // const qb = this.stRepository
    //   .createQueryBuilder('st')
    //   .leftJoinAndSelect('st.warehouse', 'warehouse')
    //   .leftJoin('st.items', 'items')
    //   .take(pagination?.limit)
    //   .skip(pagination?.skip)
    //   .orderBy('st.updatedAt', 'DESC');

    // // Điều kiện tìm kiếm theo tên
    // if (name) {
    //   qb.andWhere(
    //     new Brackets(qr => {
    //       qr.where('st.name ILIKE :name', { name: `%${name}%` })
    //         .orWhere('cast(st.id as text) ILIKE :name', { name: `%${name}%` })
    //         .orWhere('st.code ILIKE :name', { name: `%${name}%` });
    //     }),
    //   );
    // }

    // // Điều kiện theo trạng thái
    // if (status) {
    //   qb.andWhere('st.status = :status', { status });
    // }

    // if (!!warehouseIds) {
    //   qb.andWhere('st.warehouseId IN (:...warehouseIds)', { warehouseIds });
    // }

    // if (!!statuses) {
    //   qb.andWhere('st.status IN (:...statuses)', { statuses });
    // }

    // if (!!clientId && type != $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
    //   qb.andWhere('st.clientId = :clientId', { clientId });
    // }

    // if (warehouseId) {
    //   qb.andWhere('st.warehouseId = :warehouseId', { warehouseId });
    // }

    // if (!isNil(header)) {
    //   const whIds = header['warehouse-ids']?.split(',');
    //   const countryIds = header['country-ids']?.split(',');

    //   if (!isEmpty(whIds)) qb.andWhere('st.warehouseId IN (:...whIds)', { whIds });
    //   if (!isEmpty(countryIds))
    //     qb.andWhere('warehouse.countryCode IN (:...countryIds)', { countryIds });
    // }

    // if (companyId) {
    //   qb.andWhere('st.bizId = :companyId', { companyId });
    // }

    // if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
    //   qb.andWhere('st.clientId = :clientId', { clientId: request?.user?.id });
    //   qb.andWhere('warehouse.type = :typeWarehouse', { typeWarehouse: WarehouseType.main });
    // } else if (type == $enum(UserType).getKeyOrDefault(UserType.employee, null) && !isAdmin) {
    //   qb.andWhere('st.warehouseId IN (:...warehouses)', { warehouses });
    // }

    // if (!!clientIds) {
    //   qb.andWhere('st.clientId IN (:...clientIds)', { clientIds });
    // }

    // if (!isEmpty(variantIds)) {
    //   qb.andWhere('items.variant_id IN (:...variantIds)', { variantIds });
    // }

    // // Điều kiện thời gian
    // if (from) qb.andWhere('st.created_at >= :from', { from });
    // if (to) qb.andWhere('st.created_at <= :to', { to });

    const data = await qb.getManyAndCount();

    return data;
  }

  async getOneStockTaking(id, request, header): Promise<StockTaking> {
    const { warehouses, type, companyId, isAdmin } = request?.user;
    const data = await this.stRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
          bizId: companyId,
        });
        if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
          qb.andWhere([
            {
              creatorId: request?.user?.id,
            },
            {
              clientId: request?.user?.id,
            },
          ]);
        } else if (type == $enum(UserType).getKeyOrDefault(UserType.employee, null) && !isAdmin) {
          qb.andWhere({
            warehouseId: In(warehouses),
          });
        }

        if (!isNil(header)) {
          const whIds = header['warehouse-ids']?.split(',');
          const countryIds = header['country-ids']?.split(',');

          if (!isEmpty(whIds)) qb.andWhere('StockTaking.warehouseId IN (:...whIds)', { whIds });
          if (!isEmpty(countryIds))
            qb.andWhere('StockTaking__warehouse.countryCode IN (:...countryIds)', { countryIds });
        }
      },
      relations: [
        'warehouse',
        'items',
        'items.variant',
        'items.variant.product',
        'items.variant.properties',
      ],
      order: {
        createdAt: 'DESC',
      },
    });
    return data;
  }

  async getOneStockTakingV2(id, request, header): Promise<IStockTaking> {
    const { warehouses, type, companyId, isAdmin } = request?.user;
    const data = await this.stRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
          bizId: companyId,
        });
        if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
          qb.andWhere([
            {
              creatorId: request?.user?.id,
            },
            {
              clientId: request?.user?.id,
            },
          ]);
        } else if (type == $enum(UserType).getKeyOrDefault(UserType.employee, null) && !isAdmin) {
          qb.andWhere({
            warehouseId: In(warehouses),
          });
        }

        if (!isNil(header)) {
          const whIds = header['warehouse-ids']?.split(',');
          const countryIds = header['country-ids']?.split(',');

          if (!isEmpty(whIds)) qb.andWhere('StockTaking.warehouseId IN (:...whIds)', { whIds });
          if (!isEmpty(countryIds))
            qb.andWhere('StockTaking__warehouse.countryCode IN (:...countryIds)', { countryIds });
        }
      },
      relations: [
        'warehouse',
        'items',
        'items.variant',
        'items.variant.product',
        'items.variant.properties',
      ],
      order: {
        createdAt: 'DESC',
      },
    });

    // console.log(222, data, $enum(StockTakingStatus).getKeyOrDefault(Number(data.status), null));

    return {
      ...data,
      status: $enum(StockTakingStatus).getKeyOrDefault(Number(data.status), null),
      items: data?.items?.map((item: StockTakingItem) => {
        return {
          ...item,
          currentSellable: item.inventoryGood,
          currentDamaged: item.inventoryDamaged,
          correctedSellable: item.good,
          correctedDamaged: item.damaged,
          phyGood: item.phyGood,
          goodOutShelf: item.goodOutShelf,
          inProgress: item.inProgress,
        } as IStockTakingItem;
      }),
    } as IStockTaking;
  }

  async createSt(request, data: StockTakingDto): Promise<StockTaking> {
    const { warehouses, type, companyId, id, isAdmin } = request?.user;

    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      const wh = await this.whRepository.findOne({
        where: qb => {
          qb.where({
            id: data?.warehouseId,
            type: WarehouseType.main,
          });
        },
      });
      if (!wh?.id || data?.clientId != id) throw new Error('Warehouse or Client invalid');
    }

    if (
      (!!data?.warehouseId &&
        !warehouses.includes(data?.warehouseId) &&
        type == $enum(UserType).getKeyOrDefault(UserType.employee, null) &&
        !isAdmin) ||
      !data?.warehouseId
    )
      throw new Error('Not found warehouse');

    let st = new StockTaking();
    st = {
      ...st,
      ...data,
      bizId: companyId,
      dueDate: !!data?.dueDate ? data?.dueDate : null,
      status: StockTakingStatus.new,
      items: [],
    } as StockTaking;

    st.creatorId = id;
    st.lastEditorId = id;

    data?.items.forEach(e => {
      if (e?.variantId) {
        let sti = new StockTakingItem();
        sti = {
          ...sti,
          ...omit(e, ['id']),
        } as StockTakingItem;
        sti.lastEditorId = id;
        st.items.push(sti);
      }
    });

    return await this.stRepository.save(st);
  }

  async updateSt(data: StockTakingDto, id: number, request): Promise<StockTaking> {
    const { warehouses, type, companyId, isAdmin } = request?.user;

    if (!!data?.warehouseId && !warehouses.includes(data?.warehouseId) && !isAdmin)
      throw new Error('Not found warehouse');

    if (
      !CLIENT_STOCK_TAKING_STATUS_EDIT.includes(data?.status.toString()) &&
      type == $enum(UserType).getKeyOrDefault(UserType.customer, null)
    )
      throw new BadRequestException('Status invalid');

    let st = await this.stRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
          bizId: companyId,
        });
      },
      relations: ['items'],
    });
    const currentStatus = st.status;
    if (st?.status == StockTakingStatus.done) throw new BadRequestException('Status invalid');

    const syncInventory = StockTakingStatus.done == data?.status && data?.status != st?.status;

    const strStatus = STOCK_TAKING_STATUS[st?.status];
    const strNewStatus = STOCK_TAKING_STATUS[data?.status];

    console.log(data?.status, st?.status, NEXT_STOCK_TAKING_STATUS[strStatus]);

    if (
      !!strNewStatus &&
      strNewStatus != strStatus &&
      (!NEXT_STOCK_TAKING_STATUS[strStatus] ||
        (!!NEXT_STOCK_TAKING_STATUS[strStatus] &&
          !NEXT_STOCK_TAKING_STATUS[strStatus].includes(strNewStatus)))
    )
      throw new ForbiddenException('');

    st = {
      ...st,
      ...omit(data, currentStatus == StockTakingStatus.checking ? ['warehouseId', 'clientId'] : []),
      dueDate: !!data?.dueDate ? data?.dueDate : null,
      lastEditorId: request?.user?.id,
    } as StockTaking;

    const params = [];

    data?.items.forEach(e => {
      if (e?.variantId) {
        let sti = new StockTakingItem();
        sti = {
          ...sti,
          ...omit(e, e?.stockTakingId ? [] : ['id']),
          lastEditorId: request?.user?.id,
        };

        if (
          (currentStatus == StockTakingStatus.checking && !e?.stockTakingId) ||
          (!!e?.stockTakingId && e?.stockTakingId != st.id)
        )
          throw new BadRequestException('Variant invalid');
        if (
          currentStatus == StockTakingStatus.new ||
          (currentStatus == StockTakingStatus.checking && !!e?.stockTakingId)
        )
          params.push(sti);
      }
    });
    if (data?.items?.length != st.items?.length && currentStatus == StockTakingStatus.checking)
      throw new BadRequestException('Variant invalid');
    if ([StockTakingStatus.new, StockTakingStatus.checking]?.includes(currentStatus))
      st.items = params;

    await this.stRepository.save(st).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });

    if (syncInventory) {
      await this.amqpConnection.publish('stock-inventory-service', 'stock-taking-stock', {
        id,
        user: request?.user,
      });
    }

    return st;
  }

  async detailHistories(id: any, request: any): Promise<any> {
    const STOCK_TAKING_MAPPING = {
      item: [
        'inventory_sellable',
        'inventory_good',
        'inventory_damaged',
        'sellable',
        'good',
        'damaged',
      ],
      name: 'name',
      note: 'note',
      due_date: 'due_date',
      warehouse_id: 'warehouse_id',
      variant_id: 'variant_id',
      client_id: 'client_id',
    };

    const STOCK_TAKING_TITLE_MAPPING = {
      [STOCK_TAKING_MAPPING?.item?.[0]]: 'Stocked Phy.Sellable',
      [STOCK_TAKING_MAPPING?.item?.[1]]: 'Agg. Actual Good',
      [STOCK_TAKING_MAPPING?.item?.[2]]: 'Agg. Damaged',
      [STOCK_TAKING_MAPPING?.item?.[3]]: 'Sellable Quantity',
      [STOCK_TAKING_MAPPING?.item?.[4]]: 'Actual Good Quantity',
      [STOCK_TAKING_MAPPING?.item?.[5]]: 'Actual Damaged Quantity',
    };

    const sql = this.logRepository.createQueryBuilder('log');

    sql.select('log.recordId as id');
    sql.addSelect('log.changes as changes');
    sql.addSelect('log.beforeChanges as before');
    sql.addSelect('log.afterChanges as after');
    sql.addSelect('log.table_name as table');
    sql.addSelect('log.action as action');
    sql.addSelect('log.creatorId as uid');
    sql.addSelect('log.createdAt as time');

    sql.where(
      '(log.action != :status OR (log.action = :status AND log.beforeChanges is not null))',
      { status: 'STATUS' },
    );

    sql.andWhere(
      '((log.tableName = :tableName and log.recordId = :id) OR (log.tableName = :tableItemName and log.parentId = :parentId))',
      {
        insert: 'INSERT',
        tableName: 'stock_taking',
        id,
        tableItemName: 'stock_taking_item',
        parentId: id,
      },
    );

    sql.orderBy('log.createdAt', 'DESC');
    const data = await sql.getRawMany();
    let result: StockTakingLogDto[] = [];

    const userIds = [],
      whIds = [],
      variantIds = [];

    data.forEach((item: any) => {
      const dataValid: any[] =
        !!item?.changes && item?.changes?.length > 0 ? item?.changes : item?.after ?? [];
      dataValid?.map((e: any, index: number) => {
        if (index % 2 == 0) {
          if (e == STOCK_TAKING_MAPPING.warehouse_id) whIds.push(dataValid[index + 1]);
          if (e == STOCK_TAKING_MAPPING.client_id) userIds.push(dataValid[index + 1]);
          if (e == STOCK_TAKING_MAPPING.variant_id) variantIds.push(Number(dataValid[index + 1]));
          userIds.push(item?.uid);
        }
      });
    });

    console.log(userIds, whIds, variantIds);

    let users: Users[];
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-users',
        routingKey: 'find-user',
        payload: {
          filters: { ids: uniq(userIds) },
          pagination: {},
          header: {},
          request: { user: { companyId: request?.user?.companyId } },
        },
        timeout: 10000,
      });
      users = data as Users[];
    } catch (error) {
      console.log(error);
      throw new BadRequestException();
    }

    const userLookup = [],
      warehouseLookup = [],
      productLookup = [];

    users.forEach((user: Users) => {
      userLookup[user?.id] = `${!!user?.displayId ? `${user?.displayId} - ` : ''}${user?.name}`;
    });
    const [warehouses, products] = await Promise.all([
      this.whRepository
        .createQueryBuilder('wh')
        .andWhere({
          bizId: request?.user?.companyId,
          id: In(uniq(whIds)),
        })
        .addSelect('name', 'name')
        .addSelect('display_id', 'displayId')
        .getMany(),
      this.variantRepository
        .createQueryBuilder('var')
        .andWhere({
          bizId: request?.user?.companyId,
          id: In(uniq(variantIds)),
        })
        .leftJoinAndSelect('var.product', 'product')
        .leftJoinAndSelect('var.properties', 'properties')
        .getMany(),
    ]);

    warehouses.forEach((wh: SlotWarehouses) => {
      warehouseLookup[wh?.id] = `${!!wh?.displayId ? `${wh?.displayId} - ` : ''}${wh?.name}`;
    });

    console.log(data);
    products.forEach((variant: ProductVariation) => {
      productLookup[variant?.id] = `${variant?.sku} ${variant?.properties?.map(
        (pp: AttributesValue) => pp?.name,
      )}, ${variant?.product?.name} - ${variant?.product?.sku}`;
    });

    data.forEach((item: any) => {
      const afterChanges = {},
        beforeChanges = {};
      item?.before?.map((e: any, index: number) => {
        if (index % 2 == 0) {
          beforeChanges[e] = item?.before[index + 1] ?? '';
        }
      });
      item?.after?.map((e: any, index: number) => {
        if (index % 2 == 0) {
          afterChanges[e] = item?.after[index + 1] ?? '';
        }
      });

      const dataValid: any[] =
        !!item?.changes && item?.changes?.length > 0 ? item?.changes : item?.after ?? [];

      const value = new StockTakingLogDto();
      value.actor = userLookup[item?.uid] ?? '-';
      value.date = item?.time;
      value.time = moment(item?.time)?.valueOf();

      if (item?.table == 'stock_taking_item') {
        value.type = 2;
        if (item?.action == 'DELETE') {
          result.push({
            ...value,
            from:
              `${productLookup[beforeChanges[STOCK_TAKING_MAPPING?.variant_id]]} (` +
              STOCK_TAKING_MAPPING?.item
                ?.map((it: string) => `${STOCK_TAKING_TITLE_MAPPING[it]} - ${beforeChanges[it]}`)
                .join(', ') +
              ') ',
            action: 'Removed product',
          });
        }
        if (
          item?.action == 'UPDATE' &&
          intersection(STOCK_TAKING_MAPPING?.item, dataValid)?.length > 0
        ) {
          result.push({
            ...value,
            from:
              `${productLookup[afterChanges[STOCK_TAKING_MAPPING?.variant_id]]} (` +
              STOCK_TAKING_MAPPING?.item
                ?.map(
                  (it: string) =>
                    `${STOCK_TAKING_TITLE_MAPPING[it]} - ${beforeChanges[it] ?? afterChanges[it]}`,
                )
                .join(', ') +
              ') ',
            to:
              '(' +
              [
                STOCK_TAKING_MAPPING?.item?.[3],
                STOCK_TAKING_MAPPING?.item?.[4],
                STOCK_TAKING_MAPPING?.item?.[5],
              ]
                ?.map((it: string) => `${STOCK_TAKING_TITLE_MAPPING[it]} - ${afterChanges[it]}`)
                .join(', ') +
              ') ',
            action: 'Changed Stock Inventory',
          });
        }
        if (item?.action == 'INSERT') {
          result.push({
            ...value,
            from:
              `${productLookup[afterChanges[STOCK_TAKING_MAPPING?.variant_id]]} (` +
              [
                STOCK_TAKING_MAPPING?.item?.[0],
                STOCK_TAKING_MAPPING?.item?.[1],
                STOCK_TAKING_MAPPING?.item?.[2],
              ]
                ?.map((it: string) => `${STOCK_TAKING_TITLE_MAPPING[it]} - ${afterChanges[it]}`)
                .join(', ') +
              ') ',
            action: 'Added product',
          });
        }
      } else {
        value.type = 1;
        if (item?.action == 'INSERT') {
          result.push({
            ...value,
            action: 'Created Stock-taking Receipt',
          });
        } else if (item?.action == 'STATUS') {
          result.push({
            ...value,
            action: 'Changed Status',
            from: $enum(StockTakingStatus).getKeyOrDefault(Number(item?.before?.[0]), null),
            to: $enum(StockTakingStatus).getKeyOrDefault(Number(item?.changes?.[0]), null),
          });
        } else {
          if (dataValid.includes(STOCK_TAKING_MAPPING.name)) {
            result.push({
              ...value,
              action: 'Changed Name',
              from: beforeChanges[STOCK_TAKING_MAPPING.name],
              to: afterChanges[STOCK_TAKING_MAPPING.name],
            });
          }

          if (dataValid.includes(STOCK_TAKING_MAPPING.client_id)) {
            result.push({
              ...value,
              action: 'Changed Client',
              from: userLookup[beforeChanges[STOCK_TAKING_MAPPING.client_id]] ?? '-',
              to: userLookup[afterChanges[STOCK_TAKING_MAPPING.client_id]] ?? '-',
            });
          }

          if (dataValid.includes(STOCK_TAKING_MAPPING.warehouse_id)) {
            result.push({
              ...value,
              action: 'Changed Warehouse',
              from: warehouseLookup[beforeChanges[STOCK_TAKING_MAPPING.warehouse_id]] ?? '-',
              to: warehouseLookup[afterChanges[STOCK_TAKING_MAPPING.warehouse_id]] ?? '-',
            });
          }

          if (dataValid.includes(STOCK_TAKING_MAPPING.due_date)) {
            result.push({
              ...value,
              action: 'Changed Due-date',
              from: moment(beforeChanges[STOCK_TAKING_MAPPING.due_date]).format('DD/MM/YYYY'),
              to: moment(afterChanges[STOCK_TAKING_MAPPING.due_date]).format('DD/MM/YYYY'),
            });
          }

          if (dataValid.includes(STOCK_TAKING_MAPPING.note)) {
            result.push({
              ...value,
              action: 'Changed Note',
              from: beforeChanges[STOCK_TAKING_MAPPING.note],
              to: afterChanges[STOCK_TAKING_MAPPING.note],
            });
          }
        }
      }
    });

    // console.log(result);
    result = sortBy(result, ['time', 'type']);
    reverse(result);
    return result;
  }

  async getHistoriesV2(id: any, request: any): Promise<any> {
    const STOCK_TAKING_MAPPING = {
      item: ['inventory_good', 'inventory_damaged', 'good', 'damaged'],
      validate: [
        'inventory_good',
        'inventory_damaged',
        'good',
        'damaged',
        'good_out_shelf',
        'in_progress',
      ],
      name: 'name',
      note: 'note',
      due_date: 'due_date',
      warehouse_id: 'warehouse_id',
      variant_id: 'variant_id',
      client_id: 'client_id',
      currentGood: 'inventory_good',
      currentDamaged: 'inventory_damaged',
      correctGood: 'good',
      correctDamaged: 'damaged',
      goodOutShelf: 'good_out_shelf',
      inProgress: 'in_progress',
    };

    const STOCK_TAKING_TITLE_MAPPING = {
      [STOCK_TAKING_MAPPING?.item?.[0]]: 'Current Sellable',
      [STOCK_TAKING_MAPPING?.item?.[1]]: 'Current Damaged',
      [STOCK_TAKING_MAPPING?.item?.[2]]: 'Corrected Sellable',
      [STOCK_TAKING_MAPPING?.item?.[3]]: 'Corrected Damaged',
      ['Differenced Sellable']: 'Differenced Sellable',
      ['Differenced Damaged']: 'Differenced Damaged',
    };

    const sql = this.logRepository.createQueryBuilder('log');

    sql.select('log.recordId as id');
    sql.addSelect('log.changes as changes');
    sql.addSelect('log.beforeChanges as before');
    sql.addSelect('log.afterChanges as after');
    sql.addSelect('log.table_name as table');
    sql.addSelect('log.action as action');
    sql.addSelect('log.creatorId as uid');
    sql.addSelect('log.createdAt as time');

    sql.where(
      '(log.action != :status OR (log.action = :status AND log.beforeChanges is not null))',
      { status: 'STATUS' },
    );

    sql.andWhere(
      '((log.tableName = :tableName and log.recordId = :id) OR (log.tableName = :tableItemName and log.parentId = :parentId))',
      {
        insert: 'INSERT',
        tableName: 'stock_taking',
        id,
        tableItemName: 'stock_taking_item',
        parentId: id,
      },
    );

    sql.orderBy('log.createdAt', 'DESC');
    const data = await sql.getRawMany();
    let result: StockTakingLogDto[] = [];

    const userIds = [],
      whIds = [],
      variantIds = [];

    data.forEach((item: any) => {
      const dataValid: any[] =
        !!item?.changes && item?.changes?.length > 0 ? item?.changes : item?.after ?? [];
      dataValid?.map((e: any, index: number) => {
        if (index % 2 == 0) {
          if (e == STOCK_TAKING_MAPPING.warehouse_id) whIds.push(dataValid[index + 1]);
          if (e == STOCK_TAKING_MAPPING.client_id) userIds.push(dataValid[index + 1]);
          if (e == STOCK_TAKING_MAPPING.variant_id) variantIds.push(Number(dataValid[index + 1]));
          userIds.push(item?.uid);
        }
      });
    });

    console.log(userIds, whIds, variantIds);

    let users: Users[];
    try {
      const { data } = await this.amqpConnection.request({
        exchange: 'identity-service-users',
        routingKey: 'find-user',
        payload: {
          filters: { ids: uniq(userIds) },
          pagination: {},
          header: {},
          request: { user: { companyId: request?.user?.companyId } },
        },
        timeout: 10000,
      });
      users = data as Users[];
    } catch (error) {
      console.log(error);
      throw new BadRequestException();
    }

    const userLookup = [],
      warehouseLookup = [],
      productLookup = [];

    users.forEach((user: Users) => {
      userLookup[user?.id] = `${!!user?.displayId ? `${user?.displayId} - ` : ''}${user?.name}`;
    });
    const [warehouses, products] = await Promise.all([
      this.whRepository
        .createQueryBuilder('wh')
        .andWhere({
          bizId: request?.user?.companyId,
          id: In(uniq(whIds)),
        })
        .addSelect('name', 'name')
        .addSelect('display_id', 'displayId')
        .getMany(),
      this.variantRepository
        .createQueryBuilder('var')
        .andWhere({
          bizId: request?.user?.companyId,
          id: In(uniq(variantIds)),
        })
        .leftJoinAndSelect('var.product', 'product')
        .leftJoinAndSelect('var.properties', 'properties')
        .getMany(),
    ]);

    warehouses.forEach((wh: SlotWarehouses) => {
      warehouseLookup[wh?.id] = `${!!wh?.displayId ? `${wh?.displayId} - ` : ''}${wh?.name}`;
    });

    console.log(data);
    products.forEach((variant: ProductVariation) => {
      productLookup[variant?.id] = `${variant?.sku} - ${variant?.properties?.map(
        (pp: AttributesValue) => pp?.name,
      )}, ${variant?.product?.name} - ${variant?.product?.sku}`;
    });

    data.forEach((item: any) => {
      const afterChanges = {},
        beforeChanges = {};
      item?.before?.map((e: any, index: number) => {
        if (index % 2 == 0) {
          beforeChanges[e] = item?.before[index + 1] ?? '';
        }
      });
      item?.after?.map((e: any, index: number) => {
        if (index % 2 == 0) {
          afterChanges[e] = item?.after[index + 1] ?? '';
        }
      });

      // console.log(222, afterChanges);

      const dataValid: any[] =
        !!item?.changes && item?.changes?.length > 0 ? item?.changes : item?.after ?? [];

      const value = new StockTakingLogDto();
      value.actor = userLookup[item?.uid] ?? '-';
      value.date = item?.time;
      value.time = moment(item?.time)?.valueOf();

      if (item?.table == 'stock_taking_item') {
        value.type = 2;
        if (item?.action == 'DELETE') {
          result.push({
            ...value,
            from:
              `${productLookup[beforeChanges[STOCK_TAKING_MAPPING?.variant_id]]} (` +
              STOCK_TAKING_MAPPING?.item
                ?.map((it: string) => `${STOCK_TAKING_TITLE_MAPPING[it]} - ${beforeChanges[it]}`)
                .join(', ') +
              ') ',
            action: 'Removed product',
          });
        }
        if (
          item?.action == 'UPDATE' &&
          intersection(STOCK_TAKING_MAPPING?.validate, dataValid)?.length > 0
        ) {
          console.log(afterChanges, beforeChanges, item);

          const correctedSellable = beforeChanges[STOCK_TAKING_MAPPING?.correctGood]
            ? Number(beforeChanges[STOCK_TAKING_MAPPING?.correctGood]) == 0
              ? 0
              : Number(beforeChanges[STOCK_TAKING_MAPPING?.correctGood]) -
                Number(afterChanges[STOCK_TAKING_MAPPING?.inProgress])
            : Number(afterChanges[STOCK_TAKING_MAPPING?.correctGood]) -
              Number(afterChanges[STOCK_TAKING_MAPPING?.inProgress]);

          const correctedDamaged =
            beforeChanges[STOCK_TAKING_MAPPING?.correctDamaged] ??
            afterChanges[STOCK_TAKING_MAPPING?.correctDamaged];

          const differencedSellable =
            Number(correctedSellable) - Number(afterChanges[STOCK_TAKING_MAPPING?.currentGood]);

          const differencedDamaged =
            Number(correctedDamaged) - Number(afterChanges[STOCK_TAKING_MAPPING?.currentDamaged]);

          result.push({
            ...value,
            from: `${
              productLookup[afterChanges[STOCK_TAKING_MAPPING?.variant_id]]
            } (Corrected Sellable - ${correctedSellable}, Differenced Sellable - ${differencedSellable}, Corrected Damaged - ${correctedDamaged}, Differenced Damaged - ${differencedDamaged})`,

            to: `(Corrected Sellable - ${Number(afterChanges[STOCK_TAKING_MAPPING?.correctGood]) -
              Number(
                afterChanges[STOCK_TAKING_MAPPING?.inProgress],
              )}, Differenced Sellable - ${Number(afterChanges[STOCK_TAKING_MAPPING?.correctGood]) -
              Number(afterChanges[STOCK_TAKING_MAPPING?.inProgress]) -
              Number(afterChanges[STOCK_TAKING_MAPPING?.currentGood])}, Corrected Damaged - ${
              afterChanges[STOCK_TAKING_MAPPING?.correctDamaged]
            }, Differenced Damaged - ${Number(afterChanges[STOCK_TAKING_MAPPING?.correctDamaged]) -
              Number(afterChanges[STOCK_TAKING_MAPPING?.currentDamaged])})`,
            action: 'Changed Stock Inventory',
          });
        }
        if (item?.action == 'INSERT') {
          result.push({
            ...value,
            from:
              `${productLookup[afterChanges[STOCK_TAKING_MAPPING?.variant_id]]} (` +
              [STOCK_TAKING_MAPPING?.item?.[0], STOCK_TAKING_MAPPING?.item?.[1]]
                ?.map((it: string) => `${STOCK_TAKING_TITLE_MAPPING[it]} - ${afterChanges[it]}`)
                .join(', ') +
              ') ',
            action: 'Added product',
          });
        }
      } else {
        value.type = 1;
        if (item?.action == 'INSERT') {
          result.push({
            ...value,
            action: 'Created Stock-taking Receipt',
          });
        } else if (item?.action == 'STATUS') {
          result.push({
            ...value,
            action: 'Changed Status',
            from: $enum(StockTakingStatus).getKeyOrDefault(Number(item?.before?.[0]), null),
            to: $enum(StockTakingStatus).getKeyOrDefault(Number(item?.changes?.[0]), null),
          });
        } else {
          if (dataValid.includes(STOCK_TAKING_MAPPING.name)) {
            result.push({
              ...value,
              action: 'Changed Name',
              from: beforeChanges[STOCK_TAKING_MAPPING.name],
              to: afterChanges[STOCK_TAKING_MAPPING.name],
            });
          }

          if (dataValid.includes(STOCK_TAKING_MAPPING.client_id)) {
            result.push({
              ...value,
              action: 'Changed Client',
              from: userLookup[beforeChanges[STOCK_TAKING_MAPPING.client_id]] ?? '-',
              to: userLookup[afterChanges[STOCK_TAKING_MAPPING.client_id]] ?? '-',
            });
          }

          if (dataValid.includes(STOCK_TAKING_MAPPING.warehouse_id)) {
            result.push({
              ...value,
              action: 'Changed Warehouse',
              from: warehouseLookup[beforeChanges[STOCK_TAKING_MAPPING.warehouse_id]] ?? '-',
              to: warehouseLookup[afterChanges[STOCK_TAKING_MAPPING.warehouse_id]] ?? '-',
            });
          }

          if (dataValid.includes(STOCK_TAKING_MAPPING.due_date)) {
            result.push({
              ...value,
              action: 'Changed Due-date',
              from: moment(beforeChanges[STOCK_TAKING_MAPPING.due_date]).format('DD/MM/YYYY'),
              to: moment(afterChanges[STOCK_TAKING_MAPPING.due_date]).format('DD/MM/YYYY'),
            });
          }

          if (dataValid.includes(STOCK_TAKING_MAPPING.note)) {
            result.push({
              ...value,
              action: 'Changed Note',
              from: beforeChanges[STOCK_TAKING_MAPPING.note],
              to: afterChanges[STOCK_TAKING_MAPPING.note],
            });
          }
        }
      }
    });

    // console.log(result);
    result = sortBy(result, ['time', 'type']);
    reverse(result);
    return result;
  }

  async createStockTakingV2(request, data: StockTakingV2Dto): Promise<StockTaking> {
    const { warehouses, type, companyId, id, isAdmin } = request?.user;

    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      const wh = await this.whRepository.findOne({
        where: qb => {
          qb.where({
            id: data?.warehouseId,
            type: WarehouseType.main,
          });
        },
      });
      if (!wh?.id || data?.clientId != id) throw new Error('Warehouse or Client invalid');
    }

    if (
      (!!data?.warehouseId &&
        !warehouses.includes(data?.warehouseId) &&
        type == $enum(UserType).getKeyOrDefault(UserType.employee, null) &&
        !isAdmin) ||
      !data?.warehouseId
    )
      throw new Error('Not found warehouse');

    let st = new StockTaking();
    st = {
      ...data,
      bizId: companyId,
      dueDate: !!data?.dueDate ? data?.dueDate : null,
      status: StockTakingStatus.new,
    } as StockTaking;

    st.creatorId = id;
    st.lastEditorId = id;

    const params = [];
    data?.items.forEach(e => {
      if (e?.variantId) {
        let sti = new StockTakingItem();
        sti = {
          good: e?.correctedGood,
          damaged: e?.correctedDamaged,
          inventoryGood: e?.currentGood,
          inventoryDamaged: e?.currentDamaged,
          inventorySellable: e?.currentGood,
          phyGood: e?.phyGood,
          goodOutShelf: e?.goodOutShelf,
          inProgress: e?.inProgress,
          lastEditorId: id,
          variantId: e?.variantId,
        } as StockTakingItem;

        // sti.stockTaking = st;
        params.push(sti);
      }
    });

    const res = await this.stRepository.save({
      ...st,
      items: params,
    });
    return res;
  }

  async updateStockTakingV2(data: StockTakingV2Dto, id: number, request): Promise<StockTaking> {
    const { warehouses, type, companyId, isAdmin } = request?.user;

    if (!!data?.warehouseId && !warehouses.includes(data?.warehouseId) && !isAdmin)
      throw new Error('Not found warehouse');

    if (
      !CLIENT_STOCK_TAKING_STATUS_EDIT.includes(data?.status.toString()) &&
      type == $enum(UserType).getKeyOrDefault(UserType.customer, null)
    )
      throw new BadRequestException('Status invalid');

    let st = await this.stRepository.findOne({
      where: qb => {
        qb.andWhere({
          id,
          bizId: companyId,
        });
      },
      relations: ['items', 'items.variant'],
    });

    const lookupItems = [];
    const lookupSkus = [];

    if (st.items?.length > 0)
      st.items.forEach(el => {
        lookupItems[el.variantId] = el.id;
        lookupSkus[el.variantId] = el?.variant?.fullSku;
        // console.log(
        //   '🐔  ~ StockTakingService ~ updateStockTakingV2 ~ el.variantId:',
        //   el.variantId,
        //   el?.variant?.fullSku,
        // );
      });
    const currentStatus = st.status;

    if (currentStatus == StockTakingStatus.done) throw new BadRequestException('Status invalid');

    const syncInventory = StockTakingStatus.done == data?.status && data?.status != currentStatus;

    const strStatus = STOCK_TAKING_STATUS[currentStatus];
    const strNewStatus = STOCK_TAKING_STATUS[data?.status];

    console.log(data?.status, currentStatus, NEXT_STOCK_TAKING_STATUS[strStatus]);

    if (
      !!strNewStatus &&
      strNewStatus != strStatus &&
      (!NEXT_STOCK_TAKING_STATUS[strStatus] ||
        (!!NEXT_STOCK_TAKING_STATUS[strStatus] &&
          !NEXT_STOCK_TAKING_STATUS[strStatus].includes(strNewStatus)))
    )
      throw new ForbiddenException('');

    st = {
      ...st,
      ...omit(data, currentStatus == StockTakingStatus.checking ? ['warehouseId', 'clientId'] : []),
      dueDate: !!data?.dueDate ? data?.dueDate : null,
      lastEditorId: request?.user?.id,
    };

    const params = [];

    data?.items.forEach(e => {
      if (e?.variantId) {
        let sti = new StockTakingItem();
        sti = {
          ...omit(e, ['id']),
          lastEditorId: request?.user?.id,
          good: e?.correctedGood,
          damaged: e?.correctedDamaged,
          inventoryGood: e?.currentGood,
          inventoryDamaged: e?.currentDamaged,
          inventorySellable: e?.currentGood,
          phyGood: e?.phyGood,
          goodOutShelf: e?.goodOutShelf,
          inProgress: e?.inProgress,
        };
        if (lookupItems[e.variantId]) sti.id = lookupItems[e.variantId];
        if (
          (currentStatus == StockTakingStatus.checking && !e?.stockTakingId) ||
          (!!e?.stockTakingId && e?.stockTakingId != st.id)
        )
          throw new BadRequestException('Variant invalid');
        if ([StockTakingStatus.new, StockTakingStatus.checking]?.includes(currentStatus))
          params.push(sti);
      }
    });
    if (data?.items?.length != st.items?.length && currentStatus == StockTakingStatus.checking)
      throw new BadRequestException('Variant invalid');
    if ([StockTakingStatus.new, StockTakingStatus.checking]?.includes(currentStatus))
      st.items = params;

    const stockTakingParam = cloneDeep(st);

    if (syncInventory) {
      console.log(st);
      const res = await this.inventoryService.stockTakingInventory(
        st,
        request?.user,
        moment()?.valueOf(),
        lookupSkus,
      );
      console.log('stock-taking-success', res);

      if (res.status == 500) throw new BadRequestException(res);
    }
    console.log(123, stockTakingParam);
    return await this.stRepository.save(stockTakingParam);
  }

  async exportData(query: FilterStockTaking, request, header) {
    const sql = await this.getSQLQueryStockTakingInventory(query, request, header);
    sql.leftJoinAndSelect('st.items', 'items');
    sql.leftJoinAndSelect('items.variant', 'variant');
    sql.leftJoinAndSelect('variant.product', 'product');
    const data = await sql.getMany();
    if (isEmpty(data)) throw new BadRequestException('Data not found!');
    let result = [];
    for (const ele of data) {
      const variant = ele?.items.map(x => ({
        ...x,
        id: ele.id,
        createdAt: ele.createdAt,
        note: ele.note,
        name: ele.name,
        status: ele.status,
        clientId: ele.clientId,
        warehouseId: ele.warehouseId,
        warehouseName: ele?.warehouse?.name,
        creatorId: ele.creatorId,
        code: ele.code,
        dueDate: ele.dueDate,
      }));
      result = result.concat(variant);
    }
    const userIds = [];
    for (const item of result) {
      userIds.push(item?.clientId, item?.creatorId);
    }

    const { data: uData } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: uniq(userIds) },
      timeout: 10000,
    });
    const users = uData as Users[];
    const userLookup: Record<string, Users> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    let param = [];
    let index = 1;
    const mergeConfig = [
      { s: { r: 0, c: 0 }, e: { r: 1, c: 0 } }, // Merge cột A1 và A2
      { s: { r: 0, c: 1 }, e: { r: 1, c: 1 } }, // Merge cột B1 và B2
      { s: { r: 0, c: 2 }, e: { r: 1, c: 2 } }, // Merge cột C1 và C2
      { s: { r: 0, c: 3 }, e: { r: 1, c: 3 } }, // Merge cột D1 và C2
      { s: { r: 0, c: 4 }, e: { r: 1, c: 4 } }, // Merge cột E1 và C2
      { s: { r: 0, c: 5 }, e: { r: 1, c: 5 } }, // Merge cột F1 và C2
      { s: { r: 0, c: 6 }, e: { r: 1, c: 6 } }, // Merge cột G1 và C2
      { s: { r: 0, c: 7 }, e: { r: 1, c: 7 } }, // Merge cột H1 và C2
      { s: { r: 0, c: 8 }, e: { r: 1, c: 8 } }, // Merge cột I1 và C2
      { s: { r: 0, c: 9 }, e: { r: 1, c: 9 } }, // Merge cột J1 và C2
      { s: { r: 0, c: 10 }, e: { r: 1, c: 10 } }, // Merge cột K1 và C2
      { s: { r: 0, c: 11 }, e: { r: 1, c: 11 } }, // Merge cột L1 và C2
      { s: { r: 0, c: 12 }, e: { r: 0, c: 17 } }, // Merge cột M1, N1, O1, P1, Q1, R1
      { s: { r: 0, c: 18 }, e: { r: 0, c: 21 } }, // Merge cột S1, T1, U1
    ];
    param = reduce(
      result,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            index++,
            next?.code ?? '',
            next?.clientId ? userLookup[next?.clientId]?.name : '',
            next?.name ?? '',
            next?.warehouseName ?? '',
            next?.dueDate ? moment(next?.dueDate).format('DD/MM/YY') : '',
            StockTakingStatus[next?.status] ?? '',
            next?.note ?? '',
            moment(next?.createdAt).format('DD/MM/YY') ?? '',
            next?.creatorId ? userLookup[next?.creatorId]?.name : '',
            next?.variant?.sku,
            next?.variant?.product?.name,
            next?.phyGood,
            next?.inProgress,
            next?.inventorySellable,
            next?.good,
            next?.good - next?.inProgress,
            next?.good - next?.inProgress - next?.inventoryGood,
            next?.inventoryDamaged,
            next?.damaged,
            next?.damaged - next?.inventoryDamaged,
          ]);
        }
        return prev;
      },
      [
        [
          'No',
          'StocktakingCode',
          'Client',
          'ReceiptName',
          'Warehouse',
          'DueDate',
          'Status',
          'Note',
          'Created',
          'Creator',
          'SKU',
          'ProductName',
          'SellableOnShelf',
          '',
          '',
          '',
          '',
          '',
          'Damaged',
        ],
        [
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          'PhyGood',
          'InProgress',
          'CurrentSellable',
          'CorrectedPhyGood',
          'NewSellable',
          'SellableDifferences',
          'CurrentDamaged',
          'CorrectedDamaged',
          'DamagedDifference',
        ],
      ],
    );

    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push({
      name: 'Stock Taking',
      data: param,
      options: { '!merges': mergeConfig },
    });
    const buffer = xlsx.build(xlsxSheets);
    return buffer;
  }

  async exportDataDetail(id: number, dto: ExportStockTakingDetailDto, request, header) {
    const { companyId } = request?.user;
    const sql = this.stRepository
      .createQueryBuilder('st')
      .andWhere('st.id = :id', { id })
      .leftJoinAndSelect('st.warehouse', 'warehouse')
      .leftJoinAndSelect('st.items', 'items')
      .leftJoinAndSelect('items.variant', 'variant')
      .leftJoinAndSelect('variant.product', 'product');

    if (!isNil(header)) {
      const whIds = header['warehouse-ids']?.split(',');
      const countryIds = header['country-ids']?.split(',');

      if (!isEmpty(whIds)) sql.andWhere('st.warehouseId IN (:...whIds)', { whIds });
      if (!isEmpty(countryIds))
        sql.andWhere('warehouse.countryCode IN (:...countryIds)', { countryIds });
    }

    if (companyId) {
      sql.andWhere('st.bizId = :companyId', { companyId });
    }

    const data = await sql.getOne();
    if (isEmpty(data)) throw new BadRequestException('Data not found!');

    const userIds = [data?.clientId, data?.creatorId];

    const { data: uData } = await this.amqpConnection.request({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-ids',
      payload: { ids: uniq(userIds) },
      timeout: 10000,
    });
    const users = uData as Users[];
    const userLookup: Record<string, Users> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    let index = 1;
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Stocktaking');
    worksheet.addRow([
      `${dto['StocktakingCode']}`,
      `${data?.code}`,
      `${dto['RecieptName']}`,
      `${data?.name}`,
    ]);
    worksheet.addRow([
      `${dto['Warehouse']}`,
      `${data?.warehouse?.name}`,
      `${dto['ClientName']}`,
      `${userLookup[data?.clientId]?.name}`,
    ]);
    worksheet.addRow([
      `${dto['Status']}`,
      `${dto[StockTakingStatus[data?.status]]}`,
      `${dto['Created']}`,
      `${moment(data?.createdAt).format('DD/MM/YY')}`,
      `${dto['Creator']}`,
      `${userLookup[data?.creatorId]?.name}`,
    ]);
    worksheet.addRow(['Note', `${data?.note ?? ''}`]);
    worksheet.addRow([]);

    worksheet.mergeCells('A6:A7'); // No
    worksheet.mergeCells('B6:B7'); // Image
    worksheet.mergeCells('C6:C7'); // SKU
    worksheet.mergeCells('D6:D7'); // Product Name
    worksheet.mergeCells('E6:J6'); // Sellable On Shelf
    worksheet.mergeCells('K6:M6'); // Damaged

    worksheet.getCell('A6').value = `${dto['No']}`;
    worksheet.getCell('B6').value = `${dto['Image']}`;
    worksheet.getCell('C6').value = `${dto['SKU']}`;
    worksheet.getCell('D6').value = `${dto['ProductName']}`;
    worksheet.getCell('E6').value = `${dto['SellableOnShelf']}`;
    worksheet.getCell('K6').value = `${dto['Damaged']}`;

    worksheet.getCell('E7').value = `${dto['PhyGood']}`;
    worksheet.getCell('F7').value = `${dto['InProgress']}`;
    worksheet.getCell('G7').value = `${dto['CurrentSellable']}`;
    worksheet.getCell('H7').value = `${dto['CorrectedPhyGood']}`;
    worksheet.getCell('I7').value = `${dto['NewSellable']}`;
    worksheet.getCell('J7').value = `${dto['SellableDifferen']}`;

    worksheet.getCell('K7').value = `${dto['CurrentDamaged']}`;
    worksheet.getCell('L7').value = `${dto['CorrectedDamaged']}`;
    worksheet.getCell('M7').value = `${dto['DamagedDifference']}`;

    worksheet.getCell('B1').font = { bold: true };
    worksheet.getCell('D1').font = { bold: true };
    worksheet.getCell('F1').font = { bold: true };
    worksheet.getCell('H1').font = { bold: true };
    worksheet.getCell('B2').font = { bold: true };
    worksheet.getCell('D2').font = { bold: true };
    worksheet.getCell('B3').font = { bold: true };
    worksheet.getCell('D3').font = { bold: true };
    worksheet.getCell('D3').font = { bold: true };
    worksheet.getCell('F3').font = { bold: true };

    const headerCells = [
      'A6',
      'B6',
      'C6',
      'D6',
      'E6',
      'K6',
      'E7',
      'F7',
      'G7',
      'H7',
      'I7',
      'J7',
      'K7',
      'L7',
      'M7',
    ];
    headerCells.forEach(cell => {
      worksheet.getCell(cell).border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    if (!isEmpty(data?.items)) {
      const imagesArray = [];
      const rows = [];
      for (const next of data?.items) {
        rows.push([
          index++,
          '', // Image column (to be filled later)
          next?.variant?.sku,
          next?.variant?.product?.name,
          next?.phyGood,
          next?.inProgress,
          next?.inventorySellable,
          next?.good,
          next?.good - next?.inProgress,
          next?.good - next?.inProgress - next?.inventoryGood,
          next?.inventoryDamaged,
          next?.damaged,
          next?.damaged - next?.inventoryDamaged,
        ]);

        // Add image if available
        if (next?.variant?.images && next?.variant?.images.length > 0) {
          imagesArray.push({
            image: next?.variant?.images[0],
            rowIndex: index + 6,
          });
        }
      }
      // Add all rows at once for better performance
      const addedRows = worksheet.addRows(rows);

      // Apply border for each cell in the added rows
      addedRows.forEach(row => {
        row.eachCell(cell => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        });
      });
      // Save images to the worksheet
      const imageColumn = 2;
      if (imagesArray.length > 0) {
        await saveImageToExcel(imagesArray, workbook, worksheet, imageColumn);
      }
    }
    // Set width for the image column (column B, which is column 2)
    worksheet.getColumn(2).width = 24;

    return await workbook.xlsx.writeBuffer();
  }

  async getSQLQueryStockTakingInventory(
    query: FilterStockTaking,
    request,
    header,
    pagination?: PaginationOptions,
  ) {
    const { warehouses, type, companyId, isAdmin } = request?.user;
    const {
      status,
      clientId,
      warehouseId,
      from,
      to,
      name,
      clientIds,
      statuses,
      warehouseIds,
      variantIds,
      ids,
    } = query;
    const qb = this.stRepository
      .createQueryBuilder('st')
      .leftJoinAndSelect('st.warehouse', 'warehouse')
      .take(pagination?.limit)
      .skip(pagination?.skip)
      .orderBy('st.updatedAt', 'DESC');

    // Điều kiện tìm kiếm theo tên
    if (!isEmpty(ids)) {
      qb.andWhere('st.id IN (:...ids)', { ids });
    }
    if (name) {
      qb.andWhere(
        new Brackets(qr => {
          qr.where('st.name ILIKE :name', { name: `%${name}%` })
            .orWhere('cast(st.id as text) ILIKE :name', { name: `%${name}%` })
            .orWhere('st.code ILIKE :name', { name: `%${name}%` });
        }),
      );
    }

    // Điều kiện theo trạng thái
    if (status) {
      qb.andWhere('st.status = :status', { status });
    }

    if (!!warehouseIds) {
      qb.andWhere('st.warehouseId IN (:...warehouseIds)', { warehouseIds });
    }

    if (!!statuses) {
      qb.andWhere('st.status IN (:...statuses)', { statuses });
    }

    if (!!clientId && type != $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      qb.andWhere('st.clientId = :clientId', { clientId });
    }

    if (warehouseId) {
      qb.andWhere('st.warehouseId = :warehouseId', { warehouseId });
    }

    if (!isNil(header)) {
      const whIds = header['warehouse-ids']?.split(',');
      const countryIds = header['country-ids']?.split(',');

      if (!isEmpty(whIds)) qb.andWhere('st.warehouseId IN (:...whIds)', { whIds });
      if (!isEmpty(countryIds))
        qb.andWhere('warehouse.countryCode IN (:...countryIds)', { countryIds });
    }

    if (companyId) {
      qb.andWhere('st.bizId = :companyId', { companyId });
    }

    if (type == $enum(UserType).getKeyOrDefault(UserType.customer, null)) {
      qb.andWhere('st.clientId = :clientId', { clientId: request?.user?.id });
      qb.andWhere('warehouse.type = :typeWarehouse', { typeWarehouse: WarehouseType.main });
    } else if (type == $enum(UserType).getKeyOrDefault(UserType.employee, null) && !isAdmin) {
      qb.andWhere('st.warehouseId IN (:...warehouses)', { warehouses });
    }

    if (!!clientIds) {
      qb.andWhere('st.clientId IN (:...clientIds)', { clientIds });
    }

    if (!isEmpty(variantIds)) {
      qb.andWhere('items.variant_id IN (:...variantIds)', { variantIds });
    }

    // Điều kiện thời gian
    if (from) qb.andWhere('st.created_at >= :from', { from });
    if (to) qb.andWhere('st.created_at <= :to', { to });

    return qb;
  }
}
