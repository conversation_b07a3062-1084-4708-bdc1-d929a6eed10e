import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  Validate,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import {
  StockTransferPurpose,
  StockTransferStatus,
  StockTransferType,
} from '../catalog/enum/stock-transfer.enum';
export class VariantStockTransferDto {
  @ApiProperty()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @IsEnum(StockTransferType)
  @IsNotEmpty()
  @EnumTransform(StockTransferType)
  type?: StockTransferType;

  @ApiProperty()
  @IsEnum(StockTransferPurpose)
  @IsNotEmpty()
  @EnumTransform(StockTransferPurpose)
  purpose?: StockTransferPurpose;

  @ApiProperty()
  @IsNotEmpty()
  senderVariantId?: number;

  @ApiProperty()
  @IsNotEmpty()
  senderWarehouseId?: number;

  @ApiProperty()
  @IsNotEmpty()
  recipientWarehouseId?: number;

  @ApiProperty()
  @IsNotEmpty()
  recipientVariantId?: number;

  @ApiProperty()
  @IsNotEmpty()
  senderSellable?: number;

  @ApiProperty()
  @IsNotEmpty()
  senderDamaged?: number;

  @ApiProperty()
  @IsOptional()
  recipientSellable?: number;

  @ApiProperty()
  @IsOptional()
  recipientDamaged?: number;

  @ApiProperty()
  @IsNotEmpty()
  transferredSellable?: number;

  @ApiProperty()
  @IsNotEmpty()
  transferredDamaged: number;

  @ApiProperty()
  @IsOptional()
  senderSku: string;

  @ApiProperty()
  @IsOptional()
  recipientSku: string;
}

export class VariantStockTransferProcessingDto extends VariantStockTransferDto {
  @ValidateIf(o => o.senderDamaged < o.transferredDamaged)
  @IsDefined({ message: 'senderDamaged must be more than transferredDamaged' })
  protected readonly checkOnlySellable: undefined;

  @ValidateIf(o => o.senderSellable < o.transferredSellable)
  @IsDefined({ message: 'senderSellable must be more than transferredSellable' })
  protected readonly checkOnlyDamaged: undefined;

  @ValidateIf(
    o =>
      o.type == StockTransferType.transfer &&
      o.purpose == StockTransferPurpose.stockTransferred &&
      o.senderVariantId == o.recipientVariantId &&
      o.senderWarehouseId == o.recipientWarehouseId,
  )
  @IsDefined({ message: 'variant must be valid value' })
  protected readonly checkStockTransferredUniq: undefined;

  @ValidateIf(
    o =>
      o.type == StockTransferType.transfer &&
      o.purpose == StockTransferPurpose.damagedToSellable &&
      (o.senderVariantId != o.recipientVariantId || o.senderWarehouseId != o.recipientWarehouseId),
  )
  @IsDefined({ message: 'variant must be valid value' })
  protected readonly checkDamagedToSellableUniq: undefined;

  @ValidateIf(
    o =>
      o.type == StockTransferType.transfer &&
      o.purpose == StockTransferPurpose.damagedToSellable &&
      o.transferredDamaged < 1,
  )
  @IsDefined({ message: 'transferredDamaged must be more than 0' })
  protected readonly checkDamagedToSellable: undefined;

  @ValidateIf(
    o =>
      ((o.type == StockTransferType.transfer &&
        o.purpose == StockTransferPurpose.stockTransferred) ||
        o.type == StockTransferType.withdrawal) &&
      o.transferredDamaged < 1 &&
      o.transferredSellable < 1,
  )
  @IsDefined({ message: 'transferredDamaged, transferredSellable must be more than 0' })
  protected readonly checkStockTransferred: undefined;
}

export class StockTransferDto {
  @ApiProperty()
  @IsOptional()
  note?: string;

  @ApiProperty()
  @IsNotEmpty()
  name?: string;

  @ApiProperty()
  @IsOptional()
  lastEditorId?: string;

  @ApiProperty()
  @IsOptional()
  creatorId?: string;

  @ApiProperty()
  @IsEnum(StockTransferStatus)
  @IsNotEmpty()
  @EnumTransform(StockTransferStatus)
  status?: StockTransferStatus;

  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(StockTransferStatus)
  @EnumTransform(StockTransferStatus)
  currentStatus?: StockTransferStatus;

  @ApiProperty()
  @IsEnum(StockTransferType)
  @IsNotEmpty()
  @EnumTransform(StockTransferType)
  type?: StockTransferType;

  @ApiProperty()
  @IsEnum(StockTransferPurpose)
  @IsNotEmpty()
  @EnumTransform(StockTransferPurpose)
  purpose?: StockTransferPurpose;

  @ApiProperty()
  @IsNotEmpty()
  senderId: number;

  @ApiProperty()
  @IsNotEmpty()
  senderWarehouseId: number;

  @ApiProperty()
  @IsNotEmpty()
  recipientId: number;

  @ApiProperty()
  @IsNotEmpty()
  recipientWarehouseId: number;

  @ApiProperty()
  @IsOptional()
  companyId?: number;

  @ApiProperty()
  @IsOptional()
  dueDate: Date;

  @IsNotEmpty({ message: 'items product should not be empty' })
  @ArrayNotEmpty({ message: 'items product must be a valid variantStockTransferDto value' })
  @ValidateNested({ each: true })
  @ApiProperty({
    type: VariantStockTransferDto,
    isArray: true,
  })
  @Type((o: any) => {
    if (o?.newObject?.currentStatus === StockTransferStatus.processing)
      return VariantStockTransferProcessingDto;
    return VariantStockTransferDto;
  })
  items: (VariantStockTransferDto | VariantStockTransferProcessingDto)[];

  @ValidateIf(
    o =>
      o.type == StockTransferType.transfer &&
      o.purpose == StockTransferPurpose.damagedToSellable &&
      (o.senderId != o.recipientId || o.senderWarehouseId != o.recipientWarehouseId),
  )
  @IsDefined({ message: 'senderId, senderWarehouseId must be valid value' })
  protected readonly checkOnlyType: undefined;
}
export class ExportDetailDto {
  @ApiProperty()
  @IsNotEmpty()
  StockTransferDetail?: string;

  @ApiProperty()
  @IsNotEmpty()
  StockWithdrawalDetail?: string;

  @ApiProperty()
  @IsNotEmpty()
  TransferCode?: string;

  @ApiProperty()
  @IsNotEmpty()
  WithdrawalCode?: string;

  @ApiProperty()
  @IsNotEmpty()
  Type?: string;

  @ApiProperty()
  @IsNotEmpty()
  RecieptName?: string;

  @ApiProperty()
  @IsNotEmpty()
  Purpose?: string;

  @ApiProperty()
  @IsNotEmpty()
  SenderWarehouse?: string;

  @ApiProperty()
  @IsNotEmpty()
  ClientName?: string;

  @ApiProperty()
  @IsNotEmpty()
  RecipientWarehouse?: string;

  @ApiProperty()
  @IsNotEmpty()
  Status?: string;

  @ApiProperty()
  @IsNotEmpty()
  Created?: string;

  @ApiProperty()
  @IsNotEmpty()
  Creator?: string;

  @ApiProperty()
  @IsNotEmpty()
  Note?: string;

  @ApiProperty()
  @IsNotEmpty()
  No?: string;

  @ApiProperty()
  @IsNotEmpty()
  Image?: string;

  @ApiProperty()
  @IsNotEmpty()
  TransferSKUImage?: string;

  @ApiProperty()
  @IsNotEmpty()
  TransferSKU?: string;

  @ApiProperty()
  @IsNotEmpty()
  ProductName?: string;

  @ApiProperty()
  @IsNotEmpty()
  RecipientSKUImage?: string;

  @ApiProperty()
  @IsNotEmpty()
  RecipientSKU?: string;

  @ApiProperty()
  @IsNotEmpty()
  SenderSellable?: string;

  @ApiProperty()
  @IsNotEmpty()
  TransferedSellable?: string;

  @ApiProperty()
  @IsNotEmpty()
  Recipientaftertransferred?: string;

  @ApiProperty()
  @IsNotEmpty()
  SenderDamaged?: string;

  @ApiProperty()
  @IsNotEmpty()
  TransferedDamaged?: string;

  @ApiProperty()
  @IsNotEmpty()
  SKU?: string;

  @ApiProperty()
  @IsNotEmpty()
  OriginSellable?: string;

  @ApiProperty()
  @IsNotEmpty()
  WithdrewSellable?: string;

  @ApiProperty()
  @IsNotEmpty()
  SellableAfterWithdrew?: string;

  @ApiProperty()
  @IsNotEmpty()
  OriginDamaged?: string;

  @ApiProperty()
  @IsNotEmpty()
  WithdrewDamaged?: string;

  @ApiProperty()
  @IsNotEmpty()
  DamagedAfterWithdrew?: string;

  @ApiProperty()
  @IsNotEmpty()
  transfer?: string;

  @ApiProperty()
  @IsNotEmpty()
  withdrawal?: string;

  @ApiProperty()
  @IsNotEmpty()
  returnToSupplier?: string;

  @ApiProperty()
  @IsNotEmpty()
  clearance?: string;

  @ApiProperty()
  @IsNotEmpty()
  others?: string;

  @ApiProperty()
  @IsNotEmpty()
  stockTransferred?: string;

  @ApiProperty()
  @IsNotEmpty()
  damagedToSellable?: string;

  @ApiProperty()
  @IsNotEmpty()
  new?: string;

  @ApiProperty()
  @IsNotEmpty()
  processing?: string;

  @ApiProperty()
  @IsNotEmpty()
  delivery?: string;

  @ApiProperty()
  @IsNotEmpty()
  done?: string;

  @ApiProperty()
  @IsNotEmpty()
  canceled?: string;
}
