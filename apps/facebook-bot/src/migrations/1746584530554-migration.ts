import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1746584530554 implements MigrationInterface {
  name = 'migration1746584530554';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "datasets" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "id" SERIAL NOT NULL, "dataset_id" character varying NOT NULL, "page_id" character varying NOT NULL, CONSTRAINT "PK_1bf831e43c559a240303e23d038" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_DATASET_PAGE_ID" ON "datasets" ("page_id", "dataset_id") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_DATASET_PAGE_ID"`);
    await queryRunner.query(`DROP TABLE "datasets"`);
  }
}
