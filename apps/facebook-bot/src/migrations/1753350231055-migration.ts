import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1753350231055 implements MigrationInterface {
  name = 'migration1753350231055';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "datasets" ADD "long_term_token" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "datasets" DROP COLUMN "long_term_token"`);
  }
}
