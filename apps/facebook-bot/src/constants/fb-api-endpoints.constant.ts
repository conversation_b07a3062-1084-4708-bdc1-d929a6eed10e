export const FACEBOOK_API_BASE_URL = 'https://graph.facebook.com';

export const FACEBOOK_API_ENDPOINT = {
  OAUTH: () => '/oauth/access_token',
  ME: (id = 'me') => `/${id}`,
  FEED: () => '/me/feed',
  COMMENTS: (id: string) => `/${id}/comments`,
  ACCOUNTS: (id = 'me') => `/${id}/accounts`,
  CONVERSATIONS: (pageId: string) => `/${pageId}/conversations`,
  MESSENGER_PROFILE: (pageId = 'me') => `/${pageId}/messenger_profile`,
  MESSAGES: (conversationId = 'me') => `/${conversationId}/messages`,
  MESSAGES_DETAIL: (id: string) => `/${id}`,
  SUBSCRIBE: () => 'me/subscribed_apps',
  APP_EVENTS: (appId: string) => `/${appId}/page_activities`,
  CONVERSIONS: (datasetId: string) => `/${datasetId}/events`,
  MESSAGE_ATTACHMENTS: (pageId: string) => `/${pageId}/message_attachments`,
  BLOCKED: (pageId: string) => `/${pageId}/blocked`,
  CREATE_DATASET: (pageId: string) => `/${pageId}/dataset`,
};

export const PANCAKE_API_ENDPOINT = {
  CONTENTS: '/contents',
};
