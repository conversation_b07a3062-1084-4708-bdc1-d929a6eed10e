import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { CommonStatus } from 'core/enums/common-status.enum';
import { Redis } from 'ioredis';
import { KeywordConfigurationType } from '../../enums/keyword-configuration-type.enum';
import { FacebookBotService } from '../services/facebook-bot.service';
import { FacebookService } from '../services/facebook.service';
import { KeywordsService } from '../services/keywords.service';
import { ScheduledJobsService } from '../services/scheduled-jobs.service';
import { isNil } from 'lodash';
import { getConnection } from 'typeorm';
import { messageConnection } from '../../../../../core/constants/database-connection.constant';

@Processor('facebook-jobs')
export class FacebookJobsProcessor {
  constructor(
    private sJobsService: ScheduledJobsService,
    private fbBotService: FacebookBotService,
    private fbService: FacebookService,
    private keywordsService: KeywordsService,
    private amqpConnection: AmqpConnection,
    @InjectRedis() private redis: Redis,
  ) {}

  @Process('handle-llm-message')
  async sendLlmMessage(job: Job) {
    await this.amqpConnection.publish('facebook-webhook-event', 'hande-send-llm-message', job.data);
    return true;
  }

  @Process('handle-chatbot-message')
  async sendChatbotMessage(job: Job) {
    console.log('sendChatbotMessage', job.data, Date.now());
    await this.amqpConnection.publish(
      'facebook-webhook-event',
      'handle-send-chatbot-message',
      job.data,
    );
    return true;
  }

  @Process('message-schedule-action')
  async handleMessageScheduleAction(job: Job) {
    const { action, content, source } = job.data || {};
    if (isNil(action) || !content) {
      return;
    }
    console.log('action', action, content);
    await this.amqpConnection.publish('facebook-bot', 'process-queue-action', {
      action,
      content,
      source,
    });
  }

  @Process('scan-scheduled-jobs')
  async scanScheduledJobs(job: Job) {
    // try {
    //   const executeAt = new Date(job.timestamp);
    //   const data = await this.sJobsService.getJobs(null, {executeAt});
    //   const jobs = data[0];
    //
    //   for (const scheduledJob of jobs) {
    //     const {id, action, content} = scheduledJob;
    //     const redisKey = `bot-scan-scheduled-job.${scheduledJob.id}`;
    //     const processing = await this.redis.incr(redisKey);
    //     if (processing > 1) continue;
    //     await this.redis.expire(redisKey, 60);
    //
    //     try {
    //       switch (action) {
    //         case ScheduledAction.sendMessage:
    //           await this.fbBotService.processScheduledMessages(
    //             content as IContentMessage,
    //           );
    //           break;
    //         case ScheduledAction.attachCareScenario:
    //           await this.fbBotService.processScheduledAttachScenario(
    //             content as IContentScenario,
    //             so
    //           );
    //           break;
    //         case ScheduledAction.removeCareScenario:
    //           await this.fbBotService.processScheduledRemoveScenario(
    //             content as IContentScenario,
    //           );
    //           break;
    //         case ScheduledAction.attachTag:
    //           await this.fbBotService.processScheduledAttachTags(
    //             content as IContentTags,
    //           );
    //           break;
    //         case ScheduledAction.removeTag:
    //           await this.fbBotService.processScheduledRemoveTags(
    //             content as IContentTags,
    //           );
    //           break;
    //         default:
    //           break;
    //       }
    //     } catch (error) {
    //       console.log(`handling scheduled job error`, error);
    //     }
    //     await this.sJobsService.deleteJob(id);
    //   }
    // } catch (error) {
    //   console.log(`scan Scheduled Jobs error`, error);
    // }
  }

  @Process('scan-temp-messages')
  async scanGlobalId(job: Job) {
    return this.fbService.scanTempMessage();
  }

  @Process('create-new-partition')
  async createNewPartition() {
    await getConnection(messageConnection)
      .createQueryRunner()
      .query(`SELECT createMessagePartitionIfNotExists((now() + interval '1 month')::DATE)`);
    return 'partioned';
  }

  @Process('scan-trigger-jobs')
  async scanTriggerJobs(job: Job) {
    return {};
    const configs = await this.keywordsService.getKeywordConfigs({
      status: CommonStatus.activated,
      type: KeywordConfigurationType.proactive,
      getGroup: true,
      getPages: true,
    });

    const executePages = [];

    for (const config of configs) {
      console.log('config pages', config.group.pages);
      for (const page of config.group.pages || []) {
        if (!page.isBotEnabled) continue;
        const mIndex = executePages.findIndex(
          obj => obj.pageId === page.id && obj.configId === config.id,
        );
        console.log('mIndex', page);
        if (mIndex === -1)
          executePages.push({
            pageId: page.id,
            configId: config.id,
            configGroupId: config.group.groupId,
          });
      }
    }

    for (const item of executePages) {
      await this.amqpConnection.sendMessage('facebook-bot', null, item, {
        routingKey: 'automation-action-triggered-by-condition',
      });
    }
  }
}
