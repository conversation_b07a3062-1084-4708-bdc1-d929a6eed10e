import {
  AmqpConnection,
  default<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@golevelup/nestjs-rabbitmq';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToInstance } from 'class-transformer';
import { messageConnection } from 'core/constants/database-connection.constant';
import { CommonStatus } from 'core/enums/common-status.enum';
import PhoneUtils from 'core/utils/PhoneUtils';
import { every, find, includes, isEmpty, isNil, some, union, uniq } from 'lodash';
import { getConnection, IsNull, Repository } from 'typeorm';
import { KeywordConfigurationGroup } from '../../entities/keyword-configuration-group.entity';
import { KeywordConfiguration } from '../../entities/keyword-configuration.entity';
import { ScheduledJob } from '../../entities/scheduled-job.entity';
import { KeywordRequirement } from '../../read-entities/keyword-requirement.entity';
import { PassiveConditions } from '../../constants/keyword-configurations.constant';
import { FbMessagingEntry } from '../dtos/facebook-webhook-message.dto';
import {
  CreateKeywordConfigurationDto,
  UpdateKeywordConfigurationDto,
} from '../dtos/keyword-configuration.dto';
import { KeywordCondition } from '../../enums/keyword-condition.enum';
import { KeywordConfigurationType } from '../../enums/keyword-configuration-type.enum';
import { KeywordConfigurationsFilter } from '../filters/keyword-configurations.filter';
import BotUtils from '../utils/BotUtils';
import { ConversationsService } from './conversation.service';
import { FacebookBotService } from './facebook-bot.service';
import { FanPagesService } from './fanpage.service';
import { MessageTag } from '../../entities/message-tag.entity';
import { Message, parseAttachments } from '../../entities/message.entity';
import { IRmqMessage } from 'core/interfaces';
import { MessageEntity } from '../../entities/message-entities.entity';
import { Conversation } from '../../entities/conversation.entity';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { nackErrorsHandler, rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { FanPage } from '../../entities/fanpage.entity';
import { PageScopedUserCareItem } from '../../entities/page-scoped-user-care-item.entity';
import * as moment from 'moment-timezone';
import { BotApiService } from '../../facebook-chat/services/bot.service';
import {
  ChatCompletionContentPart,
  ChatCompletionMessageParam,
} from 'openai/src/resources/chat/completions';
import {
  IMessageInitialData,
  LLM_DEFAULT_PROMPTS,
  LlmService,
} from '../../facebook-chat/services/llm.service';
import { AttachmentsService } from '../../facebook-chat/services/attachments.service';
import { OrderStatus } from 'core/enums/order-status.enum';
import { ConversationOrder } from '../../entities/conversation-order.entity';
import { Comment } from '../../entities/comment.entity';
import { FACEBOOK_API_ENDPOINT } from '../../constants/fb-api-endpoints.constant';
import { FbMessagingType } from '../../enums/fb-message.enum';
import { findAvailableKeys } from '../../facebook-api/functions/uploadAttachments';
import axios from 'axios';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';

@Injectable()
export class KeywordsService {
  constructor(
    @InjectRepository(KeywordConfiguration, messageConnection)
    private keywordRepo: Repository<KeywordConfiguration>,
    @InjectRepository(Message, messageConnection)
    private messageRepository: Repository<Message>,
    @InjectRepository(Comment, messageConnection)
    private commentRepository: Repository<Comment>,
    @InjectRepository(FanPage, messageConnection)
    private fanPageRepository: Repository<FanPage>,
    @InjectRepository(ScheduledJob, messageConnection)
    private scheduledJobRepo: Repository<ScheduledJob>,
    @InjectRepository(KeywordConfigurationGroup, messageConnection)
    private keywordGroupsRepo: Repository<KeywordConfigurationGroup>,
    @InjectRepository(PageScopedUserCareItem, messageConnection)
    private pageScopedUserCareItemRepository: Repository<PageScopedUserCareItem>,
    private fbBotService: FacebookBotService,
    private fanpageService: FanPagesService,
    private conversationService: ConversationsService,
    @InjectQueue('facebook-jobs')
    private jobQueue: Queue,
    private amqpConnection: AmqpConnection,
    private botApiService: BotApiService,
    private llmService: LlmService,
    private attachmentService: AttachmentsService,
    @InjectRepository(Conversation, messageConnection)
    private conversationRepo: Repository<Conversation>,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async getKeywordConfigById(
    id: number,
    request?: Record<string, any>,
  ): Promise<KeywordConfiguration> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const config = await this.keywordRepo.findOne(id, { where: { companyId } });
    if (!config) throw new NotFoundException(`Keyword config ${id} not found`);
    return config;
  }

  async getKeywordConfigs(
    filters?: KeywordConfigurationsFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<KeywordConfiguration[]> {
    const companyId = request?.user?.companyId;

    const mQuery = this.keywordRepo.createQueryBuilder('o');

    const { status, groupIds, assigned, type, getGroup, getPages } = filters || {};
    if (companyId) mQuery.andWhere('o.companyId = :companyId', { companyId });
    if (groupIds) mQuery.andWhere('o.groupId IN (:...groupIds)', { groupIds });
    if (status) mQuery.andWhere('o.status = :status', { status });
    if (type) mQuery.andWhere('o.type = :type', { type });
    if (!isNil(assigned)) {
      if (assigned) mQuery.andWhere('o.groupId IS NOT NULL');
      else mQuery.andWhere('o.groupId IS NULL');
    }
    if (getGroup) mQuery.leftJoinAndSelect('o.group', 'group');
    if (getPages) mQuery.leftJoinAndSelect('group.pages', 'pages');
    mQuery.orderBy('o.createdAt', 'ASC');
    // console.log(`mQuery`, mQuery.getQueryAndParameters())
    const pageConfigGroups: Record<string, FanPage[]> = {};
    const configs = await mQuery.getMany();
    if (getPages) {
      const groupIds = uniq(configs.filter(i => i.group?.isDefault).map(i => i.group.id));
      if (!isEmpty(groupIds)) {
        const groupPages = await this.keywordGroupsRepo
          .createQueryBuilder('g')
          .leftJoinAndSelect('g.group', 'cg')
          .leftJoinAndSelect('cg.pageGroups', 'pg')
          .leftJoinAndSelect('pg.pages', 'p')
          .where('g.id IN (:...groupIds)', { groupIds })
          .andWhere('p.is_bot_enabled = TRUE')
          .getMany();
        for (const groupPage of groupPages) {
          pageConfigGroups[groupPage.id] = union(
            ...(groupPage?.group?.pageGroups?.map(i => i.pages) || []),
          );
        }
        for (const config of configs) {
          if (config.group && pageConfigGroups[config.group.id]) {
            config.group.pages = pageConfigGroups[config.group.id];
          }
        }
        console.log('pageConfigGroups', pageConfigGroups);
      }
    }
    return configs;
  }

  async getKeywordsByIds(ids: number[]): Promise<KeywordConfiguration[]> {
    return await this.keywordRepo.findByIds(ids);
  }

  async createKeywordConfig(
    data: CreateKeywordConfigurationDto,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<KeywordConfiguration> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const { groupId } = data;
    const config = await this.keywordRepo.findOne({
      where: {
        groupId,
        requirements: IsNull(),
      },
    });
    if (config)
      throw new BadRequestException(
        'Mỗi nhóm từ khoá chỉ được thiết lập duy nhất một hành động mặc định khi khách hàng trả lời',
      );

    const nConfig = plainToInstance(KeywordConfiguration, data);
    nConfig.companyId = companyId;

    if (!isNil(data.requirements)) {
      const activeRequirements = data.requirements?.filter(
        req => !PassiveConditions.includes(req.condition),
      );
      if (!isEmpty(activeRequirements)) {
        nConfig.requirements = activeRequirements;
        nConfig.type = KeywordConfigurationType.proactive;
      } else nConfig.type = KeywordConfigurationType.passive;
    }

    return await this.keywordRepo.save(nConfig);
  }

  async updateKeywordConfig(
    id: number,
    data: UpdateKeywordConfigurationDto,
    request?: Record<string, any>,
  ): Promise<KeywordConfiguration> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const config = await this.getKeywordConfigById(id, request);
    if (!config) throw new NotFoundException(`Keyword config ${id} not found`);

    const uConfig = plainToInstance(KeywordConfiguration, {
      ...config,
      ...data,
    });

    if (!isNil(data.requirements)) {
      const activeRequirements = data.requirements?.filter(
        req => !PassiveConditions.includes(req.condition),
      );
      if (!isEmpty(activeRequirements)) {
        uConfig.requirements = activeRequirements;
        uConfig.type = KeywordConfigurationType.proactive;
      } else uConfig.type = KeywordConfigurationType.passive;
    }

    return await this.keywordRepo.save(uConfig);
  }

  async deleteKeywordConfig(
    id: number,
    request?: Record<string, any>,
  ): Promise<KeywordConfiguration> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const config = await this.getKeywordConfigById(id, request);
    if (!config) throw new NotFoundException(`Keyword config ${id} not found`);

    return await this.keywordRepo.remove(config);
  }

  @RabbitRPC({
    exchange: 'facebook-webhook-event',
    routingKey: 'hande-sale-pitching',
    queue: 'agbiz-hande-sale-pitching',
    errorHandler: defaultNackErrorHandler,
  })
  async handleSalePitching(payload: { pageId: string; userId: string }) {
    const { pageId, userId } = payload;
    const data = await this.preloadLlmConfig(pageId, userId, true);
    if (!data) {
      return new Nack();
    }
    const { messages, initConfig, botApi } = data;
    const content = await this.llmService.completeMessage(messages, initConfig);
    if (!content) {
      return new Nack();
    }
    await botApi({
      method: 'POST',
      url: `me/messages`,
      data: {
        message: { text: content },
        message_type: 'MESSAGE_TAG',
        tag: 'ACCOUNT_UPDATE',
        recipient: {
          id: userId,
        },
      },
    });
    return true;
  }

  @RabbitRPC({
    exchange: 'facebook-webhook-event',
    routingKey: 'handle-send-chatbot-message',
    queue: 'agbiz-handle-send-chatbot-message',
    errorHandler: defaultNackErrorHandler,
  })
  async sendChatbotMessage(payload: { pageId: string; userId: string; postId: string }) {
    try {
      console.log('handle-send-chatbot-message start', payload, Date.now());
      const res = await axios.post(
        'https://chatbot.agbiz.vn/chat-page',
        {
          page_id: payload.pageId,
          scoped_user_id: payload.userId,
          post_id: payload.postId || '',
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      if (res.status === 200) {
        console.log('send message to bot', payload);
      } else {
        console.log('send message with status != 200');
      }
    } catch (err) {
      console.log('can not send chatbot message', err);
    }
    console.log('handleSendChatbotMessage finished', Date.now());
    return true;
  }

  @RabbitRPC({
    exchange: 'facebook-webhook-event',
    routingKey: 'hande-send-llm-message',
    queue: 'agbiz-hande-send-llm-message',
    errorHandler: defaultNackErrorHandler,
  })
  async sendLlmMessage(payload: { pageId: string; userId: string; commentId: string }) {
    try {
      const { pageId, userId, commentId } = payload;
      const data = await this.preloadLlmConfig(pageId, userId, false, commentId);
      if (!data) {
        return new Nack();
      }
      const { messages, initConfig, botApi, conversation } = data;
      console.log('send message', messages);
      const content = await this.llmService.completeMessage(messages, initConfig);
      // console.log('content', content);
      // return true;
      if (!conversation?.productId && initConfig.currentProductId) {
        await this.conversationRepo.update(
          {
            pageId,
            scopedUserId: userId,
          },
          { productId: initConfig.currentProductId },
        );
      }
      if (!content) {
        return true;
      }
      if (typeof content === 'string') {
        if (commentId) {
          const date = new Date();
          const res = await botApi({
            method: 'POST',
            url: `${pageId}/messages`,
            params: {
              message: JSON.stringify({ text: content }),
              message_type: 'RESPONSE',
              recipient: JSON.stringify({ comment_id: commentId }),
            },
          });
          await this.messageRepository.save({
            id: res.message_id,
            scopedUserId: userId,
            pageId,
            recipientId: userId,
            senderId: pageId,
            // isEcho: true,
            text: content,
            raw: {
              comment_id: commentId,
            },
            timestamp: date,
          });
        } else {
          const paragraphs = content.split('\n\n');
          for (const paragraph of paragraphs) {
            await botApi({
              method: 'POST',
              url: `me/messages`,
              data: {
                message: { text: paragraph },
                message_type: 'RESPONSE',
                recipient: {
                  id: userId,
                },
              },
            });
            try {
              await botApi({
                method: 'POST',
                url: `me/messages`,
                data: {
                  sender_action: 'typing_on',
                  recipient: {
                    id: userId,
                  },
                },
              });
            } catch (e) {}
            await global.sleep(2000);
          }
          try {
            await botApi({
              method: 'POST',
              url: `me/messages`,
              data: {
                sender_action: 'typing_off',
                recipient: {
                  id: userId,
                },
              },
            });
          } catch (e) {}
        }
      } else {
        try {
          const botApi = await this.botApiService.getPageTokenApi(
            pageId,
            process.env.FACEBOOK_APP_CLIENT_ID,
          );
          const attachments = await this.attachmentService.findAttachments(content);
          const attachment = await this.attachmentService.getPageAttachments2(attachments, pageId);
          const date = new Date();
          for (let i = 0; i < attachment.length; i++) {
            const att = attachment[i];
            const key = findAvailableKeys(att) || 'src';
            const message = await botApi({
              method: 'POST',
              url: FACEBOOK_API_ENDPOINT.MESSAGES(),
              data: {
                messaging_type: FbMessagingType.RESPONSE,
                tag: null,
                recipient: {
                  id: userId,
                },
                message: {
                  attachment: {
                    type: key === 'src' ? att.filetype : key.split('_')[0],
                    payload:
                      key === 'src'
                        ? {
                            url: att[key],
                          }
                        : {
                            attachment_id: att[key],
                          },
                  },
                },
              },
            });
            const mes = await this.messageRepository.save({
              id: message.message_id,
              scopedUserId: userId,
              pageId,
              _attachments: parseAttachments({
                attachments: [
                  {
                    type: att.filetype || key?.replace('_id', ''),
                    payload: {
                      url: att.src,
                    },
                  },
                ],
              }),
              recipientId: userId,
              senderId: pageId,
              // isEcho: true,
              timestamp: date,
            });

            console.log('mess', mes.id);
          }
          const txtMessage = await botApi({
            method: 'POST',
            url: FACEBOOK_API_ENDPOINT.MESSAGES(),
            data: {
              messaging_type: FbMessagingType.RESPONSE,
              recipient: {
                id: userId,
              },
              message: {
                text: 'Dạ, em xin gửi anh/chị hình ảnh ạ',
              },
            },
          });
          const txtMes = await this.messageRepository.save({
            id: txtMessage.message_id,
            scopedUserId: userId,
            pageId,
            _attachments: parseAttachments(txtMessage),
            recipientId: userId,
            senderId: pageId,
            // isEcho: true,
            text: 'Dạ, em xin gửi anh/chị hình ảnh ạ',
            timestamp: date,
          });
          console.log('txtMess', txtMes.id);
          /* const api = await this.botApiService.getApi(pageId);
          await api.sendMessage(
            {
              // body: 'Dạ, em xin gửi anh/chị hình ảnh ạ',
              attachment: attachment,
            },
            conversation.userGlobalId,
          ); */
        } catch (e) {
          console.log('send image error', e);
        }
      }
      return new Nack();
    } catch (e) {
      console.log(e);
      throw e;
    }
  }

  @RabbitRPC({
    exchange: 'facebook-webhook-event',
    routingKey: 'process-new-llm-message',
    queue: 'agbiz-process-new-llm-message',
    errorHandler: nackErrorsHandler,
  })
  async handleNewLLMMessage(payload: { pageId: string; userId: string; postId: string }) {
    const { pageId, userId, postId } = payload;
    if (!pageId || !userId) {
      return new Nack();
    }
    console.log('handleNewLLMMessage', Date.now());
    /* await this.jobQueue.removeJobs(`handle-chatbot-message-${pageId}-${userId}`);
    await this.jobQueue.add(
      'handle-chatbot-message',
      {
        pageId,
        userId,
        postId,
      },
      {
        removeOnComplete: true,
        delay: 3000,
        jobId: `handle-chatbot-message-${pageId}-${userId}`,
      },
    ); */

    const debounceKey = `processing-llm-message-${pageId}-${userId}`;
    const callTimestamp = Date.now();
    const messageDelay = 3000;

    await this.redis.set(debounceKey, callTimestamp);
    await this.redis.expire(debounceKey, 10);

    setTimeout(async () => {
      try {
        const currentTimestamp = await this.redis.get(debounceKey);
        if (currentTimestamp === String(callTimestamp)) {
          await this.amqpConnection.publish(
            'facebook-webhook-event',
            'handle-send-chatbot-message',
            {
              pageId,
              userId,
              postId,
            },
          );
          await this.redis.del(debounceKey);
        } else {
          console.log('ignore this message', payload);
        }
      } catch (error) {
        console.error('error when handle-send-chatbot-message', error);
      }
    }, messageDelay);

    return true;
  }

  @RabbitRPC({
    exchange: 'facebook-webhook-event',
    routingKey: 'process-llm-message',
    queue: 'agbiz-process-llm-message',
    errorHandler: nackErrorsHandler,
  })
  async handleMessage(payload: { pageId: string; userId: string; senderId: string }) {
    const { pageId, userId, senderId } = payload;
    if (!pageId || !userId || !senderId || senderId === pageId) {
      return new Nack();
    }
    const botApi = await this.botApiService.getPageTokenApi(
      pageId,
      process.env.FACEBOOK_APP_CLIENT_ID,
    );
    try {
      await botApi({
        method: 'POST',
        url: `me/messages`,
        data: {
          sender_action: 'typing_on',
          recipient: {
            id: userId,
          },
        },
      });
    } catch (e) {}

    await this.jobQueue.removeJobs(`handle-llm-message-${pageId}-${userId}`);
    await this.jobQueue.add(
      'handle-llm-message',
      {
        pageId,
        userId,
      },
      {
        removeOnComplete: true,
        delay: 3000,
        jobId: `handle-llm-message-${pageId}-${userId}`,
      },
    );

    return true;
  }

  @RabbitRPC({
    exchange: 'facebook-webhook-event',
    routingKey: 'process-keywords',
    queue: 'agbiz-process-keywords-from-fb-message',
    errorHandler: nackErrorsHandler,
  })
  async handlingKeywords(payload: IRmqMessage<FbMessagingEntry>) {
    const message = payload.message;
    if (!message || !message.messaging) return new Nack(false);

    const entryId = message.id;
    const messaging = message.messaging[0];
    const senderId = messaging.sender.id;
    const recipientId = messaging.recipient.id;
    const pageId = entryId;
    const userId = entryId !== senderId ? senderId : recipientId;

    if (!messaging?.message || senderId === pageId) return new Nack(false);

    const textMessage = messaging.message.text;

    // Check if this fanpage whether enabled bot
    const fanpage = await this.fanpageService.getPageById(pageId, false, true);
    if (senderId != pageId) {
      const conversationPost = (
        await getConnection(messageConnection).query(
          `SELECT cr.post_id, cr.scoped_user_id, cr.page_id
         FROM conversations c
         INNER JOIN conversation_referrals cr on cr. page_id = c.page_id and cr.scoped_user_id = c.scoped_user_id
         WHERE c.page_id = $1 AND cr.scoped_user_id= $2`,
          [pageId, userId], // Pass parameters to avoid SQL injection
        )
      )[0];

      console.log('process-llm', conversationPost, { pageId, userId, recipientId, senderId });
      let botPost = null;
      if (conversationPost) {
        botPost = (
          await getConnection(messageConnection).query(
            `SELECT b.id, fb.feed_id
           FROM chatbot.feed_bot fb
           INNER JOIN chatbot.bots b ON b.id = fb.bot_id
           WHERE fb.feed_id = $1 AND b.status = 1`,
            [pageId + '_' + conversationPost.post_id], // Pass parameters to avoid SQL injection
          )
        )[0];
      }

      const botFanpage = botPost
        ? null
        : await getConnection(messageConnection).query(
            `SELECT cf.*
           FROM chatbot.fanpages cf
           INNER JOIN chatbot.bots b ON b.id = cf.bot_id
           WHERE cf.id = $1 AND b.status = 1`,
            [pageId], // Pass parameters to avoid SQL injection
          );

      const canProcess = !isEmpty(botPost) || !isEmpty(botFanpage);
      console.log('can process-new-llm-message', canProcess, Date.now());
      if (canProcess) {
        const [, postId] = (botPost?.feed_id || '').split('_');
        await this.amqpConnection.publish('facebook-webhook-event', 'process-new-llm-message', {
          pageId,
          userId,
          postId: postId || '',
        });
        console.log('process-new-llm-message finished', Date.now());
      }
    }
    if (!fanpage?.isBotWorking) return new Nack(false);

    if (!textMessage) return new Nack(false);
    // const [res] = await new NlpClient().processMessages([textMessage]);
    // let messageTag: MessageTag;
    // console.log('message processed', res);
    // if (res && res.type && res.score >= 0.7) {
    //   if (res.type === 'None') {
    //     res.type = 'unknown';
    //   }
    //   messageTag = await this.messageTagRepository.findOne({ key: res.type }, { select: ['id'] });
    //   if (messageTag) {
    //     const upsert = await this.messageRepository.update(
    //       {
    //         id: messaging.message.mid,
    //       },
    //       {
    //         tagId: messageTag.id,
    //         isTrained: res.score == 1,
    //       },
    //     );
    //     console.log('upsert messageTag', upsert, messageTag);
    //   }
    // } else if (res) {
    //   messageTag = await this.messageTagRepository.findOne({ key: 'unknown' }, { select: ['id'] });
    // }
    // const entities = res?.entities
    //   ?.filter(i => i.accuracy > 0.8 && !isNil(i.resolution.value))
    //   ?.map(i => {
    //     const entity = new MessageEntity();
    //     entity.entity = i.entity;
    //     entity.resolution = i.resolution;
    //     entity.text = i.sourceText;
    //     entity.offset = i.start;
    //     entity.length = i.len;
    //     entity.messageId = messaging.message.mid;
    //     return entity;
    //   });
    // if (!isEmpty(entities)) {
    //   await this.messageEntityRepository.upsert(entities, ['entity', 'messageId', 'offset']);
    // }

    const conversation = await this.conversationService.getConversation(pageId, userId, '');

    // Check if conversation has enabled bot
    if (conversation && !conversation?.isBotEnabled) return new Nack(false);

    // Get keyword configuration group that applied to this page
    let keywordGroupId = fanpage.keywordGroupId;

    if (!keywordGroupId) {
      const defaultKeywordGroup = await this.keywordGroupsRepo.findOne({
        where: {
          groupId: fanpage.group?.configurationGroupId,
          isDefault: true,
        },
      });

      // console.log(`fanpage`, fanpage)
      if (!defaultKeywordGroup) return new Nack(false);

      // console.log(`defaultKeywordGroup`, defaultKeywordGroup)
      keywordGroupId = defaultKeywordGroup.id;
    }

    // Get keyword configurations that applied to this group
    const keywordConfigs = await this.getKeywordConfigs({
      status: CommonStatus.activated,
      type: KeywordConfigurationType.passive,
      groupIds: [keywordGroupId],
    });

    // console.log(`keywordConfigs`, keywordConfigs);
    // return new Nack(false);

    const scheduleJobs: ScheduledJob[] = [];
    for (const config of keywordConfigs) {
      const { requirements, scheduleDuration, actions } = config;
      if (
        !conversation.lastCare &&
        requirements.find(i =>
          [KeywordCondition.includesReasons, KeywordCondition.excludesReasons].includes(
            i.condition,
          ),
        )
      ) {
        conversation.lastCare = await this.pageScopedUserCareItemRepository.findOne({
          where: {
            scopedUserId: conversation.user.id,
          },
          order: {
            updatedAt: 'DESC',
          },
        });
      }
      const isMeetRequirements = this.isMessageMeetRequirements(
        textMessage,
        requirements,
        null,
        null,
        conversation,
        undefined,
        messaging.timestamp,
      );
      console.log(
        `is ${textMessage} is meet requirements`,
        isMeetRequirements,
        config.id,
        requirements,
      );

      if (!isMeetRequirements) continue;

      console.log(`handle config`, config.id, config);
      const source = {
        keywordGroupId: keywordGroupId,
        keywordConfigId: config.id,
        fromMessageId: messaging.message.mid,
        configId: fanpage.group?.configurationGroupId,
      };

      for (const action of actions) {
        const content = BotUtils.parseBotAction(action, pageId, userId);
        if (!scheduleDuration) {
          await this.fbBotService.executeAction(action, content, pageId, userId, source);
        } else {
          // const executeAt = message?.messaging[0]?.timestamp + scheduleDuration;
          // const scheduleJob = plainToInstance(ScheduledJob, {
          //   executeAt,
          //   action: action.action,
          //   content,
          // });
          // scheduleJobs.push(scheduleJob);
          await this.jobQueue.add(
            'message-schedule-action',
            {
              action: action.action,
              content,
              source,
            },
            {
              removeOnComplete: true,
              delay: Math.max(Date.now() + scheduleDuration - message?.messaging[0]?.timestamp, 0),
            },
          );
        }
      }
    }

    // console.log(`scheduleJob`, scheduleJobs);
    if (!isEmpty(scheduleJobs)) await this.scheduledJobRepo.save(scheduleJobs);

    return new Nack(false);
  }

  isMessageMeetRequirements(
    text: string,
    requirements: KeywordRequirement[],
    messageTag?: MessageTag,
    entities?: MessageEntity[],
    conversation?: Conversation,
    orderStatus?: OrderStatus,
    messageTimestamp?: number,
  ): boolean {
    let result = true;
    for (const requirement of requirements) {
      const { condition, keywords } = requirement;
      switch (condition) {
        // When has condition user reply, the message always meets requirements to execute automation
        case KeywordCondition.userReply:
          return true;
        case KeywordCondition.includes: {
          const isIncludes = some(keywords, keyword => {
            return text.search(keyword) !== -1;
          });
          result = result && isIncludes;
          break;
        }
        case KeywordCondition.includesPhone:
          result = result && PhoneUtils.validate(text, false);
          break;
        case KeywordCondition.includesAll:
          result =
            result &&
            every(keywords, keyword => {
              return text.search(keyword) !== -1;
            });
          break;
        case KeywordCondition.excludes:
          result =
            result &&
            every(keywords, keyword => {
              return text.search(keyword) === -1;
            });
          break;
        case KeywordCondition.includesTags:
          result =
            result &&
            some(requirement.tagIds, tagId => {
              return conversation.user.tags?.map(i => i.id)?.includes(tagId);
            });
          break;
        case KeywordCondition.includesReasons:
          result = result && includes(requirement.tagIds, conversation.lastCare?.reasonId);
          break;
        case KeywordCondition.excludesReasons:
          result =
            result &&
            (!conversation.lastCare ||
              !includes(requirement.tagIds, conversation.lastCare?.reasonId));
          break;
        case KeywordCondition.includesAllTags:
          result =
            result &&
            requirement.tagIds?.sort()?.join(',') ===
              conversation.user.tags
                ?.map(i => i.id)
                ?.sort()
                ?.join(',');
          break;
        case KeywordCondition.excludesTags:
          result =
            result &&
            some(requirement.tagIds, tagId => {
              return !conversation.user.tags?.map(i => i.id)?.includes(tagId);
            });
          break;
        case KeywordCondition.messageTag:
          result = result && messageTag && requirement.tagIds.includes(messageTag.id);
          break;
        case KeywordCondition.entity:
          if (!isEmpty(entities)) {
            let entityResult = false;
            if (requirement.entityType === 'number') {
              requirement.entityUnit = null;
            }
            const { entityOperator, entityType, entityUnit } = requirement;
            const entityOperatorValue = Number(requirement.entityOperatorValue);
            for (const entity of entities) {
              let subResult = false;
              if (entity.entity != entityType) {
                continue;
              }
              if (entityUnit && entity.resolution.unit != entityUnit) {
                continue;
              }
              const value = entity.resolution.value;
              switch (entityOperator) {
                case 'eq':
                  if (entityOperatorValue == value) {
                    subResult = false;
                  } else {
                    subResult = true;
                  }
                  break;
                case 'gt':
                  if (entityOperatorValue >= value) {
                    subResult = false;
                  } else {
                    subResult = true;
                  }
                  break;
                case 'gte':
                  if (entityOperatorValue > value) {
                    subResult = false;
                  } else {
                    subResult = true;
                  }
                  break;
                case 'lt':
                  if (entityOperatorValue <= value) {
                    subResult = false;
                  } else {
                    subResult = true;
                  }
                  break;
                case 'lte':
                  if (entityOperatorValue < value) {
                    subResult = false;
                  } else {
                    subResult = true;
                  }
                  break;
              }
              entityResult = entityResult || subResult;
              console.log(
                'entities',
                requirement,
                entity.entity,
                entityOperator,
                entityOperatorValue,
                value,
                entityType,
                entity.resolution.unit,
                entityUnit,
                entityResult,
                result,
              );
              if (subResult) {
                break;
              }
            }
            result = entityResult && result;
          } else {
            result = false;
          }
          break;
        case KeywordCondition.conversationCreationTime:
          result =
            result &&
            conversation.createdAt >= new Date(requirement.conversationCreationTimeRange.from) &&
            conversation.createdAt < new Date(requirement.conversationCreationTimeRange.to);
          break;
        case KeywordCondition.workingTime:
          if (!messageTimestamp) {
            result = false;
            break;
          }
          const [sHour, sMinute, sSecond] = String(requirement.activePeriod.startAt).split(':');
          const [eHour, eMinute, eSecond] = String(requirement.activePeriod.endAt).split(':');

          const timezone = 'Asia/Ho_Chi_Minh';
          const now = moment(messageTimestamp).tz(timezone);
          const botStartAt = now.clone().set({
            hour: Number(sHour),
            minute: Number(sMinute),
            second: Number(sSecond),
          });
          const botEndAt = now.clone().set({
            hour: Number(eHour),
            minute: Number(eMinute),
            second: Number(eSecond),
          });

          const isBetweenTimeRange = !botEndAt.isBefore(botStartAt)
            ? now.isAfter(botStartAt) && botEndAt.isAfter(now)
            : (now.isAfter(botStartAt.clone().subtract(1, 'days')) && botEndAt.isAfter(now)) ||
              (now.isAfter(botStartAt) &&
                botEndAt
                  .clone()
                  .add(1, 'days')
                  .isAfter(now));

          console.log(`isBetweenTimeRange`, isBetweenTimeRange);
          result = result && isBetweenTimeRange;
          break;
        case KeywordCondition.changeOrderStatus:
          result = result && !isNil(orderStatus) && requirement.orderStatuses.includes(orderStatus);
          break;
        default:
          result = false;
          break;
      }
    }
    return result;
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'keyword-trigger-on-order-status-changed',
    queue: 'fb-handle-keyword-trigger-on-order-status-changed',
    errorHandler: rmqErrorsHandler,
  })
  async handleKeywordTriggerOnOrderStatusChanged(payload: {
    id: number;
    status: OrderStatus;
    updatedAt: number;
    updatedBy?: number;
  }) {
    const order = await getConnection(messageConnection)
      .createQueryBuilder(ConversationOrder, 'co')
      .where('co.order_id = :id', { id: payload.id })
      .getOne();
    if (!order || !order.pageId || !order.fbScopedUserId) return new Nack(false);

    const pageId = order.pageId;
    const userId = order.fbScopedUserId;

    // Check if this fanpage whether enabled bot
    const fanpage = await this.fanpageService.getPageById(pageId, false, true);
    if (!fanpage?.isBotWorking) return new Nack(false);

    const conversation = await this.conversationService.getConversation(pageId, userId, '');

    // Check if conversation has enabled bot
    if (conversation && !conversation?.isBotEnabled) return new Nack(false);

    // Get keyword configuration group that applied to this page
    let keywordGroupId = fanpage.keywordGroupId;

    if (!keywordGroupId) {
      const defaultKeywordGroup = await this.keywordGroupsRepo.findOne({
        where: {
          groupId: fanpage.group?.configurationGroupId,
          isDefault: true,
        },
      });

      // console.log(`fanpage`, fanpage)
      if (!defaultKeywordGroup) return new Nack(false);

      // console.log(`defaultKeywordGroup`, defaultKeywordGroup)
      keywordGroupId = defaultKeywordGroup.id;
    }

    // Get keyword configurations that applied to this group
    const keywordConfigs = await this.getKeywordConfigs({
      status: CommonStatus.activated,
      type: KeywordConfigurationType.passive,
      groupIds: [keywordGroupId],
    });

    for (const config of keywordConfigs) {
      const { requirements, scheduleDuration, actions } = config;
      const hasOrderStatusChangeRequirement = find(
        requirements,
        req => req.condition === KeywordCondition.changeOrderStatus,
      );
      if (!hasOrderStatusChangeRequirement) continue;

      if (
        !conversation.lastCare &&
        requirements.find(i =>
          [KeywordCondition.includesReasons, KeywordCondition.excludesReasons].includes(
            i.condition,
          ),
        )
      ) {
        conversation.lastCare = await this.pageScopedUserCareItemRepository.findOne({
          where: {
            scopedUserId: conversation.user.id,
          },
          order: {
            updatedAt: 'DESC',
          },
        });
      }
      const isMeetRequirements = this.isMessageMeetRequirements(
        '',
        requirements,
        undefined,
        [],
        conversation,
        payload.status,
      );
      console.log(
        `is order status of order id ${payload.id} is meet requirements`,
        isMeetRequirements,
        config.id,
        requirements,
      );

      if (!isMeetRequirements) continue;

      console.log(`handle config`, config.id, config);
      const source = {
        keywordGroupId: keywordGroupId,
        keywordConfigId: config.id,
        fromOrderId: payload.id,
        configId: fanpage.group?.configurationGroupId,
      };

      for (const action of actions) {
        const content = BotUtils.parseBotAction(action, pageId, userId);
        if (!scheduleDuration) {
          await this.fbBotService.executeAction(action, content, pageId, userId, source);
        } else {
          await this.jobQueue.add(
            'message-schedule-action',
            {
              action: action.action,
              content,
              source,
            },
            {
              removeOnComplete: true,
              delay: Math.max(
                Date.now() + scheduleDuration - new Date(payload.updatedAt).getTime(),
                0,
              ),
            },
          );
        }
      }
    }

    return new Nack(false);
  }

  private async preloadLlmConfig(
    pageId: string,
    userId: string,
    isPitching = false,
    commentId?: string,
  ) {
    if (!pageId || !userId) {
      return;
    }
    const [fanpage, conversation] = await Promise.all([
      this.fanPageRepository
        .createQueryBuilder('p')
        .select(['p.id', 'p.isBotEnabled'])
        .leftJoinAndSelect(
          'p.aiConfig',
          'aiConfig',
          'aiConfig.country_id = p.country_id AND aiConfig.project_id = p.project_id AND aiConfig.company_id = p.company_id',
        )
        .leftJoinAndSelect(
          'aiConfig.productConfigurations',
          'pConfig',
          'aiConfig.country_id = pConfig.country_id AND aiConfig.project_id = pConfig.project_id AND aiConfig.company_id = pConfig.company_id',
        )
        .where('p.id = :pageId', { pageId })
        .getOne(),
      this.conversationService.getConversation(pageId, userId),
    ]);
    if (
      (conversation && !conversation.isBotEnabled) ||
      !fanpage?.isBotEnabled ||
      !fanpage.aiConfig ||
      !fanpage.aiConfig.status
    ) {
      return;
    }
    if (isEmpty(fanpage.aiConfig.productConfigurations)) {
      console.log('no configs');
      return;
    }
    console.log('handle send llm message', fanpage.aiConfig);

    const botApi = await this.botApiService.getPageTokenApi(
      pageId,
      process.env.FACEBOOK_APP_CLIENT_ID,
    );
    const [oldMessages, comment] = await Promise.all([
      this.messageRepository
        .createQueryBuilder()
        .where({
          scopedUserId: userId,
          pageId,
        })
        .limit(21)
        .orderBy('timestamp', 'DESC')
        .getMany()
        .then(res =>
          res.filter(i => {
            if (
              !i.text &&
              i.senderId !== i.pageId &&
              i.attachments?.find(i => i.type === 'sticker')
            ) {
              i.text = '👍';
              i._attachments = [];
              return true;
            }
            if (!i.text && i.senderId === i.pageId) {
              return false;
            }
            if (!isEmpty(i._attachments)) {
              return false;
              i._attachments = i._attachments.filter(i => i.startsWith('image'));
            }
            return !(isEmpty(i._attachments) && !i.text);
          }),
        ),
      commentId
        ? this.commentRepository
            .createQueryBuilder('c')
            .where(`c.id = :commentId`, { commentId })
            .leftJoinAndMapOne('c.feed', 'c.feed', 'f')
            .select(['c.createdAt', 'c.message', 'f.createdAt', 'f.message'])
            .getOne()
        : null,
    ]);
    const firstMessage = oldMessages.length === 21 ? oldMessages.pop() : null;
    if (
      comment &&
      (firstMessage ? firstMessage.timestamp.getTime() > comment.createdAt.getTime() : true)
    ) {
      if (isEmpty(oldMessages)) {
        oldMessages.splice(
          0,
          0,
          {
            senderId: userId,
            text: comment.message,
            timestamp: comment.createdAt,
          } as Message,
          {
            senderId: pageId,
            text: comment.feed.message,
          } as Message,
        );
      } else {
        for (let i = 0; i < oldMessages.length; i++) {
          const mess = oldMessages[i];
          if (mess.timestamp.getTime() < comment.createdAt.getTime()) {
            oldMessages.splice(
              i,
              0,
              {
                senderId: userId,
                text: comment.message,
                timestamp: comment.createdAt,
              } as Message,
              {
                senderId: pageId,
                text: comment.feed.message,
              } as Message,
            );
            break;
          }
        }
      }
    }
    console.log('mess', oldMessages);
    if (!isPitching ? oldMessages[0]?.senderId !== userId : oldMessages[0]?.senderId === userId) {
      return;
    }
    const messageContext: ChatCompletionMessageParam[] = oldMessages.reverse().map(i => {
      const role = i.senderId == userId ? 'user' : 'assistant';
      if (role === 'user' && !isEmpty(i.attachments)) {
        const content: ChatCompletionContentPart[] = i.attachments.map(i => ({
          type: 'image_url',
          image_url: {
            url:
              i.payload.url.replace('https://content.pancake.vn', 'https://cdn-staging.agbiz.vn/') +
              '?w=500',
            detail: 'low',
          },
        }));
        if (i.text) {
          content.push({
            type: 'text',
            text: i.text,
          });
        }
        return {
          role,
          content: content,
        };
      }
      return {
        role,
        content: i.text,
      };
    });
    const user = conversation?.user;
    const initConfig: IMessageInitialData = {
      scopedUserId: userId,
      pageId,
      botSaleId: -2,
      customerName: user?.name,
      groupConfiguration: fanpage.aiConfig,
      currentProductId: conversation?.productId,
    };
    if (initConfig.currentProductId) {
      initConfig.currentConfiguration = initConfig.groupConfiguration.productConfigurations.find(
        i => i.productId == initConfig.currentProductId,
      );
      if (!initConfig.currentConfiguration) {
        delete initConfig.currentProductId;
      }
    }
    if (fanpage.aiConfig.productConfigurations.length === 1) {
      initConfig.currentConfiguration = fanpage.aiConfig.productConfigurations[0];
      initConfig.currentProductId = initConfig.currentConfiguration.productId;
    }
    const systemPrompt = LLM_DEFAULT_PROMPTS({ gender: user?.gender }, initConfig, isPitching);
    const messages = isPitching
      ? messageContext.concat(systemPrompt)
      : systemPrompt.concat(messageContext);
    return { messages, initConfig, botApi, conversation };
  }
}
