import {
  AmqpConnection,
  defaultN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  RabbitRPC,
} from '@golevelup/nestjs-rabbitmq';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { plainToInstance } from 'class-transformer';
import * as countries from 'core/constants/countries.json';
import { messageConnection } from 'core/constants/database-connection.constant';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import StringUtils from 'core/utils/StringUtils';
import { find, isEmpty, isNil, omit, pick, uniq } from 'lodash';
import * as moment from 'moment-timezone';
import { Equal, getConnection, In, IsNull, LessThan, Not, Repository } from 'typeorm';
import {
  FACEBOOK_API_BASE_URL,
  FACEBOOK_API_ENDPOINT,
} from '../../constants/fb-api-endpoints.constant';
import { ConversationOrder } from '../../entities/conversation-order.entity';
import { Conversation } from '../../entities/conversation.entity';
import { Message, parseAttachments } from '../../entities/message.entity';
import { FbMessagingTag, FbMessagingType } from '../../enums/fb-message.enum';
import { BotApiService } from '../../facebook-chat/services/bot.service';
import { Order } from '../../read-entities/order-api/order.entity';
import {
  FbSendMessagesContent,
  FbSendMessagesDto,
  MessageAttachmentType,
  MessageTemplateType,
} from '../dtos/facebook-send-messages.dto';
import { IFbReceiptElement } from '../dtos/facebook-send-receipt.dto';
import { FbMessagingEntry } from '../dtos/facebook-webhook-message.dto';
import { FbMessagesService } from './facbook-messages.service';
import { FanPagesService } from './fanpage.service';
import { OrderStatus } from 'core/enums/order-status.enum';
import { ConversationOrderItem } from '../../entities/conversation-order-item.entity';
import { CrossCareMode } from '../../enums/cross-care.enum';
import { ScopedUsersService } from './scoped-users.service';
import { FanPage } from '../../entities/fanpage.entity';
import { PixelConversionDto } from '../dtos/pixel-conversion.dto';
import * as crypto from 'crypto';
import { FacebookPixelService } from './facebook-pixel.service';
import { INSTA_APP_ID } from '../../facebook-api';
import { FbActionType } from '../../enums/fb-button.enum';
import { stringify } from 'qs';
import * as fbRecieptTranslation from 'core/localize/fb-reciept.json';
import { Company } from '../../read-entities/identity-api/company.entity';
import { Dataset } from '../../entities/dataset.entity';

@Injectable()
export class FacebookOrdersService {
  constructor(
    @InjectRepository(Conversation, messageConnection)
    private conversationRepo: Repository<Conversation>,
    @InjectRepository(ConversationOrder, messageConnection)
    private conversationOrdersRepo: Repository<ConversationOrder>,
    @InjectRepository(Dataset, messageConnection)
    private datasetRepo: Repository<Dataset>,
    private fbPixelService: FacebookPixelService,
    private messagesService: FbMessagesService,
    private fanpageService: FanPagesService,
    private scopedUsersService: ScopedUsersService,
    private botApiService: BotApiService,
    private amqpConnection: AmqpConnection,
  ) {}

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'order-created',
    queue: 'bot-handle-order-created',
    errorHandler: rmqErrorsHandler,
  })
  async handleFbOrderCreated(payload: Order) {
    if (!payload || !payload.id) return new Nack(false);

    const { data } = await this.amqpConnection.request<{ data: Order }>({
      exchange: 'order-service',
      routingKey: 'get-order-by-id',
      payload: { id: payload.id, relations: ['products'] },
      timeout: 10000,
    });
    if (!data || !data.pageId || !data.fbScopedUserId) return new Nack(false);

    const { id, products, status, confirmedAt, ...rest } = data;

    const order = plainToInstance(ConversationOrder, {
      ...rest,
      orderId: id,
      status: status,
    });

    const items = plainToInstance(ConversationOrderItem, products);

    const connection = getConnection(messageConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const result = await queryRunner.manager
        .createQueryBuilder()
        .insert()
        .into(ConversationOrder)
        .values(order)
        .orUpdate(
          [
            'created_at',
            'creator_id',
            'country_id',
            'project_id',
            'discount',
            'surcharge',
            'shipping_fee',
            'paid',
            'total_price',
          ],
          ['order_id'],
        )
        .execute();
      if (!isEmpty(items))
        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(ConversationOrderItem)
          .values(items)
          .orIgnore()
          .execute();
      console.log(`upsert result when order created`, result);

      await queryRunner.commitTransaction();

      if (
        [OrderStatus.New, OrderStatus.AwaitingStock, OrderStatus.Confirmed].includes(order.status)
      ) {
        await this.amqpConnection.publish(
          'facebook-bot',
          'create-confirmed-care-item-on-order-confirmed',
          { id },
        );
      }

      return result;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'order-edited',
    queue: 'bot-handle-order-edited',
    errorHandler: rmqErrorsHandler,
  })
  async handleFbOrderEdited(payload: Order) {
    if (!payload || !payload.id) return new Nack(false);

    const { data } = await this.amqpConnection.request<{ data: Order }>({
      exchange: 'order-service',
      routingKey: 'get-order-by-id',
      payload: { id: payload.id, relations: ['products'] },
      timeout: 10000,
    });
    if (!data || !data.pageId || !data.fbScopedUserId) return new Nack(false);

    const items = plainToInstance(ConversationOrderItem, data.products);
    const editedProductIds = items.map(p => p.productId);

    const connection = getConnection(messageConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      let order = await queryRunner.manager.findOne(ConversationOrder, { orderId: payload.id });
      if (!order) {
        order = plainToInstance(ConversationOrder, {
          ...omit(data, ['id', 'products', 'confirmedAt']),
          orderId: payload.id,
        });
        if (data.confirmedAt) order.confirmedAt = new Date(data.confirmedAt);

        const result = await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(ConversationOrder)
          .values(order)
          .orUpdate(
            [
              'created_at',
              'creator_id',
              'country_id',
              'project_id',
              'discount',
              'surcharge',
              'shipping_fee',
              'paid',
              'total_price',
            ],
            ['order_id'],
          )
          .execute();
      }

      const overwriteProps: Array<keyof ConversationOrder> = [
        'discount',
        'surcharge',
        'shippingFee',
        'paid',
        'totalPrice',
      ];
      const editedOrderProps = pick(data, overwriteProps);

      const result = await queryRunner.manager.update(
        ConversationOrder,
        { orderId: data.id },
        editedOrderProps,
      );

      await queryRunner.manager.delete(ConversationOrderItem, {
        orderId: data.id,
        productId: Not(In(editedProductIds)),
      });

      if (!isEmpty(items))
        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into(ConversationOrderItem)
          .values(items)
          .orUpdate(
            ['quantity', 'editedPrice', 'priceEditedBy', 'priceAcceptedBy'],
            ['order_id', 'product_id'],
          )
          .execute();

      console.log(`update result when order edited`, result);

      await queryRunner.commitTransaction();
      return new Nack(false);
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'sync-order-status-from-sale',
    queue: 'fb-handle-sync-order-status-from-sale',
    errorHandler: rmqErrorsHandler,
  })
  async handleFbOrderStatusUpdated(payload: {
    id: number;
    status: OrderStatus;
    updatedAt: number;
    updatedBy?: number;
  }) {
    console.log(`syncStatus from AG Sale ====> Fb Bot payload`, payload);
    const { id, status, updatedAt } = payload;
    if (!id || isNil(status) || !updatedAt) {
      console.log(`cannot sync status from AG Sale ====> Fb Bot because not enough information.`);
      return new Nack(false);
    }

    const lastUpdatedAt = new Date(updatedAt);

    const result = await this.conversationOrdersRepo
      .createQueryBuilder()
      .update()
      .where({ orderId: id })
      .andWhere({ status: Not(Equal(status)) })
      .andWhere([{ lastUpdateStatus: LessThan(lastUpdatedAt) }, { lastUpdateStatus: IsNull() }])
      .set({ status, lastUpdateStatus: lastUpdatedAt })
      .execute();
    console.log(`result of sync status from AG Sale ====> Fb bot`, result);

    if ([OrderStatus.New, OrderStatus.AwaitingStock, OrderStatus.Confirmed].includes(status)) {
      await this.amqpConnection.publish(
        'facebook-bot',
        'create-confirmed-care-item-on-order-confirmed',
        { id },
      );
    }

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'send-custom-event-when-fb-order-confirmed',
    queue: 'handle-send-custom-event-when-fb-order-confirmed',
    errorHandler: rmqErrorsHandler,
  })
  async sendCustomEventWhenFbOrderConfirmed({ id, pageId }: { id: number; pageId: string }) {
    if (pageId) {
      const orders = await this.conversationOrdersRepo.find({ pageId, confirmedAt: Not(IsNull()) });
      for (const order of orders) {
        await this.amqpConnection.publish(
          'facebook-bot',
          'send-custom-event-when-fb-order-confirmed',
          { id: order.orderId },
        );
      }
      return {};
    }

    await this.sendCustomEventToFacebook(id, 'fb_mobile_purchase');

    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'send-custom-event-when-fb-order-created',
    queue: 'handle-send-custom-event-when-fb-order-created',
    errorHandler: rmqErrorsHandler,
  })
  async sendCustomEventWhenFbOrderCreated({ id, pageId }: { id: number; pageId: string }) {
    if (pageId) {
      const orders = await this.conversationOrdersRepo.find({
        pageId,
        fbScopedUserId: Not(IsNull()),
        confirmedAt: IsNull(),
        status: OrderStatus.New,
      });
      for (const order of orders) {
        await this.amqpConnection.publish(
          'facebook-bot',
          'send-custom-event-when-fb-order-created',
          { id: order.orderId },
        );
      }
      return {};
    }

    await this.sendCustomEventToFacebook(id, 'fb_mobile_initiate_checkout');

    return new Nack(false);
  }

  async sendCustomEventToFacebook(
    orderId: number,
    eventName: 'fb_mobile_purchase' | 'fb_mobile_initiate_checkout' = 'fb_mobile_purchase',
  ) {
    const { data: order } = await this.amqpConnection.request<{ data: Order }>({
      exchange: 'order-service',
      routingKey: 'get-order-by-id',
      payload: { id: orderId },
      timeout: 10000,
    });

    if (
      !order ||
      !order.pageId ||
      !order.fbScopedUserId ||
      (eventName === 'fb_mobile_purchase' && !order.confirmedAt)
    ) {
      console.log(`error at order`, order.id, order.pageId, order.fbScopedUserId);
      return new Nack(false);
    }
    const tokens = (await this.botApiService.getPageTokens(order.pageId)).filter(
      token => token.appId !== INSTA_APP_ID.toString(),
    );
    const country = find(countries, item => item.dial_code === `+${order.countryId}`);

    const surcharge = order.surcharge || 0;
    const discount = order.discount || 0;
    const shippingFee = order.shippingFee || 0;
    const total_cost = order.totalPrice + shippingFee - discount + surcharge;

    try {
      // const customEvent = JSON.stringify([
      //   {
      //     _eventName: eventName,
      //     _valueToSum: total_cost,
      //     fb_currency: country.currency,
      //   },
      // ]);
      // const form = new FormData();
      // form.append('event', 'CUSTOM_APP_EVENTS');
      // form.append('custom_events', customEvent);
      // form.append('advertiser_tracking_enabled', '1');
      // form.append('application_tracking_enabled', '1');
      // form.append('extinfo', JSON.stringify(['mb1']));
      // form.append('page_id', order.pageId);
      // form.append('page_scoped_user_id', order.fbScopedUserId);
      const body = {
        custom_events: [
          {
            _eventName: eventName,
            _valueToSum: total_cost,
            fb_currency: country.currency,
            event_id: `ag-purchase-${order.id}`,
          },
        ],
        advertiser_tracking_enabled: 1,
        application_tracking_enabled: 1,
        page_id: order.pageId,
        page_scoped_user_id: order.fbScopedUserId,
        logging_source: 'messenger_bot',
        logging_target: 'app_and_page',
      };
      for (const token of tokens) {
        try {
          const data = await axios({
            method: 'POST',
            baseURL: FACEBOOK_API_BASE_URL,
            params: {
              access_token: token.accessToken,
            },
            url: FACEBOOK_API_ENDPOINT.APP_EVENTS(token.appId),
            data: body,
          });
          console.log(
            'send custom event to facebook response',
            eventName,
            order.pageId,
            order.fbScopedUserId,
            data.data,
          );
          if (data.data.success) return true;
        } catch (e) {
          console.log(
            'send custom event to facebook response error',
            eventName,
            order.pageId,
            order.fbScopedUserId,
            e?.response?.data,
          );
        }
      }
    } catch (e) {
      console.log(`send custom event when fb order confirmed`, e);
      throw e;
    }
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'send-pixel-event-when-fb-order-confirmed',
    queue: 'handle-pixel-custom-event-when-fb-order-confirmed',
    errorHandler: rmqErrorsHandler,
  })
  async sendPixelEventWhenFbOrderConfirmed({ id }: { id: number }) {
    const { data: order } = await this.amqpConnection.request<{ data: Order }>({
      exchange: 'order-service',
      routingKey: 'get-order-by-id',
      payload: { id, relations: ['products'] },
      timeout: 10000,
    });

    const bodyRaw = this.parseOrderToPixelConversation(order);
    console.log(
      `sendPixelEventWhenFbOrderConfirmed() body: `,
      order?.pageId,
      order?.fbScopedUserId,
      JSON.stringify(bodyRaw),
    );

    if (!order || !order.confirmedAt || !order.pageId || !order.fbScopedUserId) {
      console.log(`error at order`, order.id, order.pageId, order.fbScopedUserId);
      return new Nack(false);
    }

    const { projectId, companyId } = order;
    const productIds = uniq(order.products?.map(p => p.productId));
    const pixelConfigs = await this.fbPixelService.find(
      { projectIds: [projectId], productIds, hasAccessToken: true, hasDatasetId: true },
      companyId,
    );
    if (isEmpty(pixelConfigs)) return new Nack(false);

    const botApi = await this.botApiService.getPageTokenApi(order.pageId);

    await Promise.all(
      pixelConfigs.map(async config => {
        try {
          const body = this.parseOrderToPixelConversation(order);

          console.log(`send pixel conversion api body`, JSON.stringify(body));

          const rawData = await botApi({
            method: 'POST',
            url: FACEBOOK_API_ENDPOINT.CONVERSIONS(config.datasetId),
            data: body,
          });

          console.log(`send pixel conversion api raw data`, rawData);
        } catch (e) {
          console.log(`send pixel conversion api error`, StringUtils.getString(e));
        }
      }),
    );

    return new Nack(false);
  }

  /**
   * Get or refresh long term token for a page
   * @param pageId - The Facebook page ID
   * @param config - The dataset config
   * @returns The long term token
   */
  private async getLongTermToken(pageId: string, config: Dataset): Promise<string> {
    const now = moment();
    const shouldRefreshToken =
      !config.longTermToken ||
      !config.updatedAt ||
      now.diff(moment(config.updatedAt), 'days') >= 60;

    if (shouldRefreshToken) {
      console.log(`Refreshing long term token for page ${pageId}`);

      // Get current short-lived token from botApi
      const tokens = await this.botApiService.getPageTokens(pageId);
      if (!tokens || tokens.length === 0) {
        throw new Error(`No tokens available for page ${pageId}`);
      }

      const shortLivedToken = tokens.sort()[0].accessToken;
      // Exchange for long term token
      try {
        const response = await axios.post(
          'https://graph.facebook.com/v16.0/oauth/access_token',
          null,
          {
            params: {
              grant_type: 'fb_exchange_token',
              client_id: process.env.FACEBOOK_APP_CLIENT_ID,
              client_secret: process.env.FACEBOOK_APP_CLIENT_SECRET,
              fb_exchange_token: shortLivedToken,
            },
          },
        );

        const longTermToken = response.data.access_token;
        console.log(`Successfully obtained long term token for page ${pageId}`);

        // Update the config with new long term token
        await this.datasetRepo.update(
          { pageId },
          {
            longTermToken,
            updatedAt: now.toDate(),
          },
        );

        return longTermToken;
      } catch (error) {
        console.log(`Failed to get long term token for page ${pageId}:`, error?.response?.data);
        throw error;
      }
    }

    console.log(`Using existing long term token for page ${pageId}`);
    return config.longTermToken;
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'send-capi-when-fb-order-confirmed',
    queue: 'handle-capi-when-fb-order-confirmed',
    errorHandler: rmqErrorsHandler,
  })
  async sendCapiWhenFbOrderConfirmed({ id }: { id: number }) {
    const { data: order } = await this.amqpConnection.request<{ data: Order }>({
      exchange: 'order-service',
      routingKey: 'get-order-by-id',
      payload: { id, relations: ['products'] },
      timeout: 10000,
    });

    if (!order || !order.fbAdsId) {
      console.log(`sendCapiWhenFbOrderConfirmed don't have fbAdsId at orderId `, order.id);
      return new Nack(false);
    }

    if (!order.confirmedAt || !order.pageId || !order.fbScopedUserId) {
      console.log(
        `sendCapiWhenFbOrderConfirmed ERROR: `,
        order.id,
        order.pageId,
        order.fbScopedUserId,
      );
      return new Nack(false);
    }

    let config = await this.datasetRepo.findOne({
      where: {
        pageId: order.pageId,
      },
    });

    const botApi = await this.botApiService.getPageTokenApi(order.pageId);
    if (!config) {
      try {
        const raw = await botApi({
          method: 'POST',
          url: FACEBOOK_API_ENDPOINT.CREATE_DATASET(order.pageId),
        });
        console.log(`sendCapiWhenFbOrderConfirmed create dataset success: `, JSON.stringify(raw));

        config = await this.datasetRepo.save({
          pageId: order.pageId,
          datasetId: raw.id,
        });
      } catch (e) {
        console.log(`sendCapiWhenFbOrderConfirmed ERROR1: `, StringUtils.getString(e));
        return new Nack(false);
      }
    }

    // Get or refresh long term token
    let longTermToken: string;
    try {
      longTermToken = await this.getLongTermToken(order.pageId, config);
    } catch (e) {
      console.log(
        `sendCapiWhenFbOrderConfirmed ERROR getting long term token: `,
        StringUtils.getString(e),
      );
      return new Nack(false);
    }

    try {
      const body = this.parseOrderToPixelConversation(order);
      console.log(`sendCapiWhenFbOrderConfirmed api body`, JSON.stringify(body));

      // Use long term token for the API call
      const rawData = await axios({
        method: 'POST',
        baseURL: FACEBOOK_API_BASE_URL,
        url: FACEBOOK_API_ENDPOINT.CONVERSIONS(config.datasetId),
        params: {
          access_token: longTermToken,
        },
        data: body,
      });
      console.log(`sendCapiWhenFbOrderConfirmed send capi success: `, JSON.stringify(rawData.data));
    } catch (e) {
      console.log(`sendCapiWhenFbOrderConfirmed ERROR2: `, StringUtils.getString(e));
      console.log(`sendCapiWhenFbOrderConfirmed ERROR2 with orderId: `, order.id);
    }

    return new Nack(false);
  }

  parseOrderToPixelConversation(order: Order): PixelConversionDto {
    const country = find(countries, item => item.dial_code === `+${order.countryId}`);

    const surcharge = order.surcharge || 0;
    const discount = order.discount || 0;
    const shippingFee = order.shippingFee || 0;
    const total_cost = order.totalPrice + shippingFee - discount + surcharge - (order.paid || 0);

    const hashedPhone = crypto
      .createHash('sha256')
      .update(order.customerPhone.toLowerCase())
      .digest('hex');
    const hashedName = crypto
      .createHash('sha256')
      .update(order.customerName.toLowerCase())
      .digest('hex');

    return {
      data: [
        {
          event_time: moment(order.createdAt).unix(),
          event_name: 'Purchase',
          action_source: 'physical_store',
          user_data: {
            ph: [hashedPhone],
            fn: [hashedName],
          },
          custom_data: {
            currency: country?.currency || '',
            value: total_cost,
            contents: order.products.map(item => {
              return {
                id: String(item.productDetail.product.id),
                quantity: item.quantity,
              };
            }),
          },
        },
      ],
    };
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'order-confirmed',
    queue: 'bot-handle-order-confirmed',
    errorHandler: defaultNackErrorHandler,
  })
  async handleFbOrderConfirmed({ id }: { id: number }) {
    const { data: order } = await this.amqpConnection.request<{ data: Order }>({
      exchange: 'order-service',
      routingKey: 'get-order-by-id',
      payload: { id, relations: ['products'] },
      timeout: 10000,
    });
    if (
      !order ||
      !order.confirmedAt ||
      !order.fbScopedUserId ||
      !order.pageId ||
      order.fbReceiptMessageId
    )
      return new Nack(false);

    const conversation = await this.conversationRepo.findOne({
      where: {
        scopedUserId: order.fbScopedUserId,
        feedId: '',
      },
    });

    const page = await this.fanpageService.getPageById(order.pageId);
    if (!conversation || !page) return new Nack(false);
    const result = await this.conversationOrdersRepo.upsert(
      {
        orderId: order.id,
        confirmedAt: new Date(order.confirmedAt),
        fbScopedUserId: `${order.fbScopedUserId}`,
        fbGlobalId: order.fbGlobalId ? `${order.fbGlobalId}` : undefined,
        pageId: `${order.pageId}`,
      },
      ['orderId'],
    );
    console.log(`upsert result when order status changed to confirmed.`, result);

    const scopedUserId = order.fbScopedUserId;
    // const content = await this.parseOrderToReceiptContent(order);
    const content = await this.parseOrderToButtonTemplateContent(order);
    if (!content) {
      console.log(`Cannot parse order to receipt content.`);
      return new Nack(false);
    }
    const body: FbSendMessagesDto<IFbReceiptElement> = {
      recipient: {
        id: String(scopedUserId),
      },
      ...content,
      messaging_type: FbMessagingType.MESSAGE_TAG,
      tag: FbMessagingTag.ACCOUNT_UPDATE,
    };
    console.log(`send order receipt body`, body);

    try {
      const timeStamp = new Date();
      const botApi = await this.botApiService.getPageTokenApi(order.pageId);
      const rawData = await botApi({
        method: 'POST',
        url: FACEBOOK_API_ENDPOINT.MESSAGES(),
        data: body,
      });
      console.log(`send order receipt response`, rawData);
      const mId = rawData.message_id;
      await this.amqpConnection.publish('order-service', 'on-fb-receipt-sent', {
        id,
        receipt: rawData,
      });

      // const rawAttachments = [
      //   {
      //     url: null,
      //     type: MessageAttachmentType.template,
      //     title: content.message.attachment.payload.elements[0]?.title,
      //     payload: content.message.attachment.payload,
      //   },
      // ];
      // const raw: FbMessagingEntry = {
      //   id: String(order.pageId),
      //   time: timeStamp.getTime(),
      //   messaging: [
      //     {
      //       sender: {
      //         id: String(order.pageId),
      //       },
      //       message: {
      //         mid: mId,
      //         app_id: 1733556690196497,
      //         is_echo: true,
      //         attachments: rawAttachments,
      //       },
      //       recipient: {
      //         id: String(scopedUserId),
      //       },
      //       timestamp: timeStamp.getTime(),
      //     },
      //   ],
      // };
      // const message = plainToInstance(Message, {
      //   id: mId,
      //   senderId: String(order.pageId),
      //   recipientId: String(scopedUserId),
      //   // isEcho: true,
      //   // raw,
      //   timestamp: timeStamp,
      //   _attachments: parseAttachments(raw),
      //   pageId: String(order.pageId),
      //   scopedUserId: String(scopedUserId),
      //   // type: MessageType.message,
      //   conversation,
      // });

      // await this.messagesService.createMessage(message);
      return rawData;
    } catch (e) {
      console.log(`send order receipt error ee`, e);
      if (e?.driverError) {
        console.log(`send order receipt error`, StringUtils.getString(e?.driverError?.detail));
        return;
      }
      console.log(`send order receipt error`, StringUtils.getString(e));
    }

    return new Nack(false);
  }

  async parseOrderToButtonTemplateContent(
    order: Order,
  ): Promise<FbSendMessagesContent | undefined> {
    const { data: company } =
      (await this.amqpConnection.request<{ data: Company }>({
        exchange: 'identity-service-companies',
        routingKey: 'get-company-by-id',
        payload: { id: order.companyId },
        timeout: 10000,
      })) || {};

    const checksum = crypto
      .createHmac('sha256', process.env.ORDER_TRACKING_SECRET_KEY)
      .update(order.displayId + String(company.id))
      .digest('hex');

    const params = stringify({
      code: order.displayId,
      c: order.companyId,
      checksum,
    });
    // console.log(`query params`, params);

    const url = new URL('order-tracking', String('https://').concat(company.domainName));
    url.search = params;

    const countryCode = order.countryId || 1;
    const text = fbRecieptTranslation[String(countryCode)].text.replace(
      '{{orderId}}',
      order.displayId,
    );
    const buttonTitle = fbRecieptTranslation[String(countryCode)].buttonTitle;

    return {
      message: {
        attachment: {
          type: MessageAttachmentType.template,
          payload: {
            template_type: MessageTemplateType.button,
            text: text,
            buttons: [
              {
                type: FbActionType.web_url,
                title: buttonTitle,
                url: url.href,
              },
            ],
          },
        },
      },
    };
  }

  async parseOrderToReceiptContent(
    order: Order,
  ): Promise<FbSendMessagesContent<IFbReceiptElement> | undefined> {
    const country = find(countries, item => item.dial_code === `+${order.countryId}`);

    let province, district, ward;
    try {
      const { data }: Record<string, any> = await this.amqpConnection.request({
        exchange: 'order-service',
        routingKey: 'get-location',
        payload: {
          wardId: order.addressWardId,
          districtId: order.addressDistrictId,
          provinceId: order.addressProvinceId,
        },
        timeout: 10000,
      });
      province = data.province;
      district = data.district;
      ward = data.ward;
    } catch (error) {
      console.log(
        `cannot find location district ${order.addressDistrictId} province ${order.addressProvinceId} of country ${country.code}`,
      );
    }

    if (!district || !province) return;

    const surcharge = order.surcharge || 0;
    const discount = order.discount || 0;
    const shippingFee = order.shippingFee || 0;
    const total_cost = order.totalPrice + shippingFee - discount + surcharge - (order.paid || 0);

    return {
      message: {
        attachment: {
          type: MessageAttachmentType.template,
          payload: {
            template_type: MessageTemplateType.receipt,
            recipient_name: order.customerName,
            order_number: String(order.id),
            timestamp: String(moment(order.createdAt).unix()),
            currency: country?.currency || '',
            payment_method: total_cost > 0 ? 'Cash' : 'Bank transfer',
            address: {
              street_1: order.addressText,
              street_2: order.customerPhone,
              city: district.name,
              country: country.code,
              postal_code: order.postCode ? String(order.postCode) : ward?.name || '',
              state: province.name,
            },
            summary: {
              subtotal: order.totalPrice,
              shipping_cost: shippingFee,
              total_cost,
            },
            elements: order.products.map(item => {
              let variantName = '';
              for (const property of item.productDetail.properties) {
                if (property.attributes && property.name)
                  variantName += `${property.attributes.name}: ${property.name} `;
              }
              return {
                title: String(item.productDetail.product.name),
                subtitle: variantName,
                quantity: item.quantity,
                price: item.editedPrice || item.price,
              };
            }),
          },
        },
      },
    };
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'fetch-order-conversation-created-at',
    queue: 'handle-fetch-order-conversation-created-at',
    errorHandler: defaultNackErrorHandler,
  })
  async fetchOrderConversationCreatedAt() {
    const qb = this.conversationRepo
      .createQueryBuilder('c')
      .innerJoin(
        ConversationOrder,
        'co',
        `co.page_id = c.page_id
           AND ( co.fb_global_id = c.user_global_id OR co.fb_scoped_user_id = c.scoped_user_id )
           AND co.fb_scoped_user_id IS NOT NULL`,
      )
      .where(`c.feedId = ''`)
      .andWhere('c.created_at IS NOT NULL')
      .select(['c.pageId', 'c.scopedUserId', 'c.feedId', 'c.userGlobalId', 'c.createdAt']);

    const data = await qb.getMany();
    return data;
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'fetch-conversation-created-at',
    queue: 'handle-fetch-conversation-created-at',
    errorHandler: defaultNackErrorHandler,
  })
  async fetchConversationCreatedAt({ pageId, scopedUserId, feedId }) {
    try {
      const qb = this.conversationRepo
        .createQueryBuilder('c')
        .where(`c.pageId = :pageId`, { pageId })
        .andWhere(`c.scopedUserId = :scopedUserId`, { scopedUserId })
        .andWhere(`c.feedId = :feedId`, { feedId })
        .andWhere('c.created_at IS NOT NULL')
        .select(['c.pageId', 'c.scopedUserId', 'c.feedId', 'c.userGlobalId', 'c.createdAt']);

      const data = await qb.getOne();
      return data || {};
    } catch (error) {
      console.log(`fetch conversation created at error: `, StringUtils.getString(error));
      return {};
    }
  }

  @RabbitRPC({
    exchange: 'facebook-bot',
    routingKey: 'request-tls-cross-care',
    queue: 'facebook-bot-handle-request-tls-cross-care',
    errorHandler: rmqErrorsHandler,
  })
  async requestTlsCrossCare({ pageId, scopedUserId, messageId, phone, text }) {
    if (!pageId || !scopedUserId || !messageId || !phone) return new Nack(false);

    const page = await this.fanpageService.findPageById(pageId);
    if (
      !page ||
      !(page instanceof FanPage) ||
      page.crossCareMode !== CrossCareMode.auto ||
      !page.projectId
    )
      return new Nack(false);

    const scopedUser = await this.scopedUsersService.getUserByScopedId(pageId, scopedUserId);
    if (!scopedUser) return new Nack(false);

    await this.amqpConnection.publish('order-service', 'create-cross-care-tls-order', {
      companyId: page.companyId,
      countryId: page.countryId,
      projectId: page.projectId,
      name: scopedUser.name || phone.phone,
      phone: phone.phone,
      scopedUserId,
      pageId,
      fbGlobalId: scopedUser.globalId,
      note: text,
    });

    return new Nack(false);
  }
}
