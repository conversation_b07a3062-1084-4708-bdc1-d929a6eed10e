import { Expose } from 'class-transformer';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

@Entity({
  name: 'datasets',
  database: process.env.DATABASE_MESSAGE,
})
@Index('IDX_DATASET_PAGE_ID', ['pageId', 'datasetId'])
export class Dataset extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id: number;

  @Column({
    name: 'dataset_id',
    type: 'varchar',
  })
  @Expose()
  datasetId: string;

  @Column({
    name: 'page_id',
    type: 'varchar',
  })
  @Expose()
  pageId: string;

  @Column({
    name: 'long_term_token',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  longTermToken?: string;
}
