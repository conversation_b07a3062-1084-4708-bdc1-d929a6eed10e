import { Expose, Type } from 'class-transformer';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { OrderStatus } from 'core/enums/order-status.enum';
import { OrderProduct } from './order-product.entity';

export class Order {
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  createdAt: Date;

  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  updatedAt: Date;

  @Expose()
  id: number;

  @Expose()
  saleId?: number;

  @Expose()
  carePageId?: number;

  @Expose()
  sourceId?: number;

  @Expose()
  carrierId?: number;

  @Expose()
  discount?: number;

  @Expose()
  surcharge?: number;

  @Expose()
  shippingFee?: number;

  @Expose()
  paid?: number;

  @Expose()
  totalPrice?: number;

  @Expose()
  note?: string;

  @Expose()
  status: OrderStatus;

  @Expose()
  creatorId?: number;

  @Expose()
  customerName: string;

  @Expose()
  customerPhone: string;

  @Expose()
  addressText: string;

  @Expose()
  addressNote?: string;

  @Expose()
  addressWard?: string;

  @Expose()
  addressWardId?: string;

  @Expose()
  addressDistrict: string;

  @Expose()
  addressDistrictId: string;

  @Expose()
  addressProvince: string;

  @Expose()
  addressProvinceId: string;

  @Expose()
  postCode?: string;

  @Expose()
  countryId: number;

  @Expose()
  projectId?: number;

  @Type(() => OrderProduct)
  @Expose()
  products: OrderProduct[];

  @Expose()
  @NonEmptyTransform()
  cancelReasonText?: string;

  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  packingAt?: Date;

  @Expose()
  displayId?: string;

  @Expose()
  @NonEmptyTransform()
  lastUpdatedBy?: number;

  @Expose()
  isCheckedAfterReturn?: boolean;

  @Expose()
  partnerId?: string;

  @Expose()
  pageId?: string;

  @Expose()
  pancakeConversationId?: number;

  @Expose()
  sourceDetail?: Record<string, any>;

  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  confirmedAt?: Date;

  @Expose()
  pfgId?: number;

  @Expose()
  @NonEmptyTransform()
  fbGlobalId?: string;

  @Expose()
  @NonEmptyTransform()
  fbScopedUserId: string;

  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  expectDeliveryAt?: Date;

  @Expose()
  @NonEmptyTransform()
  fbReceiptMessageId?: string;

  @Expose()
  companyId?: number;

  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  lastUpdateStatus: Date;

  @Expose()
  fbAdsId: string;
}
