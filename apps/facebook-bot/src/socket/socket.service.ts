import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { instanceTo<PERSON>lain } from 'class-transformer';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { messageConnection } from 'core/constants/database-connection.constant';
import { getConnection, Repository, SelectQueryBuilder } from 'typeorm';
import { Conversation } from '../entities/conversation.entity';
import { FanPage } from '../entities/fanpage.entity';
import { Message } from '../entities/message.entity';
import { ISocketEvent, ISocketRoom, SocketGateway } from './socket.gateway';
import { isEmpty } from 'lodash';
import { ConversationsPhones } from '../entities/conversations-phones.entity';
import StringUtils from 'core/utils/StringUtils';
import { PageScopedUser } from '../entities/page-scoped-user.entity';
import { AmqpConnection, Nack, RabbitRPC } from '@golevelup/nestjs-rabbitmq';
import { rmqErrorsHandler } from '../../../../core/handlers/rmq-errors.handler';

@Injectable()
export class SocketService {
  constructor(
    @InjectRepository(Conversation, messageConnection)
    private conversationRepo: Repository<Conversation>,
    @InjectRepository(FanPage, messageConnection)
    private pageRepo: Repository<FanPage>,
    private socketGateway: SocketGateway,
    private redisCache: RedisCacheService,
    private amqpConnection: AmqpConnection,
  ) {}

  public emit(
    room: ISocketRoom,
    roomId: string | number,
    event: ISocketEvent,
    message: any,
  ): boolean {
    // console.log(`socket gate way emit`, room, roomId, event, message);
    return this.socketGateway.emit(room, roomId, event, instanceToPlain(message));
  }

  public async emitUserReadConversation(
    pageId: string,
    userId: string,
    userReadAt: Date,
  ): Promise<boolean> {
    try {
      // this.emit(ISocketRoom.page, pageId, ISocketEvent.userReadConversation, {
      //   pageId,
      //   userId,
      //   userReadAt,
      // });
      const key = `fanpage.${pageId}`;
      let page: FanPage = await this.redisCache.get(key);
      if (!page) {
        page = await this.pageRepo.findOne(pageId);
        await this.redisCache.set(key, page);
      }
      if (page?.groupId) {
        this.emit(ISocketRoom.pageGroup, page.groupId, ISocketEvent.userReadConversation, {
          pageId,
          userId,
          userReadAt,
        });
      }
      return true;
    } catch (error) {
      console.log('emit user read conversation error', error);
    }
    return;
  }

  getFullConversationQuery(): SelectQueryBuilder<Conversation> {
    const qb = this.conversationRepo
      .createQueryBuilder('c')
      .leftJoinAndMapMany(
        'c.phones',
        ConversationsPhones,
        'phones',
        `"phones"."page_id" = "c"."page_id" 
    AND "phones"."scoped_user_id" = "c"."scoped_user_id" 
    AND ("phones"."feed_id" = "c"."feed_id" OR  ("phones"."feed_id" IS NULL AND "c"."feed_id" = ''))`,
      )
      .leftJoinAndMapOne('c.page', FanPage, 'p', 'p.id = c.pageId')
      .leftJoinAndMapOne(
        'c.user',
        PageScopedUser,
        'u',
        'u.scopedUserId = c.scopedUserId AND c.pageId = u.pageId',
      )
      .leftJoinAndSelect('u.tags', 'tags')
      .leftJoin('u.cares', 'cares')
      .addSelect(['cares.id', 'cares.status', 'cares.createdAt', 'cares.scopedUserId'])
      .leftJoin('cares.items', 'ci')
      .addSelect(['ci.id', 'ci.reasonId', 'ci.createdAt'])
      .leftJoin('ci.reason', 'reason')
      .addSelect(['reason.id', 'reason.key']);
    return qb;
  }

  public async emitConversation(pageId: string, userId: string, feedId: string): Promise<boolean> {
    try {
      const qb = this.getFullConversationQuery()
        .andWhere('c.pageId = :pageId', { pageId })
        .andWhere('c.scopedUserId = :userId', { userId })
        .andWhere('c.feedId = :feedId', { feedId });
      const conversation = await qb.getOne();

      if (!conversation) return;

      return this.emitConversations([conversation]);
    } catch (e) {
      console.log('emit conversation error', e);
    }
  }

  public async emitConversations(conversations: Conversation[]): Promise<boolean> {
    if (isEmpty(conversations)) {
      return false;
    }
    const pageId = conversations[0].pageId;
    const key = `fanpage.${pageId}`;
    let page: FanPage = await this.redisCache.get(key);
    if (!page) {
      page = await this.pageRepo.findOne(pageId);
      await this.redisCache.set(key, page);
    }
    for (const conversation of conversations) {
      this.emit(ISocketRoom.page, pageId, ISocketEvent.conversation, conversation);
      // if (page?.groupId) {
      //   this.emit(ISocketRoom.pageGroup, page.groupId, ISocketEvent.conversation, conversation);
      // }
    }
    try {
      const connection = getConnection(messageConnection);
      // const rawRecord = await connection.manager.query(
      //   `SELECT COUNT(*) AS count
      //    FROM
      //        conversations
      //    WHERE
      //        page_id = $1
      //      AND unread = true
      //    GROUP BY
      //        page_id`,
      //   [pageId],
      // );
      // const pageUnreads = rawRecord[0]?.count ? Number(rawRecord[0]?.count) : 0;

      // this.emit(ISocketRoom.page, pageId, ISocketEvent.unreadConversations, pageUnreads);

      // if (page?.groupId) {
      //   const rawRecord = await connection.manager.query(
      //     `SELECT COUNT(*) AS count
      //      FROM
      //          conversations c
      //          LEFT JOIN fanpage p
      //      ON p.id = c.page_id
      //      WHERE
      //          unread = TRUE
      //        AND group_id = $1
      //      GROUP BY
      //          group_id`,
      //     [page.groupId],
      //   );
      //   const groupUnreads = rawRecord[0]?.count ? Number(rawRecord[0]?.count) : 0;
      //   this.emit(
      //     ISocketRoom.pageGroup,
      //     page.groupId,
      //     ISocketEvent.unreadConversations,
      //     groupUnreads,
      //   );
      // }

      return true;
    } catch (error) {
      console.log(`emit conversation error`, error);
    }

    return;
  }

  public async emitFacebookMessage(message: Message): Promise<boolean> {
    const pageId = message.pageId;

    // const key = `fanpage.${pageId}`;
    // let page: FanPage = await this.redisCache.get(key);
    // if (!page) {
    //   page = await this.pageRepo.findOne(pageId);
    //   await this.redisCache.set(key, page);
    // }

    // if (page.groupId) {
    //   const result = this.emit(ISocketRoom.pageGroup, page.groupId, ISocketEvent.message, message);
    // }

    return this.emit(ISocketRoom.page, pageId, ISocketEvent.message, message);
  }

  public async emitScopedUser(userId: string): Promise<boolean> {
    try {
      const [pageId, scopedUserId] = userId.split('_');
      const qb = this.getFullConversationQuery()
        .where('c.pageId = :pageId AND c.scopedUserId = :scopedUserId', { pageId, scopedUserId })
        .andWhere(`c.feedId = ''`);
      const conversations = await qb.getMany();

      if (isEmpty(conversations)) return;

      // const key = `fanpage.${pageId}`;
      // let page: FanPage = await this.redisCache.get(key);
      // if (!page) {
      //   page = await this.pageRepo.findOne(pageId);
      //   await this.redisCache.set(key, page);
      // }
      for (const conversation of conversations) {
        this.emit(ISocketRoom.page, pageId, ISocketEvent.conversation, conversation);
        // if (page?.groupId) {
        //   this.emit(ISocketRoom.pageGroup, page.groupId, ISocketEvent.conversation, conversation);
        // }
      }
    } catch (error) {
      console.log(`emit scoped user error`, StringUtils.getString(error));
    }

    return;
  }

  @RabbitRPC({
    exchange: 'message-service',
    routingKey: 'after-lead-updated',
    queue: 'message-service-after-lead-updated',
    errorHandler: rmqErrorsHandler,
  })
  async afterOrderLeadUpdated({ leadId }) {
    this.emit(ISocketRoom.lead, leadId, ISocketEvent.leadUpdated, leadId);
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'message-service',
    routingKey: 'lead-distributed',
    queue: 'message-service-lead-distributed',
    errorHandler: rmqErrorsHandler,
  })
  async leadDistributed(countLeadDistributedForEachUsers) {
    console.log('Send notification to user distributed leads');
    this.emit(
      ISocketRoom.leadDistributed,
      'leadDistributed',
      ISocketEvent.leadDistributed,
      countLeadDistributedForEachUsers,
    );
    return new Nack(false);
  }

  @RabbitRPC({
    exchange: 'message-service',
    routingKey: 'lead-gather',
    queue: 'message-service-lead-gather',
    errorHandler: rmqErrorsHandler,
  })
  async leadGather(leadIds) {
    console.log('Send notification to lead gather', leadIds);
    this.emit(
      ISocketRoom.leadGather,
      'leadGather',
      ISocketEvent.leadGather,
      leadIds,
    );
    return new Nack(false);
  }
}
