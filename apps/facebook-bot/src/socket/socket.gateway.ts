import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { instanceToPlain } from 'class-transformer';
import { verify } from 'jsonwebtoken';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq/lib/amqp/connection';
import { User } from 'core/entities/identity/user.entity';
import { Injectable } from '@nestjs/common';
import { isArray, isEmpty } from 'lodash';
import { InjectRepository } from '@nestjs/typeorm';
import { FanPage } from '../entities/fanpage.entity';
import { messageConnection } from '../../../../core/constants/database-connection.constant';
import { Repository } from 'typeorm';
import { createRequestContext } from '../../../../core/hooks/request-context.hook';
import { AuthService } from 'core/auth/services/auth.service';

// interface INamespace extends Namespace {
//   adapter: IAdapter;
// }

interface ISocketUser extends Socket {
  user: User;
}

// interface IHookRequest {
//   type: HookTypes;
//   data: any;
// }

// enum HookTypes {
//   getUserIds,
// }

// const BOT_LIMITS = [
//   [200, 5],
//   [500, 3],
//   [1000, 2],
// ];

export enum ISocketRoom {
  page = 'page',
  pageGroup = 'pageGroup',
  all = 'all',
  lead = 'lead',
  leadDistributed = 'leadDistributed',
  leadGather = 'leadGather',
}

export enum ISocketEvent {
  conversation = 'conversation',
  message = 'message',
  unreadConversations = 'unreadConversations',
  userReadConversation = 'userReadConversation',
  scopedUsersManuallyGathered = 'scopedUsersManuallyGathered',
  leadUpdated = 'leadUpdated',
  leadDistributed = 'leadDistributed',
  leadGather = 'leadGather',
}

export const getRoomName = (entity: ISocketRoom, id: string | number): string =>
  `${process.env.REDIS_DB_NUMBER}${entity}:${id}`;

@WebSocketGateway(8080, {
  transports: ['websocket', 'polling'],
  path: '/',
  cors: {
    origin: '*',
    credentials: true,
    allowedHeaders: '*',
  },
})
@Injectable()
export class SocketGateway implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit {
  @WebSocketServer() private server: Server;

  constructor(
    private readonly amqpConnection: AmqpConnection, // private fbService: FacebookService
    @InjectRepository(FanPage, messageConnection)
    private pageRepo: Repository<FanPage>,
    private authService: AuthService,
  ) {
  }

  async afterInit() {
    this.server.use((socket, next) => {
      socket.use((event, next) => {
        createRequestContext({req: socket.handshake});
        next();
      });
      next();
    });
  }

  public async getGroupPages(id: number): Promise<string[]> {
    const res = await this.pageRepo.find({
      where: { groupId: id },
      select: ['id'],
    });
    return res.map(i => i.id);
  }

  public handleDisconnect(client) {
    const user = client.user;
  }

  public async handleConnection(client: ISocketUser, ...args: any[]) {
    let userId = null, user;
    try {
      user =
      client.handshake.auth.token &&
      verify(client.handshake.auth.token, process.env.SECRET_KEY_BASE);
      userId = user.sub;
      user.id = userId;
    } catch (e) {
    }
    if (!userId) {
      client.disconnect();
      return;
    }
    try {
      if(client.handshake.auth.extraHeaders) {
        client.handshake.headers = {
          ...(client.handshake.headers || {}),
          ...client.handshake.auth.extraHeaders
        };
      }
      const profiles = await this.authService.checkUserProfiles(
        userId,
        [],
        client.handshake.auth.extraHeaders?.['country-ids'],
        client.handshake.auth.extraHeaders?.['project-ids'].split(',')
      );
      if (isEmpty(profiles)) {
        client.disconnect();
        return;
      }
      user.profiles = profiles;
      client.user = user;
      (client.handshake as any).user = user;
      await client.join(ISocketRoom.all);
    } catch (e) {
      client.disconnect();
    }
  }

  public emit(
    room: ISocketRoom,
    roomId: string | number,
    event: ISocketEvent,
    message: any,
  ): boolean {
    const roomName = getRoomName(room, roomId);
    return this.server
      .to(`${roomName}`)
      .emit(
        event,
        typeof message === 'string' ? message : instanceToPlain(message),
      );
  }

  @SubscribeMessage('leave')
  private async leave(
    @ConnectedSocket() client: ISocketUser,
    @MessageBody() {room, id}: { room: ISocketRoom; id: any },
  ): Promise<any> {
    await client.leave(getRoomName(room, id));
    return client.user;
  }

  @SubscribeMessage('join')
  private async join(
    @ConnectedSocket() client: ISocketUser,
    @MessageBody() {room, id}: { room: ISocketRoom; id: any },
  ): Promise<any> {
    const currentRooms = client.rooms;
    for (const roomName of currentRooms) {
      if (roomName.startsWith(`${process.env.REDIS_DB_NUMBER}${room}`)) {
        await client.leave(roomName);
      }
    }
    if (room === ISocketRoom.pageGroup) {
      const pages = await this.getGroupPages(id);
      console.log('currentRooms', currentRooms);
      for (const roomName of currentRooms) {
        if (roomName.startsWith(`${process.env.REDIS_DB_NUMBER}${ISocketRoom.page}`)) {
          await client.leave(roomName);
        }
      }
      console.log(client.user.id + ' join pages', pages);
      await client.join(pages.map(i => getRoomName(ISocketRoom.page, i)));
      // return client.user;
    }
    if (isArray(id)) {
      await client.join(id.map(i => getRoomName(room, i)));
    } else await client.join(getRoomName(room, id));
    return client.user;
  }
}
