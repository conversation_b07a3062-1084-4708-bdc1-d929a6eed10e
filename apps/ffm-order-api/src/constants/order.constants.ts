import { CarrierCode, CountryID } from 'core/enums/carrier-code.enum';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { TypeTemplateDownloadOrder } from '../enums/order-status.enum';
export const COUNTRY_PANCAKE_100 = [CountryID.TH, CountryID.MY];

export const NEXT_STATUS_ORDER = {
  [OrderFFMStatus.Draft]: [OrderFFMStatus.Canceled, OrderFFMStatus.New],
  [OrderFFMStatus.New]: [OrderFFMStatus.Confirmed, OrderFFMStatus.Canceled],
  [OrderFFMStatus.AwaitingStock]: [OrderFFMStatus.Reconfirm, OrderFFMStatus.Canceled],
  [OrderFFMStatus.Reconfirm]: [OrderFFMStatus.Confirmed, OrderFFMStatus.Canceled],
  [OrderFFMStatus.Confirmed]: [OrderFFMStatus.Canceled],
  [OrderFFMStatus.AwaitingCollection]: [OrderFFMStatus.Collecting, OrderFFMStatus.Canceled],
  [OrderFFMStatus.Collecting]: [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.Canceled],
  [OrderFFMStatus.Awaiting3PLPickup]: [
    OrderFFMStatus.LostByWH,
    OrderFFMStatus.DamagedByWH,
    OrderFFMStatus.Canceled,
  ],
  [OrderFFMStatus.PickedUp3PL]: [
    OrderFFMStatus.InTransit,
    OrderFFMStatus.InDelivery,
    OrderFFMStatus.LostBy3PL,
  ],
  [OrderFFMStatus.InTransit]: [
    OrderFFMStatus.Stocked3PL,
    OrderFFMStatus.InDelivery,
    OrderFFMStatus.LostBy3PL,
    OrderFFMStatus.AwaitingReturn,
  ],
  [OrderFFMStatus.Stocked3PL]: [
    OrderFFMStatus.InTransit,
    OrderFFMStatus.InDelivery,
    OrderFFMStatus.LostBy3PL,
    OrderFFMStatus.AwaitingReturn,
  ],
  [OrderFFMStatus.InDelivery]: [
    OrderFFMStatus.AwaitingReturn,
    OrderFFMStatus.Delivered,
    OrderFFMStatus.FailedDelivery,
    OrderFFMStatus.LostBy3PL,
  ],
  [OrderFFMStatus.FailedDelivery]: [
    OrderFFMStatus.Delivered,
    OrderFFMStatus.AwaitingReturn,
    OrderFFMStatus.InReturn,
    OrderFFMStatus.LostBy3PL,
  ],
  [OrderFFMStatus.Delivered]: [OrderFFMStatus.DeliveredCompleted],
  [OrderFFMStatus.AwaitingReturn]: [
    OrderFFMStatus.InReturn,
    OrderFFMStatus.LostBy3PL,
    OrderFFMStatus.InDelivery,
  ],
  [OrderFFMStatus.InReturn]: [
    OrderFFMStatus.Returned,
    OrderFFMStatus.LostBy3PL,
    OrderFFMStatus.AwaitingReturn,
  ],
  [OrderFFMStatus.Returned]: [
    OrderFFMStatus.ReturnedStocked,
    OrderFFMStatus.ReturnedDamagedBy3PL,
    OrderFFMStatus.DamagedByWH,
    OrderFFMStatus.LostBy3PL,
    OrderFFMStatus.LostByWH,
  ],
  [OrderFFMStatus.ReturnedStocked]: [OrderFFMStatus.ReturnedCompleted],
  [OrderFFMStatus.ReturnedCompleted]: [],
  [OrderFFMStatus.DeliveredCompleted]: [],
  [OrderFFMStatus.ReturnedDamagedBy3PL]: [
    OrderFFMStatus.DamagedByWH,
    OrderFFMStatus.DamagedCompleted,
  ],

  [OrderFFMStatus.LostBy3PL]: [OrderFFMStatus.LostCompleted],
  [OrderFFMStatus.DamagedByWH]: [
    OrderFFMStatus.DamagedCompleted,
    OrderFFMStatus.ReturnedDamagedBy3PL,
  ],
  [OrderFFMStatus.LostByWH]: [OrderFFMStatus.LostBy3PL, OrderFFMStatus.LostCompleted],
  [OrderFFMStatus.LostCompleted]: [],
  [OrderFFMStatus.Canceled]: [],
};

export const REVERT_STATUS_ORDER = {
  [OrderFFMStatus.PickedUp3PL]: [OrderFFMStatus.Awaiting3PLPickup],
  [OrderFFMStatus.Delivered]: [OrderFFMStatus.Returned, OrderFFMStatus.InDelivery],
  [OrderFFMStatus.AwaitingReturn]: [OrderFFMStatus.InDelivery],
  // [OrderFFMStatus.InReturn]: [OrderFFMStatus.InDelivery],
  [OrderFFMStatus.Returned]: [OrderFFMStatus.Delivered],
  [OrderFFMStatus.ReturnedDamagedBy3PL]: [OrderFFMStatus.InReturn, OrderFFMStatus.InDelivery], //DamagedBy3PL
  [OrderFFMStatus.DamagedByWH]: [OrderFFMStatus.InReturn, OrderFFMStatus.InDelivery],
  [OrderFFMStatus.LostBy3PL]: [OrderFFMStatus.InReturn, OrderFFMStatus.InDelivery],
  [OrderFFMStatus.LostByWH]: [OrderFFMStatus.InReturn, OrderFFMStatus.InDelivery],
};

export const EXTENDED_UPDATE_STATUS_ORDER = {
  [OrderFFMStatus.AwaitingCollection]: [OrderFFMStatus.Collecting, OrderFFMStatus.PickedUp3PL],
  [OrderFFMStatus.Collecting]: [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.PickedUp3PL],
  [OrderFFMStatus.Awaiting3PLPickup]: [OrderFFMStatus.PickedUp3PL],
};

export const NEXT_STATUS_ORDER_RECONCILIATION = {
  [OrderFFMStatus.Delivered]: OrderFFMStatus.DeliveredCompleted,
  [OrderFFMStatus.ReturnedStocked]: OrderFFMStatus.ReturnedCompleted,
  [OrderFFMStatus.LostBy3PL]: OrderFFMStatus.LostCompleted,
  [OrderFFMStatus.LostByWH]: OrderFFMStatus.LostCompleted,
  [OrderFFMStatus.ReturnedDamagedBy3PL]: OrderFFMStatus.DamagedCompleted,
  [OrderFFMStatus.DamagedByWH]: OrderFFMStatus.DamagedCompleted,
};

export const PRINT_WAYBILL_ORDER = [
  OrderFFMStatus.Draft,
  OrderFFMStatus.New,
  OrderFFMStatus.Confirmed,
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
  OrderFFMStatus.AwaitingCollection,
  OrderFFMStatus.Collecting,
  OrderFFMStatus.Awaiting3PLPickup,
  OrderFFMStatus.ReturnedStocked,
];

export const FIRST_STATUS_ORDER = [
  OrderFFMStatus.Draft,
  OrderFFMStatus.New,
  OrderFFMStatus.Confirmed,
];

export const CLIENT_STATUS_ORDER = [OrderFFMStatus.Draft, OrderFFMStatus.New];

export const STATUS_ALLOW_SYNC_ORDER = [OrderFFMStatus.Confirmed, OrderFFMStatus.Canceled];

export const STATUS_CURRENT_ALLOW_SYNC_ORDER = [
  OrderFFMStatus.New,
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
];

export const STATUS_CURRENT_ALLOW_SYNC_HVNET_TAG = [
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
  OrderFFMStatus.Confirmed,
  OrderFFMStatus.AwaitingCollection,
  OrderFFMStatus.Collecting,
  OrderFFMStatus.Awaiting3PLPickup,
];

export const STATUS_AG_ALLOW_UPDATE_ORDER = [
  OrderFFMStatus.Confirmed,
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
];

export const NOT_ALLOW_UPDATE_CARRIER_ORDER = [
  OrderFFMStatus.ReturnedStocked,
  OrderFFMStatus.Returned,
  OrderFFMStatus.ReturnedCompleted,
  OrderFFMStatus.Delivered,
  OrderFFMStatus.DeliveredCompleted,
  OrderFFMStatus.Canceled,
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
];

export const NOT_ALLOW_UPDATE_CARRIER_ORDER_V2 = [
  OrderFFMStatus.ReturnedStocked,
  OrderFFMStatus.Returned,
  OrderFFMStatus.ReturnedCompleted,
  OrderFFMStatus.Delivered,
  OrderFFMStatus.DeliveredCompleted,
  OrderFFMStatus.Canceled,
];

export const NOT_ALLOW_CANCEL_WAYBILL = [
  OrderFFMStatus.ReturnedStocked,
  OrderFFMStatus.LostCompleted,
  OrderFFMStatus.ReturnedCompleted,
  OrderFFMStatus.DamagedCompleted,
  OrderFFMStatus.DeliveredCompleted,
  OrderFFMStatus.Canceled,
  OrderFFMStatus.Draft,
];

export const BEFORE_ORDER_STATUS_3PL_PICKED_UP = [
  OrderFFMStatus.Draft,
  OrderFFMStatus.New,
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
  OrderFFMStatus.Confirmed,
  OrderFFMStatus.AwaitingCollection,
  OrderFFMStatus.Collecting,
  OrderFFMStatus.Awaiting3PLPickup,
];
export const EDIT_PRODUCT_STATUS_ORDER = [
  OrderFFMStatus.Draft,
  OrderFFMStatus.New,
  // OrderFFMStatus.AwaitingStock,
];
export const EDIT_WAREHOUSE_STATUS_ORDER = [
  OrderFFMStatus.Draft,
  OrderFFMStatus.New,
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
  OrderFFMStatus.Confirmed,
];
export const DENIED_RETURN_WAREHOUSE_STATUS_ORDER = [
  OrderFFMStatus.Delivered,
  OrderFFMStatus.ReturnedStocked,
  OrderFFMStatus.LostBy3PL,
  OrderFFMStatus.LostByWH,
  OrderFFMStatus.ReturnedDamagedBy3PL,
  OrderFFMStatus.DamagedByWH,
  OrderFFMStatus.LostCompleted,
  OrderFFMStatus.DamagedCompleted,
  OrderFFMStatus.ReturnedCompleted,
  OrderFFMStatus.DeliveredCompleted,
];
export const EDIT_CARRIER_ORDER = [OrderFFMStatus.Draft, OrderFFMStatus.New];
export const EDIT_CARRIER_STATUS_ORDER = [
  OrderFFMStatus.Draft,
  OrderFFMStatus.New,
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
  OrderFFMStatus.Confirmed,
  OrderFFMStatus.AwaitingCollection,
  OrderFFMStatus.Collecting,
  OrderFFMStatus.Awaiting3PLPickup,
];

export const EDIT_RECIPIENT_STATUS_ORDER = [
  OrderFFMStatus.Draft,
  OrderFFMStatus.New,
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
  OrderFFMStatus.Confirmed,
  OrderFFMStatus.AwaitingCollection,
  OrderFFMStatus.Collecting,
  OrderFFMStatus.Awaiting3PLPickup,
];

export const EDITABLE_ORDER_FIELD = {
  PRODUCT: [
    'products',
    'discount',
    'discountPercentage',
    'surcharge',
    'shippingFee',
    'paid',
    'totalPrice',
    'serviceTLS',
    'serviceCS',
    'serviceFFM',
    'serviceInsurance',
    'subTotal',
    'clientId',
  ],
  COD: [
    'discount',
    'discountPercentage',
    'surcharge',
    'shippingFee',
    'paid',
    'totalPrice',
    'serviceTLS',
    'serviceCS',
    'serviceFFM',
    'serviceInsurance',
  ],
  WAREHOUSE: ['warehouseId'],
  CUSTOMER: [
    'customer',
    'recipientName',
    'recipientPhone',
    'recipientEmail',
    'recipientAddress',
    'recipientAddressNote',
    'recipientWard',
    'recipientWardId',
    'recipientDistrict',
    'recipientDistrictId',
    'recipientProvince',
    'recipientProvinceId',
    'recipientPostCode',
    'recipientCountry',
    'recipientCountryId',
  ],
};

export const ALLOW_UPDATE_STATUS_OUT_DELIVERY = [
  OrderFFMStatus.InTransit,
  OrderFFMStatus.Stocked3PL,
  OrderFFMStatus.InDelivery,
  OrderFFMStatus.FailedDelivery,
  OrderFFMStatus.AwaitingReturn,
  OrderFFMStatus.InReturn,
  OrderFFMStatus.Delivered,
  OrderFFMStatus.LostBy3PL,
  OrderFFMStatus.Returned,
];
export const NOT_ALLOW_UPDATE_STATUS_OUT_DELIVERY = [
  OrderFFMStatus.Delivered,
  OrderFFMStatus.LostBy3PL,
  OrderFFMStatus.Returned,
];

export const PancakePFGId = 43;

export const PANCAKE_STATUS_ORDER = {
  [OrderFFMStatus.AwaitingStock]: 11, //Chờ hàng
  [OrderFFMStatus.Confirmed]: 1, //Đã xác nhận
  [OrderFFMStatus.AwaitingCollection]: 8, //Đang đóng hàng
  [OrderFFMStatus.Collecting]: 8, //Đang đóng hàng
  [OrderFFMStatus.Awaiting3PLPickup]: 9, //Chờ chuyển hàng
  [OrderFFMStatus.PickedUp3PL]: 2, //Đã gửi hàng
  [OrderFFMStatus.InTransit]: 2, //Đã gửi hàng
  [OrderFFMStatus.Stocked3PL]: 2, //Đã gửi hàng
  [OrderFFMStatus.InDelivery]: 2, //Đã gửi hàng
  [OrderFFMStatus.FailedDelivery]: 2, //Đã gửi hàng
  [OrderFFMStatus.Delivered]: 3, //Đã nhận
  [OrderFFMStatus.DeliveredCompleted]: 3, //Đã nhận
  [OrderFFMStatus.AwaitingReturn]: 4, //Đang hoàn
  [OrderFFMStatus.InReturn]: 4, //Đang hoàn
  [OrderFFMStatus.Returned]: 4, //Đang hoàn
  [OrderFFMStatus.ReturnedStocked]: 5, //Đã hoàn
  [OrderFFMStatus.ReturnedDamagedBy3PL]: 5, //Đã hoàn
  [OrderFFMStatus.LostByWH]: 5, //Đã hoàn
  [OrderFFMStatus.DamagedByWH]: 5, //Đã hoàn
  [OrderFFMStatus.ReturnedCompleted]: 5, //Đã hoàn
  [OrderFFMStatus.LostCompleted]: 5, //Đã hoàn
  [OrderFFMStatus.DamagedCompleted]: 5, //Đã hoàn
  [OrderFFMStatus.Canceled]: 6, //Đã huỷ
};

export const PANCAKE_TAG_ORDER_POS = [1, 19, 20];

export const PANCAKE_TAG_MAPPING_ORDER = {
  [OrderFFMStatus.FailedDelivery]: PANCAKE_TAG_ORDER_POS[0],
  [OrderFFMStatus.AwaitingReturn]: PANCAKE_TAG_ORDER_POS[1],
  [OrderFFMStatus.InDelivery]: PANCAKE_TAG_ORDER_POS[2],
};

export const ALLOW_UPDATE_COD_ORDER = [
  OrderFFMStatus.Draft,
  OrderFFMStatus.New,
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
  OrderFFMStatus.Confirmed,
  // OrderFFMStatus.AwaitingCollection,
  OrderFFMStatus.Collecting,
  OrderFFMStatus.Awaiting3PLPickup,
];
export const ALLOW_UPDATE_IN_DELIVERY = [
  OrderFFMStatus.PickedUp3PL,
  OrderFFMStatus.InTransit,
  OrderFFMStatus.Stocked3PL,
  OrderFFMStatus.InDelivery,
  OrderFFMStatus.Delivered,
  OrderFFMStatus.FailedDelivery,
  OrderFFMStatus.AwaitingCollection,
  OrderFFMStatus.Awaiting3PLPickup,
  OrderFFMStatus.Collecting,
];

export const ALLOW_SYNC_DATA_TO_AGSALE = [
  OrderFFMStatus.Draft,
  OrderFFMStatus.New,
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
  OrderFFMStatus.Confirmed,
  OrderFFMStatus.AwaitingCollection,
  OrderFFMStatus.Collecting,
  OrderFFMStatus.Awaiting3PLPickup,
];
export const EDITABLE_ORDER_IN_DELIVERY = {
  [CountryID.TH]: {
    [CarrierCode.flashexpress]: [
      // 'discount',
      // 'discountPercentage',
      // 'surcharge',
      // 'shippingFee',
      // 'paid',
      // 'totalPrice',
      // 'serviceTLS',
      // 'serviceCS',
      // 'serviceFFM',
      // 'serviceInsurance',
      // 'subTotal',
      'recipientPhone',
    ],
    [CarrierCode.kerryexpress]: [
      // 'discount',
      // 'discountPercentage',
      // 'surcharge',
      // 'shippingFee',
      // 'paid',
      // 'totalPrice',
      // 'serviceTLS',
      // 'serviceCS',
      // 'serviceFFM',
      // 'serviceInsurance',
      // 'subTotal',
      'recipientPhone',
    ],
  },
  [CountryID.PH]: {
    [CarrierCode.ninjavan]: [
      'discount',
      'discountPercentage',
      'surcharge',
      'shippingFee',
      'paid',
      'totalPrice',
      'serviceTLS',
      'serviceCS',
      'serviceFFM',
      'serviceInsurance',
      'subTotal',
      'recipientPhone',
      'waybillNote',
    ],
  },
  [CountryID.IN]: {},
  [CountryID.MY]: {
    [CarrierCode.flashexpress]: [
      'discount',
      'discountPercentage',
      'surcharge',
      'shippingFee',
      'paid',
      'totalPrice',
      'serviceTLS',
      'serviceCS',
      'serviceFFM',
      'serviceInsurance',
      'subTotal',
      'recipientPhone',
    ],
    [CarrierCode.bestexpress]: [
      'discount',
      'discountPercentage',
      'surcharge',
      'shippingFee',
      'paid',
      'totalPrice',
      'serviceTLS',
      'serviceCS',
      'serviceFFM',
      'serviceInsurance',
      'subTotal',
      'recipientPhone',
    ],
    [CarrierCode.ninjavan]: [
      'recipientName',
      'recipientPhone',
      'recipientAddress',
      'recipientAddressNote',
      'recipientWard',
      'recipientWardId',
      'recipientDistrict',
      'recipientDistrictId',
      'recipientProvince',
      'recipientProvinceId',
      'recipientPostCode',
      'recipientCountry',
      'recipientCountryId',
      'discount',
      'discountPercentage',
      'surcharge',
      'shippingFee',
      'paid',
      'totalPrice',
      'serviceTLS',
      'serviceCS',
      'serviceFFM',
      'serviceInsurance',
      'subTotal',
      'waybillNote',
    ],
  },
  [CountryID.VN]: {
    [CarrierCode.flashexpress]: [
      'discount',
      'discountPercentage',
      'surcharge',
      'shippingFee',
      'paid',
      'totalPrice',
      'serviceTLS',
      'serviceCS',
      'serviceFFM',
      'serviceInsurance',
      'subTotal',
      'recipientPhone',
    ],
    [CarrierCode.ninjavan]: [
      'recipientName',
      'recipientPhone',
      'recipientAddress',
      'recipientAddressNote',
      'recipientWard',
      'recipientWardId',
      'recipientDistrict',
      'recipientDistrictId',
      'recipientProvince',
      'recipientProvinceId',
      'recipientPostCode',
      'recipientCountry',
      'recipientCountryId',
      'discount',
      'discountPercentage',
      'surcharge',
      'shippingFee',
      'paid',
      'totalPrice',
      'serviceTLS',
      'serviceCS',
      'serviceFFM',
      'serviceInsurance',
      'subTotal',
    ],
    [CarrierCode.viettelpost]: [
      'recipientName',
      'recipientPhone',
      'recipientAddress',
      'recipientAddressNote',
      'recipientWard',
      'recipientWardId',
      'recipientDistrict',
      'recipientDistrictId',
      'recipientProvince',
      'recipientProvinceId',
      'recipientPostCode',
      'recipientCountry',
      'recipientCountryId',
      'discount',
      'discountPercentage',
      'surcharge',
      'shippingFee',
      'paid',
      'totalPrice',
      'serviceTLS',
      'serviceCS',
      'serviceFFM',
      'serviceInsurance',
      'subTotal',
      'waybillNote',
    ],
    [CarrierCode.ghtk]: [
      'recipientName',
      'recipientPhone',
      'recipientAddress',
      'recipientAddressNote',
      'recipientWard',
      'recipientWardId',
      'recipientDistrict',
      'recipientDistrictId',
      'recipientProvince',
      'recipientProvinceId',
      'recipientPostCode',
      'recipientCountry',
      'recipientCountryId',
      'discount',
      'discountPercentage',
      'surcharge',
      'shippingFee',
      'paid',
      'totalPrice',
      'serviceTLS',
      'serviceCS',
      'serviceFFM',
      'serviceInsurance',
      'subTotal',
      'waybillNote',
    ],
    [CarrierCode.bestexpress]: [
      'recipientName',
      'recipientPhone',
      'recipientAddress',
      'recipientAddressNote',
      'recipientWard',
      'recipientWardId',
      'recipientDistrict',
      'recipientDistrictId',
      'recipientProvince',
      'recipientProvinceId',
      'recipientPostCode',
      'recipientCountry',
      'recipientCountryId',
      'discount',
      'discountPercentage',
      'surcharge',
      'shippingFee',
      'paid',
      'totalPrice',
      'serviceTLS',
      'serviceCS',
      'serviceFFM',
      'serviceInsurance',
      'subTotal',
      'waybillNote',
    ],
  },
};
export const RETURNED_STATUS = [
  OrderFFMStatus.AwaitingReturn,
  OrderFFMStatus.InReturn,
  OrderFFMStatus.Returned,
  OrderFFMStatus.ReturnedStocked,
  OrderFFMStatus.ReturnedCompleted,
];
export const TRANFER_US_PROVINCE_CODE = {
  ALASKA: 'AK',
  ALABAMA: 'AL',
  ARKANSAS: 'AR',
  ARIZONA: 'AZ',
  CALIFORNIA: 'CA',
  COLORADO: 'CO',
  CONNECTICUT: 'CT',
  'DISTRICT OF COLUMBIA': 'DC',
  DELAWARE: 'DE',
  FLORIDA: 'FL',
  GEORGIA: 'GA',
  GUAM: 'GU',
  HAWAII: 'HI',
  IOWA: 'IA',
  IDAHO: 'ID',
  ILLINOIS: 'IL',
  INDIANA: 'IN',
  KANSAS: 'KS',
  KENTUCKY: 'KY',
  LOUISIANA: 'LA',
  MASSACHUSETTS: 'MA',
  MARYLAND: 'MD',
  MAINE: 'ME',
  MICHIGAN: 'MI',
  MINNESOTA: 'MN',
  MISSOURI: 'MO',
  MISSISSIPPI: 'MS',
  MONTANA: 'MT',
  'NORTH CAROLINA': 'NC',
  'NORTH DAKOTA': 'ND',
  NEBRASKA: 'NE',
  'NEW HAMPSHIRE': 'NH',
  'NEW JERSEY': 'NJ',
  'NEW MEXICO': 'NM',
  NEVADA: 'NV',
  'NEW YORK': 'NY',
  OHIO: 'OH',
  OKLAHOMA: 'OK',
  OREGON: 'OR',
  PENNSYLVANIA: 'PA',
  'PUERTO RICO': 'PR',
  'RHODE ISLAND': 'RI',
  'SOUTH CAROLINA': 'SC',
  'SOUTH DAKOTA': 'SD',
  TENNESSEE: 'TN',
  TEXAS: 'TX',
  UTAH: 'UT',
  VIRGINIA: 'VA',
  'VIRGIN ISLANDS': 'VI',
  VERMONT: 'VT',
  WASHINGTON: 'WA',
  WISCONSIN: 'WI',
  'WEST VIRGINIA': 'WV',
  WYOMING: 'WY',
};

export const COLOR_DEFAULT_TAG = '#EFF0F1';

export const NOT_ALLOW_UPDATE_STATUS_BY_3PL_OUT_FOR_DELIVERY = [
  OrderFFMStatus.Returned,
  OrderFFMStatus.ReturnedStocked,
  OrderFFMStatus.ReturnedCompleted,
  OrderFFMStatus.Delivered,
  OrderFFMStatus.DeliveredCompleted,
  OrderFFMStatus.LostBy3PL,
  OrderFFMStatus.LostByWH,
  OrderFFMStatus.DamagedByWH,
  OrderFFMStatus.ReturnedDamagedBy3PL,
  OrderFFMStatus.LostCompleted,
  OrderFFMStatus.DamagedCompleted,
];

export const DISAPPROVE_UPDATE_STATUS_BY_3PL = [
  OrderFFMStatus.Returned,
  OrderFFMStatus.ReturnedStocked,
  OrderFFMStatus.ReturnedCompleted,
  OrderFFMStatus.Delivered,
  OrderFFMStatus.DeliveredCompleted,
  OrderFFMStatus.LostBy3PL,
  OrderFFMStatus.LostByWH,
  OrderFFMStatus.DamagedByWH,
  OrderFFMStatus.ReturnedDamagedBy3PL,
  OrderFFMStatus.LostCompleted,
  OrderFFMStatus.DamagedCompleted,
];

export const ALLOW_EDIT_COD = [
  OrderFFMStatus.AwaitingStock,
  OrderFFMStatus.Reconfirm,
  OrderFFMStatus.Confirmed,
  OrderFFMStatus.AwaitingCollection,
  OrderFFMStatus.Collecting,
  OrderFFMStatus.Awaiting3PLPickup,
];

export const LAST_STATUS = [
  OrderFFMStatus.Delivered,
  OrderFFMStatus.DeliveredCompleted,
  OrderFFMStatus.AwaitingReturn,
  OrderFFMStatus.Returned,
  OrderFFMStatus.ReturnedCompleted,
  OrderFFMStatus.ReturnedStocked,
  OrderFFMStatus.LostByWH,
  OrderFFMStatus.LostBy3PL,
  OrderFFMStatus.DamagedByWH,
  OrderFFMStatus.ReturnedDamagedBy3PL,
  OrderFFMStatus.LostCompleted,
  OrderFFMStatus.DamagedCompleted,
  OrderFFMStatus.Canceled,
];

export const BUILD_EXPORT_TYPE = [
  TypeTemplateDownloadOrder.Collection,
  TypeTemplateDownloadOrder.CollectionDropshipBDG,
  TypeTemplateDownloadOrder.StartCollectPack,
];

export const SYSTEM_ACTOR = {
  System: '-99',
  Hvnet: '-98',
  'sales.pfg.asia': '-97',
  'sales.bigdealsglobal.com': '-96',
  'salesz.gaho.vn': '-95',
  'agsale.co': '-94',
  'admin.agbiz.tech': '-93',
};

export const SYSTEM_LOCK_ACTION_STRING = '_lock';
