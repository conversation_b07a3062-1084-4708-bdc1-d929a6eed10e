export enum StatusMIEnum {
  connected = 1,
  disconnected = 2,
  failedToConnect = 3,
}

export enum TypeMIEnum {
  onepage = "onepage",
  lazada = "lazada",
  amazon = "amazon",
  wooCommerce = "wooCommerce",
  shopee = "shopee",
  tiktok = "tiktok",
  miaoshou = "miaoshou",
  tiktokGlobal = "tiktokGlobal",
  shopeeGlobal = "shopeeGlobal"
}

export enum TypePlatformEnum {
  shopee = TypeMIEnum.shopee,
  lazada = TypeMIEnum.lazada,
  amazon = TypeMIEnum.amazon,
  tiktok = TypeMIEnum.tiktok
}

export enum TypeMIFailEnum {
  ProductsNotExists = 1,
  LocaltionNotExists = 2,
  ContactNotExists = 3,
}

export enum ProductStatusMIEnum {
  ACTIVATE = 1,
  DISACTIVATE = 2
}