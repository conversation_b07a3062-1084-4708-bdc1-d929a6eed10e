import { Expose, Type } from 'class-transformer';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import { Column, Entity, JoinColumn, JoinTable, ManyToMany, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { ProductStatusMIEnum } from '../enums/marketplace-integration.enum';
import { MarketplaceProduct } from './marketplace-product.entity';

@Entity({
  name: 'marketplace_variant',
  database: process.env.DATABASE_ORDER_FFM,
  synchronize: true,
})
export class MarketplaceVariant extends BaseEntity {
    @PrimaryGeneratedColumn({
    name: 'id',
    })
    @Expose()
    id?: number;

    @Column({
    name: 'platform_id',
    type: 'varchar',
    nullable: true,
    })
    @Expose()
    platformId?: string;

    @Column({
    name: 'sku',
    type: 'varchar',
    nullable: false,
    })
    @Expose()
    sku?: string;

    @Column({
    name: 'client_id',
    type: 'varchar',
    nullable: true,
    })
    @Expose()
    clientId?: string;

    @Column({
    name: 'creator_id',
    type: 'varchar',
    nullable: true,
    })
    @Expose()
    creatorId?: string;

    @Column({
    name: 'last_editor_id',
    type: 'varchar',
    nullable: true,
    })
    @Expose()
    lastEditorId?: string;

    @Column({
    name: 'status',
    type: 'integer',
    default: ProductStatusMIEnum.ACTIVATE,
    })
    @EnumTransform(ProductStatusMIEnum)
    @Expose()
    status?: number;

    @Column({
    name: 'biz_id',
    type: 'varchar',
    nullable: true,
    })
    @Expose()
    bizId?: string;

    @Column({
    name: 'avatar',
    type: 'varchar',
    nullable: true,
    })
    @Expose()
    avatar?: string;

    @Column({
    name: 'country_id',
    type: 'int',
    nullable: true,
    })
    @Expose()
    countryId?: number;


    @Column({
        name: 'price',
        type: 'float',
        default: 0,
    })
    @Expose()
    price?: number;

    @Column({
        name: 'inventory',
        type: 'float',
        default: 0,
    })
    @Expose()
    inventory?: number;

    @Column({ type: 'integer', name: 'id_product', nullable: true })
    @Expose()
    @NonEmptyTransform()
    productId?: number;

    @ManyToOne(
    () => MarketplaceProduct,
    att => att.variants,
    { nullable: true },
    )
    @JoinColumn({
    name: 'id_product',
    })
    @Expose()
    @NonEmptyTransform()
    product?: MarketplaceProduct;
}
