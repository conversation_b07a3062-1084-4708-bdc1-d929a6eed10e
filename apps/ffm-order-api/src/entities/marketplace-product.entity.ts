import { Expose, Type } from 'class-transformer';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import { Column, Entity, JoinTable, ManyToMany, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { ProductStatusMIEnum } from '../enums/marketplace-integration.enum';
import { MarketplaceVariant } from './marketplace-variant.entity';

@Entity({
  name: 'marketplace_product',
  database: process.env.DATABASE_ORDER_FFM,
  synchronize: true,
})
export class MarketplaceProduct extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id?: number;
  
  @Column({
    name: 'platform_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  platformId?: string;

  @Column({
    name: 'name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  name?: string;

  @Column({
    name: 'client_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  clientId?: string;

  @Column({
    name: 'creator_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  creatorId?: string;

  @Column({
    name: 'last_editor_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  lastEditorId?: string;

  @Column({
    name: 'status',
    type: 'integer',
    default: ProductStatusMIEnum.ACTIVATE,
  })
  @EnumTransform(ProductStatusMIEnum)
  @Expose()
  status?: number;

  @OneToMany(
    () => MarketplaceVariant,
    prod => prod.product,
    { nullable: true, cascade: true },
  )
  @Type(() => MarketplaceVariant)
  @Expose()
  variants?: MarketplaceVariant[];

  @Column({
    name: 'biz_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  bizId?: string;

  @Column({
    name: 'country_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  countryId?: number;

  @Column({
    name: 'cover',
    type: 'text',
    nullable: true,
    array: true,
  })
  @Expose()
  @NonEmptyTransform()
  images?: string[] | null;
}
