import { Users } from 'apps/ffm-catalog-api/src/read-entities/identity-entities/Users';
import { Expose } from 'class-transformer';
import { BaseEntity } from 'core/entities/base/base-entity.entity';
import { Column, DeleteDateColumn, Entity, Index, PrimaryColumn, PrimaryGeneratedColumn } from 'typeorm';
import { StatusMIEnum, TypeMIEnum, TypePlatformEnum } from '../enums/marketplace-integration.enum';
import { EnumTransform } from 'core/decorators/enum-transform.decorator';
import { NonEmptyTransform } from 'core/decorators/non-empty-transform/non-empty-transform.decorator';
import { DateTransform } from 'core/decorators/date-transform/date-transform.decorator';
@Entity({
  name: 'marketplace_integration',
  database: process.env.DATABASE_ORDER_FFM,
})
@Index('UQ_SHOP_CLIENT', ['companyId', 'type', 'shopId'], {
  unique: true,
})
export class MarketplaceIntegration extends BaseEntity {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  id?: number;

  @Column({
    name: 'client_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  clientId: number;

  @Column({
    name: 'country_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  countryId: string;

  @Column({
    name: 'shop_id',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  shopId: string;

  @Column({
    name: 'shop_name',
    type: 'varchar',
    nullable: true,
  })
  @Expose()
  shopName?: string;

  @Column({
    name: 'platform_id',
    type: 'varchar',
    nullable: true,
    length: 250,
  })
  @Expose()
  platformId: string;

  @Column({
    name: 'platform_name',
    type: 'varchar',
    nullable: true,
    length: 250,  
  })
  @Expose()
  platformName?: string;

  @Column({
    name: 'platform_cipher',
    type: 'varchar',
    nullable: true,
    length: 250,  
  })
  @Expose()
  platformCipher?: string;

  @Column({
    name: 'parent_shop_id',
    type: 'varchar',
    nullable: true,
    length: 250,
  })
  @Expose()
  parentShopId: string;

  @Column({
    name: 'parent_shop_name',
    type: 'varchar',
    nullable: true,
    length: 250,
  })
  @Expose()
  parentShopName?: string;

  @Column({
    name: 'shop_url',
    type: 'text',
  })
  @Expose()
  shopUrl?: string;

  @Column({
    name: 'api_key',
    type: 'text',
  })
  @Expose()
  apiKey: string;

  @Column({
    name: 'company_id',
    type: 'int',
  })
  @Expose()
  companyId?: number;

  @Column({
    name: 'last_updated_by',
    type: 'int',
    nullable: true,
  })
  @Expose()
  lastUpdatedBy?: number;

  @Column({
    name: 'creator_id',
    type: 'int',
    nullable: true,
  })
  @Expose()
  creatorId?: number;

  @Column({
    name: 'type',
    type: 'varchar',
    default: TypeMIEnum.onepage,
    nullable: true,
  })
  @Expose()
  @EnumTransform(TypeMIEnum)
  type: TypeMIEnum;

  @Column({
    name: 'platform',
    type: 'varchar',
    default: null,
    nullable: true,
  })
  @Expose()
  @EnumTransform(TypePlatformEnum)
  platform: TypePlatformEnum;

  @Column({
    name: 'status',
    type: 'int',
    default: StatusMIEnum.connected,
    nullable: true,
  })
  @Expose()
  @EnumTransform(StatusMIEnum)
  status: StatusMIEnum;

  @Column({
    name: 'connected_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  connectedAt?: Date;

  @Column({
    name: 'last_sync',
    type: 'timestamp with time zone',
    nullable: true,
  })
  @Expose()
  @DateTransform()
  @NonEmptyTransform()
  lastSync?: Date;

  @Column('boolean', {
    name: 'is_sync_order',
    default: false,
  })
  @Expose()
  isSyncOrder: boolean;
}
