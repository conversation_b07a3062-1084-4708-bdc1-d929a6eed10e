import * as crypto from 'crypto';

/**
 * TikTok Shop API Signature Generator based on Postman Pre-request Script
 * This implementation follows the exact logic from the Postman script
 */

/**
 * Sort object keys alphabetically and return new sorted object
 * @param obj - Object to sort
 * @returns Sorted object
 */
function objKeySort(obj: Record<string, any>): Record<string, any> {
  const newKey = Object.keys(obj).sort();
  const newObj: Record<string, any> = {};
  for (let i = 0; i < newKey.length; i++) {
    newObj[newKey[i]] = obj[newKey[i]];
  }
  return newObj;
}

/**
 * Generate timestamp in seconds (Unix timestamp)
 * @returns Current timestamp in seconds
 */
function generateTimestamp(): number {
  return Math.floor(Date.now() / 1000);
}

/**
 * Calculate signature based on Postman Pre-request Script logic
 * @param secret - TikTok Shop App Secret
 * @param apiPath - API path (e.g., '/authorization/202309/shops')
 * @param queryParams - Query parameters object
 * @param requestBody - Request body (for POST requests, empty string for GET)
 * @param timestamp - Optional timestamp, if not provided will generate new one
 * @returns Object containing signature and timestamp
 */
export function calculateTikTokSignature(
  secret: string,
  apiPath: string,
  queryParams: Record<string, any> = {},
  requestBody: any = {},
  timestamp?: number
): { sign: string; timestamp: number } {
  // Generate timestamp if not provided
  const ts = timestamp || generateTimestamp();

  // Prepare parameters object
  const param: Record<string, any> = { ...queryParams };

  // Set timestamp
  param.timestamp = ts;

  // Remove sign and access_token from parameters (as per Postman script)
  delete param.sign;
  delete param.access_token;

  // Sort parameters alphabetically
  const sortedObj = objKeySort(param);

  // Build sign string following Postman logic:
  // secret + apiPath + sortedParams + requestBody + secret
  let signstring = secret + apiPath;

  // Add sorted parameters
  for (const key in sortedObj) {
    signstring = signstring + key + (sortedObj[key] ?? '');
  }

  // Add request body (convert object to string) and secret
  const bodyString = requestBody != null ? (typeof requestBody === 'object' ? JSON.stringify(requestBody) : requestBody.toString()) : '';
  signstring = signstring + bodyString + secret;
  console.log("🚬 ~ calculateTikTokSignature ~ signstring:", signstring);
  
  // Generate HMAC-SHA256 signature
  const sign = crypto
    .createHmac('sha256', secret)
    .update(signstring)
    .digest('hex');

  return {
    sign,
    timestamp: ts
  };
}

/**
 * Generate signature for GET requests (no request body)
 * @param secret - TikTok Shop App Secret
 * @param apiPath - API path
 * @param queryParams - Query parameters
 * @param timestamp - Optional timestamp
 * @returns Object containing signature and timestamp
 */
export function generateGetSignature(
  secret: string,
  apiPath: string,
  queryParams: Record<string, any> = {},
  timestamp?: number
): { sign: string; timestamp: number } {
  return calculateTikTokSignature(secret, apiPath, queryParams, null, timestamp);
}

/**
 * Generate signature for POST requests (with request body)
 * @param secret - TikTok Shop App Secret
 * @param apiPath - API path
 * @param queryParams - Query parameters
 * @param requestBody - Request body as string
 * @param timestamp - Optional timestamp
 * @returns Object containing signature and timestamp
 */
export function generatePostSignature(
  secret: string,
  apiPath: string,
  queryParams: Record<string, any> = {},
  requestBody: any = {},
  timestamp?: number
): { sign: string; timestamp: number } {
  return calculateTikTokSignature(secret, apiPath, queryParams, requestBody, timestamp);
}

/**
 * Debug function to show step-by-step signature generation
 * @param secret - TikTok Shop App Secret
 * @param apiPath - API path
 * @param queryParams - Query parameters
 * @param requestBody - Request body
 * @param timestamp - Optional timestamp
 * @returns Debug information
 */
export function debugTikTokPostmanSignature(
  secret: string,
  apiPath: string,
  queryParams: Record<string, any> = {},
  requestBody: any = {},
  timestamp?: number
): {
  timestamp: number;
  timestampISO: string;
  originalParams: Record<string, any>;
  sortedParams: Record<string, any>;
  signString: string;
  signature: string;
} {
  const ts = timestamp || generateTimestamp();

  // Prepare parameters
  const param: Record<string, any> = { ...queryParams };
  param.timestamp = ts;

  const originalParams = { ...param };

  // Remove sign and access_token
  delete param.sign;
  delete param.access_token;

  // Sort parameters
  const sortedParams = objKeySort(param);

  // Build sign string
  let signString = secret + apiPath;
  for (const key in sortedParams) {
    signString = signString + key + (sortedParams[key] ?? '');
  }
  const bodyString = typeof requestBody === 'object' ? JSON.stringify(requestBody) : requestBody.toString();
  signString = signString + bodyString + secret;

  // Generate signature
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signString)
    .digest('hex');

  return {
    timestamp: ts,
    timestampISO: new Date(ts * 1000).toISOString(),
    originalParams,
    sortedParams,
    signString: `${secret.substring(0, 3)}***${apiPath}[params]${bodyString}***${secret.substring(0, 3)}`,
    signature
  };
}

/**
 * Test function with exact parameters from your test case
 * @param secret - TikTok Shop App Secret
 * @returns Test result
 */
export function testTikTokPostmanSignature(secret: string): {
  expectedSignature: string;
  actualSignature: string;
  match: boolean;
  debug: any;
} {
  const apiPath = '/authorization/202309/shops';
  const queryParams = {
    access_token: 'ROW_K3FwsQAAAABEYQI6EtzpO3zw1H13BfrHV1bX345Z7MPT1lAspzmlco_OmLy-Jqh0bpZQtrRh6oy8eOCRvpZccGasNCr4Rm61JTQpLaAXvnc-zpz9BPu7ZthM95rLIBX3iYf1GVPw4u81shqXJVJMSWIaklv3EexmleGISO900OZ_fLLdc5mtAA',
    app_key: '6gu5sdae29pts',
    shop_id: '',
    version: '202309'
  };
  const timestamp = 1753258520;
  const expectedSignature = 'b8b16ddb9b5dddf086ab502219c99c429711edaa5868ea90021a04f9221a0d0d';
  
  const result = generateGetSignature(secret, apiPath, queryParams, timestamp);
  const debug = debugTikTokPostmanSignature(secret, apiPath, queryParams, {}, timestamp);
  
  return {
    expectedSignature,
    actualSignature: result.sign,
    match: result.sign === expectedSignature,
    debug
  };
}

/**
 * Example usage:
 * 
 * const secret = '7bf9fa93cd06a160c03f5789be285ac36df8701f';
 * const apiPath = '/authorization/202309/shops';
 * const queryParams = {
 *   app_key: '6gu5sdae29pts',
 *   shop_id: '',
 *   version: '202309'
 * };
 * 
 * const { sign, timestamp } = generateGetSignature(secret, apiPath, queryParams);
 * console.log('Signature:', sign);
 * console.log('Timestamp:', timestamp);
 */
