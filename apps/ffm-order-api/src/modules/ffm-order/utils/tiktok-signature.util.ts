import * as crypto from 'crypto';

/**
 * Tạo signature cho TikTok Shop API
 * @param apiPath - Đường dẫn API (ví dụ: /authorization/202309/shops)
 * @param queryParams - Object chứa các query parameters
 * @param appSecret - TikTok Shop App Secret
 * @returns Signature string được mã hóa bằng HMAC-SHA256
 */
export function createTikTokSignature(
  apiPath: string,
  queryParams: Record<string, any>,
  appSecret: string
): string {
  // Kiểm tra appSecret
  if (!appSecret || typeof appSecret !== 'string') {
    throw new Error('TikTok App Secret is required and must be a string');
  }

  // Step 1: Sort query parameter names alphabetically
  const sortedKeys = Object.keys(queryParams).sort();

  // Step 2: Concatenate the sorted parameters names and values
  // Note: Empty values should be included as empty strings
  const concatenatedParams = sortedKeys
    .map(key => `${key}${queryParams[key] ?? ''}`)
    .join('');

  // Step 3: Append the string from step 2 to the API path
  // Based on TikTok documentation example: /path + / + concatenatedParams
  const stringToSign = `${apiPath}/${concatenatedParams}`;

  // Step 4: Prepend and append TikTok Shop App client secret
  const messageToSign = `${appSecret}${stringToSign}${appSecret}`;

  console.log("🚀 ~ messageToSign:", messageToSign)
  // Step 5: Encode the string using HMAC-SHA256
  // Use appSecret as the HMAC key and messageToSign as the message
  const signature = crypto
    .createHmac('sha256', appSecret)
    .update(messageToSign)
    .digest('hex');

  return signature;
}

/**
 * Tạo signature cho TikTok Shop API với timestamp tự động
 * @param apiPath - Đường dẫn API
 * @param queryParams - Object chứa các query parameters (không bao gồm timestamp)
 * @param appSecret - TikTok Shop App Secret
 * @returns Object chứa signature và timestamp
 */
export function createTikTokSignatureWithTimestamp(
  apiPath: string,
  queryParams: Record<string, any>,
  appSecret: string
): { signature: string; timestamp: number } {
  // Kiểm tra appSecret
  if (!appSecret || typeof appSecret !== 'string') {
    throw new Error('TikTok App Secret is required and must be a string');
  }

  // Unix timestamp GMT (UTC+00:00) in seconds
  const timestamp = Math.floor(Date.now() / 1000);

  const paramsWithTimestamp = {
    ...queryParams,
    timestamp
  };

  const signature = createTikTokSignature(apiPath, paramsWithTimestamp, appSecret);

  return {
    signature,
    timestamp
  };
}

/**
 * Tạo Unix timestamp GMT (UTC+00:00) cho TikTok Shop API
 * @returns Unix timestamp in seconds
 */
export function createTikTokTimestamp(): number {
  return Math.floor(Date.now() / 1000);
}

/**
 * Test function để verify implementation với TikTok documentation example
 */
export function testTikTokSignatureWithDocExample(): {
  expected: string;
  actual: string;
  match: boolean;
  steps: any;
} {
  // Example from TikTok documentation
  const apiPath = '/authorization/202309/shops';
  const queryParams = {
    app_key: '123456',
    timestamp: 1234567890
  };
  const appSecret = 'abc000def111';

  // Expected result based on documentation
  const expectedSignature = 'calculated_from_documentation'; // This would need to be calculated manually

  const actualSignature = createTikTokSignature(apiPath, queryParams, appSecret);

  // Show step-by-step calculation
  const sortedKeys = Object.keys(queryParams).sort();
  const concatenatedParams = sortedKeys.map(key => `${key}${queryParams[key]}`).join('');
  const stringToSign = `${apiPath}/${concatenatedParams}`;
  const messageToSign = `${appSecret}${stringToSign}${appSecret}`;

  return {
    expected: expectedSignature,
    actual: actualSignature,
    match: actualSignature === expectedSignature,
    steps: {
      sortedKeys,
      concatenatedParams,
      stringToSign,
      messageToSign: `${appSecret.substring(0, 3)}***${stringToSign}***${appSecret.substring(0, 3)}`,
      hmacKey: `${appSecret.substring(0, 3)}***`,
      hmacMessage: 'messageToSign'
    }
  };
}

/**
 * Ví dụ sử dụng:
 *
 * const apiPath = '/authorization/202309/shops';
 * const queryParams = {
 *   app_key: '123456',
 *   timestamp: 1234567890
 * };
 * const appSecret = 'abc000def111';
 *
 * const signature = createTikTokSignature(apiPath, queryParams, appSecret);
 * console.log(signature);
 *
 * // Hoặc sử dụng với timestamp tự động:
 * const { signature, timestamp } = createTikTokSignatureWithTimestamp(
 *   apiPath,
 *   { app_key: '123456' },
 *   appSecret
 * );
 */

/**
 * Debug function để kiểm tra quá trình tạo signature
 * @param apiPath - Đường dẫn API
 * @param queryParams - Query parameters
 * @param appSecret - App Secret
 * @returns Object chứa thông tin debug
 */
export function debugTikTokSignature(
  apiPath: string,
  queryParams: Record<string, any>,
  appSecret: string
): {
  timestamp: number;
  timestampISO: string;
  sortedKeys: string[];
  concatenatedParams: string;
  stringToSign: string;
  messageToSign: string;
  signature: string;
} {
  // Use provided timestamp if exists, otherwise create new one
  const timestamp = queryParams.timestamp || createTikTokTimestamp();
  const paramsWithTimestamp = { ...queryParams, timestamp };

  const sortedKeys = Object.keys(paramsWithTimestamp).sort();
  const concatenatedParams = sortedKeys
    .map(key => `${key}${paramsWithTimestamp[key]}`)
    .join('');

  const stringToSign = `${apiPath}/${concatenatedParams}`;
  const messageToSign = `${appSecret}${stringToSign}${appSecret}`;

  const signature = crypto
    .createHmac('sha256', appSecret)
    .update(messageToSign)
    .digest('hex');

  return {
    timestamp,
    timestampISO: new Date(timestamp * 1000).toISOString(),
    sortedKeys,
    concatenatedParams,
    stringToSign,
    messageToSign: `${appSecret.substring(0, 3)}***${stringToSign}***${appSecret.substring(0, 3)}`,
    signature
  };
}
