import { BadRequestException, Injectable } from '@nestjs/common';
import { aesEncrypt } from '../utils/miaoshou';
import axios from 'axios';
import { MiaoshouDto } from 'apps/ffm-order-api/src/dtos/miaoshou/miashou.dto';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import * as moment from 'moment-timezone';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { <PERSON><PERSON><PERSON>Filter, MiaoshouOrderFilter } from 'apps/ffm-order-api/src/filters/miashou.filter';
import { TokenMiaoshou } from '../controllers/miaoshou.controller';
import { WWPROXY } from '../utils/wwproxy';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { CarrierCode, CountryID } from 'core/enums/carrier-code.enum';
import { stringify } from 'qs';
import { ExternalOrderType } from 'apps/ffm-order-api/src/enums/order-status.enum';
import { CreateOrderDto } from 'apps/ffm-order-api/src/dtos/miaoshou/order.dto';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { MarketplaceIntegration } from 'apps/ffm-order-api/src/entities/marketplace-integration.entity';
import { catalogFfmConnection, orderConnection } from 'core/constants/database-connection.constant';
import { In, Repository } from 'typeorm';
import { TypeMIEnum } from 'apps/ffm-order-api/src/enums/marketplace-integration.enum';
import { SlotWarehouses } from 'apps/ffm-order-api/src/read-entities/ffm-catalog/warehouses.entity';
import { ProductsService } from './products.service';
import { WarehouseClientAllocation } from 'apps/ffm-order-api/src/read-entities/ffm-catalog/warehouse_client_allocation.entity';
import { concat, isEmpty, isNull, reduce, uniq } from 'lodash';
import { plainToInstance } from 'class-transformer';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { $enum } from 'ts-enum-util';
import { SystemIdEnum } from 'core/enums/user-type.enum';
import { OrderProductComboVariant } from 'apps/ffm-order-api/src/entities/order-product-combo-variant.entity';
import { StockInventoryInOrder } from './stock-inventory.service';
import { OrderCarrier } from 'apps/ffm-order-api/src/entities/order-carrier.entity';
import { Carrier } from 'apps/ffm-order-api/src/entities/carrier.entity';

const tokenKey = `miaoshou-token-${process.env.MIAOSHOU_ACCOUNT}`;
const opOrderFlagId = "********";
export interface IMiaoshouShop {
  shopId: string;
  parentShopId: string;
  platformId: string;
  platformName: string;
  shopName: string;
  countryId: string;
  platform: string;
}

@Injectable()
export class MiaoshouService {
  constructor(
    @InjectRepository(Carrier, orderConnection)
    private carRepository: Repository<Carrier>,
    private redisService: RedisCacheService,
    @InjectRepository(MarketplaceIntegration, orderConnection)
    private mkiRepo: Repository<MarketplaceIntegration>,
    @InjectRepository(Order, orderConnection)
    private odRepository: Repository<Order>,
    @InjectRepository(WarehouseClientAllocation, catalogFfmConnection)
    private whcaRepository: Repository<WarehouseClientAllocation>,
    @InjectRepository(SlotWarehouses, catalogFfmConnection)
    private whRepository: Repository<SlotWarehouses>,
    private productsService: ProductsService,
    @InjectRepository(OrderProductComboVariant, orderConnection)
    private opcvRepository: Repository<OrderProductComboVariant>,
    private stockInventoryInOrder: StockInventoryInOrder,
  ) {}

  async login(
    data: MiaoshouDto,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    // const token = await this.getAccessToken(data.account, data.password);
    return {};
  }
  
  async getTags(
    pagination: PaginationOptions = { limit: 1000, skip: 0 },
    filters?: MiaoshouFilter,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<[Record<string, any>[], number]> {
    const token = await this.getAccessToken();
    const cookieMiaoshou = `acw_tc=${token?.acw_tc}; bsClientId=${token?.bsClientId}; firstClientId=${token?.firstClientId}; mserp=${token?.mserp}; mserp_sst=${token?.mserp_sst}`;

    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'https://erp.91miaoshou.com/api/order/flag/searchFlagList?pageNo=1&pageSize=100',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
        'Cookie': cookieMiaoshou
      },
      timeout: 8000
    };

    const res = await axios.request(config)
    .then(async (response) => {
      if(response?.data?.result == 'success'){
        return [response?.data?.list, response?.data?.total] as [Record<string, any>[], number];
      }
      else{
        if(response?.data?.code == 50001) await this.redisService.del(tokenKey);
        throw new Error("Unstable network connection. Please try again.");
      }
    })
    .catch((error) => {
      console.log("🚀 ~ MiaoshouService ~ getTags ~ error:", error);
      throw new Error("Unstable network connection. Please try again.");
    });

    return res;
  }

  async getShops(
    pagination: PaginationOptions,
    filters?: MiaoshouFilter,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<[IMiaoshouShop[], number]> {
    // const wwproxy = new WWPROXY(this.redisService);
    // const proxy = await wwproxy.getCurrentProxy();
    // console.log('🐔  ~ OrderCarriersService ~ handleNinjaVanWaybill ~ proxy:', proxy);

    const token = await this.getAccessToken();
    const cookieMiaoshou = `acw_tc=${token?.acw_tc}; bsClientId=${token?.bsClientId}; firstClientId=${token?.firstClientId}; mserp=${token?.mserp}; mserp_sst=${token?.mserp_sst}`;
    
    // const url = 'https://erp.91miaoshou.com/api/auth/shop/getAllShopV2';
    // const apikey = '72cb0f63ae769ce776ab9037f58e14b915ed8b22';
    // const res = await axios({
    //   url: 'https://api.zenrows.com/v1/',
    //   method: 'GET',
    //   headers: { 
    //     'Accept': 'application/json, text/plain, */*', 
    //     'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
    //     'Cookie': cookieMiaoshou
    //   },
    //   params: {
    //     'url': url,
    //     'apikey': apikey,
    //     'custom_headers': 'true',
    //   },
    // })
    // .then(async response => {
    //   console.log("🚀 ~ MiaoshouService ~ .then ~ response?.data?.result:", response?.data?.result);
    //   if(response?.data?.result == 'success'){        
    //     if(response?.data?.shopList){
    //       const listData:IMiaoshouShop[] = [];
    //       response?.data?.shopList.forEach(e => {
    //         if(e?.appAccountId == process.env.MIAOSHOU_APPID && e?.parentShopId){
    //           listData.push({
    //             shopId: e?.shopId,
    //             parentShopId: e?.parentShopId,
    //             platformId: e?.platformShopId,
    //             platformName: e?.platformShopName,
    //             shopName: e?.shopNick,
    //             countryId: e?.site,
    //             platform: e?.platform
    //           })
    //         }
    //       });
    //       return [listData, listData?.length] as [IMiaoshouShop[], number];
    //     }
    //     else return [[], 0] as [IMiaoshouShop[], number];
    //   }
    //   else{
    //     if(response?.data?.code == 50001) await this.redisService.del(tokenKey);
    //     throw new Error("Unstable network connection. Please try again.");
    //   }
    // })
    // .catch(error => {
    //   console.log(error);
    //   throw new Error("Unstable network connection. Please try again.");
    // });
    // return res;
    
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'https://erp.91miaoshou.com/api/auth/shop/getAllShopV2',
      headers: { 
        'Accept': 'application/json, text/plain, */*', 
        'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
        'Cookie': cookieMiaoshou
      },
      // httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
      timeout: 8000
    };
    
    const res = axios.request(config)
    .then(async (response) => {
      if(response?.data?.result == 'success'){        
        if(response?.data?.shopList){
          const listData:IMiaoshouShop[] = [];
          response?.data?.shopList.forEach(e => {
            if(e?.appAccountId == process.env.MIAOSHOU_APPID && e?.site && (e?.parentShopId || (!e?.parentShopId && ![TypeMIEnum.shopeeGlobal, TypeMIEnum.tiktokGlobal]?.includes(e?.platform)))){
              listData.push({
                shopId: e?.shopId,
                parentShopId: e?.parentShopId,
                platformId: e?.platformShopId,
                platformName: e?.platformShopName,
                shopName: e?.shopNick ?? e?.platformShopName,
                countryId: e?.site,
                platform: e?.platform
              })
            }
          });
          return [listData, listData?.length] as [IMiaoshouShop[], number];
        }
        else return [[], 0] as [IMiaoshouShop[], number];
      }
      else{
        if(response?.data?.code == 50001) await this.redisService.del(tokenKey);
        throw new Error("Unstable network connection. Please try again.");
      }
    })
    .catch((error) => {
      console.log("🚀 ~ MiaoshouService ~ error:", error);
      throw new Error("Unstable network connection. Please try again.");
    });

    return res;
  }

  async getOrders(
    id: number,
    pagination: PaginationOptions,
    filters?: MiaoshouOrderFilter,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<[Record<string, any>[], number]> {
    const companyId = request?.user?.companyId;
    const shop = await this.mkiRepo
        .createQueryBuilder('mk')
        .andWhere('mk.type = :type', { type: TypeMIEnum.miaoshou })
        .andWhere('mk.apiKey = :apiKey', { apiKey: id })
        // .andWhere('mk.clientId is not null')
        .andWhere('mk.companyId = :companyId', { companyId })
        .getOne();
        
    if(!shop?.id && !filters?.getAllShop) throw new BadRequestException("Not found shop");
    
    const token = await this.getAccessToken();
    const cookieMiaoshou = `acw_tc=${token?.acw_tc}; bsClientId=${token?.bsClientId}; firstClientId=${token?.firstClientId}; mserp=${token?.mserp}; mserp_sst=${token?.mserp_sst}`;
    
    let params = {
      'pageSize': filters?.limit ?? '20',
      'page': filters?.page ?? '1',
      'shopIds[0]': id?.toString(),
      'platformOrderSnsRp': 'ss',
      'platformOrderSns': filters?.keyword ?? '',
      'appPackageTab': 'waitProcess',
      'waitProcessTab': 'all',
      'supplierProcessStatus': 'all'
    };
    if(filters?.getAllShop) delete params['shopIds[0]']

    // if(filters?.isSync) params['orderFlags[0]'] = opOrderFlagId;

    if(filters?.tags){
      filters?.tags.forEach((value, index) => {
        params[`orderFlags[${index}]`] = Number(value);
      });
    }

    const config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://erp.91miaoshou.com/api/order/package/searchOrderPackageList',
      headers: { 
        'Accept': 'application/json, text/plain, */*', 
        'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8', 
        'Connection': 'keep-alive', 
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': cookieMiaoshou
      },
      data : stringify(params),
      // httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
      timeout: 8000
    };
    // console.log("🚀 ~ MiaoshouService ~ config.params:", params)
    let total = 0;
    const res = await axios.request(config)
    .then(async (response) => {
      if(response?.data?.result == 'success'){ 
        total = response?.data?.total;
        let orders = [];
        response?.data?.packageList?.forEach(e => {
          orders.push({
            "shopId": e?.shopId,
            "shopName": e?.shopName,
            "shopNameInOrder": e?.shopName,
            "discount": e?.orderInfo?.discountAmount,
            "discountPercentage": 0,
            "surcharge": 0,
            "shippingFee": e?.orderInfo?.actualShippingCost,
            "paid": 0,
            "totalPrice": e?.orderInfo?.payAmount,
            "serviceTLS": 0,
            "serviceCS": 0,
            "serviceFFM": 0,
            "serviceInsurance": 0,
            "subTotal": e?.orderInfo?.productAmount,
            "externalId": e?.platformOrderSn,
            "countryCode": e?.site,
            "countryId": CountryID[e?.site],
            "products": Object.values(e?.items).map((product: any) => ({
              sku: product?.platformOuterSkuId,
              quantity: product?.quantity,
              price: product?.discountedAmount
            })),
            "recipientName": e?.consigneeInfo?.name,
            "recipientPhone": e?.consigneeInfo?.phone,
            "recipientAddress": e?.consigneeInfo?.fullAddress,
            "recipientWard": e?.consigneeInfo?.town,
            "recipientDistrict": e?.consigneeInfo?.city,
            "recipientProvince": e?.consigneeInfo?.state,
            "recipientPostCode": e?.consigneeInfo?.zipcode,
            "recipientCountry": e?.consigneeInfo?.country,
            "externalCode": ExternalOrderType[e?.platform],
            "externalProjectId": e?.shopId + ' - ' + e?.shopName,
            "externalProjectName": "Miaoshou",
            "flags": e?.flags,
            "synced": e?.flags?.includes(opOrderFlagId?.toString()),
            "createdAt": new Date(e?.orderInfo?.gmtOrderStart)
          })
        });
        return orders;
      }
      else{
        await this.redisService.del(tokenKey);
        throw new BadRequestException("Unstable network connection. Please try again.");
      }
    })
    .catch((error) => {
      console.log("🚀 ~ MiaoshouService ~ error:", error);
      throw new BadRequestException("Unstable network connection. Please try again.");
    });

    return [res, total];
  }

  async getToken(): Promise<any> {
    try {
      // const wwproxy = new WWPROXY(this.redisService);
      // const proxy = await wwproxy.getCurrentProxy();
      const params = `mobile=${aesEncrypt(process.env.MIAOSHOU_ACCOUNT)}&password=${aesEncrypt(process.env.MIAOSHOU_PASSWORD)}&isVerifyRemoteLogin=true`;

      // const url = 'https://erp.91miaoshou.com/api/auth/account/login';
      // const apikey = '72cb0f63ae769ce776ab9037f58e14b915ed8b22';
      // const res = await axios({
      //   url: 'https://api.zenrows.com/v1/',
      //   method: 'POST',
      //   data: params,
      //   headers: { 
      //     'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      //   },
      //   params: {
      //     'url': url,
      //     'apikey': apikey,
      //     'custom_headers': 'true',
      //   },
      // })
      // .then((response) => {
      //   const cookieMap = {} as TokenMiaoshou;
      //   let setCookie = response?.headers?.['set-cookie'];
        
      //   if(response?.headers?.['zr-cookies']){
      //     setCookie = response?.headers?.['zr-cookies'].split(' ');
      //   }
      //   if(!setCookie) throw new Error("Unstable network connection. Please try again.");

      //   setCookie?.forEach(cookieStr => {
      //     const parts = cookieStr.split(';')[0]; 
      //     const [name, value] = parts.split('=');
      //     cookieMap[name.trim()] = value.trim();
      //   });
      //   return cookieMap;
      // })
      // .catch((error) => {
      //   console.log("🚀 ~ MiaoshouService ~ getToken ~ error:", error?.code, error?.message);
      //   throw error;
      // });

      // const token = res;
      // console.log("🚀 ~ MiaoshouService ~ getToken ~ token:", tokenKey, token);
      // await this.redisService.set(tokenKey, token, {
      //   ttl: moment().add(6, 'days')?.valueOf()
      // })
      // return res;
      
      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://erp.91miaoshou.com/api/auth/account/login',
        headers: { 
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        },
        data : params,
        // httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
        timeout: 8000,
      };

      const res = await axios.request(config)
      .then((response) => {
        // console.log(response?.headers?.['set-cookie']);
        const cookieMap = {} as TokenMiaoshou;

        response?.headers?.['set-cookie'].forEach(cookieStr => {
          const parts = cookieStr.split(';')[0]; 
          const [name, value] = parts.split('=');
          cookieMap[name.trim()] = value.trim();
        });
        // console.log(cookieMap);
        return cookieMap;
      })
      .catch((error) => {
        console.log("🚀 ~ MiaoshouService ~ getToken ~ error:", error?.code, error?.message);
        throw error;
      });

      const token = res;
      // console.log("🚀 ~ MiaoshouService ~ getToken ~ token:", tokenKey, token);
      await this.redisService.set(tokenKey, token, {
        ttl: moment().add(6, 'days')?.valueOf()
      })
      return res;
    } catch (e) {
      console.log("🚀 ~ MiaoshouService ~ getToken ~ e:", e);
      throw e;
    }
  }

  async getAccessToken(): Promise<TokenMiaoshou> {
    const token = await this.redisService.get<TokenMiaoshou>(tokenKey);
    if (token) {
      // console.log("🚀 ~ MiaoshouService ~ getAccessToken ~ token:", token);
      return token;
    }
    return this.getToken();
  }
  
  async verifyOrder(proxy: any, data: CreateOrderDto, token: TokenMiaoshou): Promise<any> {
    const cookieMiaoshou = `acw_tc=${token?.acw_tc}; bsClientId=${token?.bsClientId}; firstClientId=${token?.firstClientId}; mserp=${token?.mserp}; mserp_sst=${token?.mserp_sst}`;
    let params = {
      'pageSize': '1',
      'page': '1',
      'shopIds[0]': data?.shopId?.toString(),
      'platformOrderSns': data?.externalId ?? '',
      'appPackageTab': 'waitProcess',
      'waitProcessTab': 'all',
      'supplierProcessStatus': 'all'
    };

    // const url = 'https://erp.91miaoshou.com/api/order/package/searchOrderPackageList';
    // const apikey = '72cb0f63ae769ce776ab9037f58e14b915ed8b22';
    // return axios({
    //   url: 'https://api.zenrows.com/v1/',
    //   method: 'POST',
    //   data: stringify(params),
    //   headers: { 
    //     'Accept': 'application/json, text/plain, */*', 
    //     'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8', 
    //     'Connection': 'keep-alive', 
    //     'Content-Type': 'application/x-www-form-urlencoded',
    //     'Cookie': cookieMiaoshou
    //   },
    //   params: {
    //     'url': url,
    //     'apikey': apikey,
    //     'custom_headers': 'true',
    //   },
    // }).then(async (response) => {
    //   console.log("🚀 ~ MiaoshouService ~ .then ~ response:", response?.data?.result, response?.data?.packageList?.[0]?.platformOrderSn);
    //   if(response?.data?.result == 'success'){ 
    //     return response?.data?.packageList?.[0];
    //   }
    //   else{
    //     await this.redisService.del(tokenKey);
    //     throw new BadRequestException("Unstable network connection. Please try again.");
    //   }
    // })
    // .catch(async (error) => {
    //   // await this.redisService.del(tokenKey);
    //   console.log("🚀 ~ MiaoshouService ~ verifyOrder ~ error:", error?.code, error?.message);
    //   throw new BadRequestException("Unstable network connection. Please try again.");
    // });
    
    const config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://erp.91miaoshou.com/api/order/package/searchOrderPackageList',
      headers: { 
        'Accept': 'application/json, text/plain, */*', 
        'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8', 
        'Connection': 'keep-alive', 
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': cookieMiaoshou
      },
      data : stringify(params),
      // httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
      timeout: 8000
    };

    return axios.request(config)
    .then(async (response) => {
      if(response?.data?.result == 'success'){ 
        return response?.data?.packageList?.[0];
      }
      else{
        await this.redisService.del(tokenKey);
        throw new BadRequestException("Unstable network connection. Please try again.");
      }
    })
    .catch(async (error) => {
      // await this.redisService.del(tokenKey);
      console.log("🚀 ~ MiaoshouService ~ verifyOrder ~ error:", error?.code, error?.message);
      throw new BadRequestException("Unstable network connection. Please try again.");
    });
  }

  async addFlagOrder(proxy: any, opOrderPackageId: string, flags: string[], token: TokenMiaoshou): Promise<any> {
    const cookieMiaoshou = `acw_tc=${token?.acw_tc}; bsClientId=${token?.bsClientId}; firstClientId=${token?.firstClientId}; mserp=${token?.mserp}; mserp_sst=${token?.mserp_sst}`;
    
    let data = stringify({
      'opOrderPackageId': opOrderPackageId?.toString(),
      'opOrderFlagIds': `[${flags?.map((it) => { return `"${it}"`; })?.join(",")}]`
    })

    // const url = 'https://erp.91miaoshou.com/api/order/purchase/saveOpOrderFlag';
    // const apikey = '72cb0f63ae769ce776ab9037f58e14b915ed8b22';
    // return axios({
    //   url: 'https://api.zenrows.com/v1/',
    //   method: 'POST',
    //   data: data,
    //   headers: { 
    //     'Accept': 'application/json, text/plain, */*', 
    //     'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8', 
    //     'Connection': 'keep-alive', 
    //     'Content-Type': 'application/x-www-form-urlencoded',
    //     'Cookie': cookieMiaoshou
    //   },
    //   params: {
    //     'url': url,
    //     'apikey': apikey,
    //     'custom_headers': 'true',
    //   },
    // })
    // .then((response) => {
    //   console.log("🚀 ~ MiaoshouService ~ addFlagOrder ~ response.data:", data);
    // })
    // .catch((error) => {
    //   console.log("🚀 ~ MiaoshouService ~ addFlagOrder ~ error:", error, data);
    // });

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://erp.91miaoshou.com/api/order/purchase/saveOpOrderFlag',
      headers: { 
        'Accept': 'application/json, text/plain, */*', 
        'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8', 
        'Connection': 'keep-alive', 
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': cookieMiaoshou
      },
      data : data,
      // httpsAgent: proxy ? new HttpsProxyAgent('http://' + proxy) : undefined,
      timeout: 8000
    };

    axios.request(config)
    .then((response) => {
      console.log("🚀 ~ MiaoshouService ~ addFlagOrder ~ response.data:", data);
    })
    .catch((error) => {
      console.log("🚀 ~ MiaoshouService ~ addFlagOrder ~ error:", error, data);
    });

  }

  async createOrder(data: CreateOrderDto, req?: Record<string, any>): Promise<Order>{
    // const wwproxy = new WWPROXY(this.redisService);
    // const proxy = await wwproxy.getCurrentProxy();
    const token = await this.getAccessToken();
    // await this.addFlagOrder(proxy, "*********", concat(["11154642"], [opOrderFlagId]), token);

    const companyId = req?.user?.companyId;
    const shop = await this.mkiRepo
        .createQueryBuilder('mk')
        .andWhere('mk.type = :type', { type: TypeMIEnum.miaoshou })
        .andWhere('mk.apiKey = :apiKey', { apiKey: data?.shopId })
        .andWhere('mk.companyId = :companyId', { companyId })
        .getOne();
    if(!shop?.id) throw new BadRequestException({ code: "SCO_0001", message: "Not found shop" });
    if(!shop?.clientId) throw new BadRequestException({ code: "SCO_0001", message: "Not found shop" });

    const [ord, warehouse, carrier, warehouseReturn] = await Promise.all([
      this.odRepository
        .createQueryBuilder('od')
        .andWhere('od.companyId = :companyId', { companyId })
        // .andWhere('od.countryId = :countryId', { countryId: shop?.countryId })
        .andWhere('od.externalId = :externalId', { externalId: data?.externalId })
        .andWhere('od.externalCode = :externalCode', { externalCode: ExternalOrderType.miaoshou })
        // .andWhere('od.clientId = :clientId', { clientId: shop?.clientId })
        .getOne(),
      this.whcaRepository.createQueryBuilder('wh').leftJoinAndSelect('wh.warehouse', 'warehouse').andWhere('wh.bizId = :companyId', { companyId })
        .andWhere('wh.countryId = :countryId', { countryId: shop?.countryId })
        .andWhere('wh.clientId = :clientId', { clientId: shop?.clientId })
        .getOne(),
      this.carRepository.findOne({
          code: CarrierCode.miaoshou,
        }),
      this.whRepository
      .createQueryBuilder('wh')
      .where({
        countryCode: data?.countryId,
        bizId: `${req?.user?.companyId}`,
        isReturnDefault: true,
      })
      .getOne()
    ]);

    if(ord?.id) throw new BadRequestException({ code: "SCO_0005", message: "The order was synced before. Please check it again!" });
    if(!warehouse?.warehouseId) throw new BadRequestException({ code: "SCO_0004", message: "Not found warehouse" });

    // fetch token
    const odMiaoShou = await this.verifyOrder(null, data, token);
    if(!odMiaoShou || odMiaoShou?.platformOrderSn != data?.externalId) throw new BadRequestException({ code: "SCO_0003", message: "Order platform" });
    const flags = concat(odMiaoShou?.flags ?? [], [opOrderFlagId]);
    
    console.log("🚀 ~ MiaoshouService ~ createOrder ~ odMiaoShou:", odMiaoShou?.opOrderPackageId, odMiaoShou?.flags, opOrderFlagId, uniq(flags));
    // flags, opOrderPackageId
    
    const products = await this.productsService.findProduct({
        sku: data?.products?.map((item) => item?.sku),
        clientId: [shop?.clientId],
        countryId: shop?.countryId,
      }) ?? [];
      
    if(isEmpty(products) || products?.length != data?.products?.length) throw new BadRequestException({ code: "SCO_0002", message: "Products" });
    
    const productLookup: Record<string, any> = reduce(
      products,
      (prev, item) => {
        prev[item?.originSku] = item;
        if (!isEmpty(item?.product?.combo)) {
          for (const element of item?.product?.combo) {
            prev[`${item?.originSku}_${element?.variant?.originSku}`] = element;
          }
        }
        return prev;
      },
      {},
    );
    if (!isEmpty(data?.products)) {
      data.products = data?.products.map((item: any) => {
        const prod = productLookup[item?.sku];
        if (prod?.product?.covers) {
          prod.product.images =
            prod?.images && prod?.images.length > 0 ? prod?.images : prod?.product?.covers;
          delete prod?.product?.covers;
        }
        const orderProductCombos = [];
        if (prod?.product?.isCombo) {
          for (const ele of prod?.product?.combo) {
            if (ele?.variant?.images) {
              ele.variant.product.images = 
                ele?.variant?.images && ele?.variant?.images.length > 0
                  ? ele?.variant?.images 
                  : ele?.variant?.product?.covers;
              delete ele?.variant?.product?.covers;
            }
            const attributes = ele?.variant?.properties
              .map(x => {
                return `${x?.attributes?.name}: ${x?.name}`;
              })
              .join(', ');
            orderProductCombos.push({
              comboSku: prod?.sku,
              productId: prod?.productId,
              sku: ele?.variant?.sku,
              attributes: attributes,
              weight: ele?.variant?.weight,
              qty: ele.qty * item?.quantity,
              variantId: ele.variant.id,
              countryId: shop?.countryId,
              companyId,
            });
          }
        }
        return {
          ...item,
          orderProductCombos,
          weight: Number(prod?.weight ?? 0) * Number(item?.quantity ?? 0),
          productId: prod?.id,
          productName: prod?.product?.name,
          productDetail: prod ?? {},
          creatorId: shop?.clientId,
        };
      });
      // console.log("🚬 ~ MiaoshouService ~ data.products=data?.products.map ~ data:", data?.products?.[0])
    }
    const od = plainToInstance(Order, {
      ...data,
      recipientAddress: data?.recipientAddress?.replace(
        /\*/g,
        'x',
      ),
      recipientName: data?.recipientName?.replace(
        /\*/g,
        'x',
      ),
      recipientPhone: data?.recipientPhone?.replace(
        /\*/g,
        '0',
      )?.replace(
        /^\(\+\d+\)/,
        '',
      ),
      companyId,
      clientId: shop?.clientId,
      countryId: shop?.countryId,
      status: OrderFFMStatus.AwaitingStock,
      warehouseId: warehouse?.warehouseId,
      countryCode: $enum(CountryID).getKeyOrDefault(Number(shop?.countryId), null),
      creatorId: req?.user?.id,
      companyPartnerId: data?.shopId,
      externalProjectId: data?.shopId,
      externalCode: ExternalOrderType.miaoshou,
      seller: data?.externalProjectId,
      recipientCountryId: data?.recipientCountryId,
      recipientPostCode: null,
      recipientDistrict: null,
      recipientWard: null,
      totalWeight: reduce(
        data?.products,
        (prev: number, next: any) => {
          prev += next?.weight;
          return prev;
        },
        0,
      ),
      returnWarehouseId: warehouse?.warehouse?.returnWarehouseId
    });
    if(odMiaoShou?.logisticsNo){
      od.carriers = [
        plainToInstance(OrderCarrier, {
          carrierId: carrier?.id,
          waybillNumber: odMiaoShou?.logisticsNo,
          creatorId: req?.user?.id,
          lastUpdatedBy: req?.user?.id,
        }),
      ];
    }

    if (od.countryId != od.recipientCountryId && warehouseReturn?.id) {
      od.returnWarehouseId = Number(warehouseReturn?.id);
      od.returnCountryId = `${warehouseReturn?.countryCode}`;
    }
    const order = await this.odRepository.save(od).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });

    const code = await this.odRepository.query(
      `SELECT id_encode( ${order?.id}, 'orders', 6,'1234567890QERTYUPLKJHGFDSAZXCVBNM' ) as id`,
    );

    console.log("🚬 ~ MiaoshouService ~ createOrder ~ order:", order?.id, code);
    let orderProductComboIds = [];
    for (const item of order?.products) {
      if (!isEmpty(item?.orderProductCombos) && !isNull(item?.orderId)) {
        orderProductComboIds = orderProductComboIds.concat(item?.orderProductCombos.map(x => x.id));
      }
    }
    if (!isEmpty(orderProductComboIds)) {
      await this.opcvRepository
        .update({ id: In(orderProductComboIds) }, { orderId: order?.id })
        .catch(err => {
          if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
          return err;
        });
    }
    try {
      const calcInventory = await this.stockInventoryInOrder.changeStockInventoryByOrder({
        id: order?.id,
        user: req?.user,
        oldStatus: OrderFFMStatus.AwaitingStock,
        newStatus: OrderFFMStatus.Confirmed,
        lastUpdateStatus: new Date(new Date().getTime() + 5 * 1000),
        updatedAt: moment()?.valueOf(),
        rollBackStatus: false,
        isRequestQueue: true,
      });
      if (calcInventory.status == 200) {
        await this.odRepository
          .update(
            { id: order.id },
            {
              status: odMiaoShou?.logisticsNo ? OrderFFMStatus.AwaitingCollection : OrderFFMStatus.Confirmed
            },
          )
          .catch(err => {
            console.log(err?.driverError?.detail);
          });
      }
    } catch (error) {
      console.log("🚀 ~ MiaoshouService ~ createOrder ~ error:", error);
    }

    // add tag order miaoshou
    if(order?.id) this.addFlagOrder(null, odMiaoShou?.opOrderPackageId, uniq(flags), token);

    return order;
  }

  async trackOrderData(trackingIds: string[]): Promise<any> {
    const token = await this.getAccessToken();
    const cookieMiaoshou = `acw_tc=${token?.acw_tc}; bsClientId=${token?.bsClientId}; firstClientId=${token?.firstClientId}; mserp=${token?.mserp}; mserp_sst=${token?.mserp_sst}`;

    let params = {
      logisticsKeyword: trackingIds,
    };

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://erp.91miaoshou.com/api/order/package/searchOrderPackageList',
      headers: {
        Accept: 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
        Connection: 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        Cookie: cookieMiaoshou,
      },
      data: stringify(params),
      timeout: 8000,
    };

    return axios
      .request(config)
      .then(async response => {
        if (response?.data?.result == 'success') {
          return response?.data?.packageList;
        } else {
          await this.redisService.del(tokenKey);
          return [];
        }
      })
      .catch(error => {
        return [];
      });
  }
}
