import { Nack, <PERSON>RP<PERSON> } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OrderProductComboVariant } from 'apps/ffm-order-api/src/entities/order-product-combo-variant.entity';
import { OrderProduct } from 'apps/ffm-order-api/src/entities/order-product.entity';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';
import { ProductStatus } from 'apps/ffm-order-api/src/enums/stock-inventory.enum';
import { ProductVariation } from 'apps/ffm-order-api/src/read-entities/ffm-catalog/product-variation.entity';
import { instanceToPlain } from 'class-transformer';
import { catalogFfmConnection, orderConnection } from 'core/constants/database-connection.constant';
import { rmqErrorsHandler } from 'core/handlers/rmq-errors.handler';
import { isEmpty, reduce } from 'lodash';
import { Repository } from 'typeorm';

export interface IOrderProduct {
  ids?: number;
  user?: number;
  sku?: string;
  clientId?: number;
  countryId?: number;
}
@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(ProductVariation, catalogFfmConnection)
    private variantRepository: Repository<ProductVariation>,
    @InjectRepository(OrderProduct, orderConnection)
    private opRepository: Repository<OrderProduct>,
    @InjectRepository(Order, orderConnection)
    private odRepository: Repository<Order>,
  ) {}

  async findProduct(payload) {
    console.log('🐔 payload', 'ffm-order-find-product', payload);
    if ((!payload?.ids && !payload?.sku) || !payload?.countryId) {
      throw new BadRequestException('Not enough payload data!');
    }
    const { ids, user, sku, clientId, countryId } = payload;

    const query = this.variantRepository
      .createQueryBuilder('variants')
      .select([
        'variants.id',
        'variants.isDangerous',
        'variants.prefix',
        'variants.productId',
        'variants.size',
        'variants.sku',
        'variants.status',
        'variants.weight',
        'variants.images',
        'variants.priceRetail',
        'variants.countryId',
        'variants.clientId',
        'properties.id',
        'properties.name',
        'properties.status',
        'attributes.id',
        'attributes.name',
        'attributes.status',
        'productVariant.categoryId',
        'productVariant.clientId',
        'productVariant.description',
        'productVariant.id',
        'productVariant.isCombo',
        'productVariant.link',
        'productVariant.name',
        'productVariant.prefix',
        'productVariant.script',
        'productVariant.sku',
        'productVariant.status',
        'productVariant.chineseName',
        'productVariant.covers',
        'variantCombo.id',
        'variantCombo.prefix',
        'variantCombo.productId',
        'variantCombo.size',
        'variantCombo.sku',
        'variantCombo.status',
        'variantCombo.weight',
        'variantCombo.isDangerous',
        'variantCombo.images',
        'variantInfo.name',
        'variantInfo.id',
        'variantInfo.prefix',
        'variantInfo.sku',
        'variantInfo.link',
        'variantInfo.chineseName',
        'variantInfo.covers',
        'variantComboProperties.id',
        'variantComboProperties.name',
        'variantComboProperties.status',
        'variantComboattributes.id',
        'variantComboattributes.name',
        'variantComboattributes.status',
      ])
      .leftJoin('variants.product', 'productVariant')
      .leftJoinAndSelect('productVariant.combo', 'combo', `combo.status = :statusCombo`, {
        statusCombo: ProductStatus.active,
      })
      .leftJoin('combo.variant', 'variantCombo')
      .leftJoin('variantCombo.product', 'variantInfo')
      .leftJoin('variantCombo.properties', 'variantComboProperties')
      .leftJoin('variantComboProperties.attributes', 'variantComboattributes')

      .leftJoin('variants.properties', 'properties')
      .leftJoin('properties.attributes', 'attributes')
      .where('variants.countryId = :countryId', { countryId })
      .andWhere('variants.status = :status', { status: ProductStatus.active })
      .andWhere('productVariant.status = :status', { status: ProductStatus.active });
    if (!isEmpty(ids)) {
      query.andWhere('variants.id IN (:...ids)', { ids });
      query.andWhere('variants.bizId = :bizId', { bizId: user?.companyId });
    }
    if (!isEmpty(sku)) {
      query.andWhere('variants.sku IN (:...sku)', { sku });
    }
    if (clientId) query.andWhere('variants.clientId IN (:...clientId)', { clientId });

    const products = await query.getMany();
    console.log('🐔  ~ ProductsService ~ findProduct ~ query:', products, query.getQueryAndParameters());
    return products.map(p => instanceToPlain(p));
  }

  @RabbitRPC({
    exchange: 'ffm-sync-order',
    routingKey: 'update-product-after-change-wh',
    queue: 'queue-update-product-after-change-wh',
    allowNonJsonMessages: true,
    errorHandler: rmqErrorsHandler,
  })
  async syncAgOrder(payload) {
    console.log('update-product-after-change-wh payload', payload);
    const { orderIds, skus, countryId, clientIds } = payload;
    const orderProducts: OrderProduct[] = await this.opRepository
      .createQueryBuilder('op')
      .addSelect('order.id')
      .addSelect('order.clientId')
      .addSelect('order.companyId')
      .where('op.order_id IN (:...orderIds)', { orderIds })
      .leftJoin('op.order', 'order')
      .getMany();

    //------------------------------------------------------

    let products: any = [];
    try {
      products =
        (await this.findProduct({
          sku: skus,
          countryId: countryId,
          clientId: clientIds,
        })) ?? [];
    } catch (error) {
      console.log(error);
      throw new BadRequestException();
    }

    const productLookup: Record<string, any> = reduce(
      products,
      (prev, item) => {
        const key = `${item?.originSku}_${item?.clientId}`;
        prev[key] = item;
        return prev;
      },
      {},
    );

    for (const item of orderProducts) {
      const sku = item?.productDetail?.originSku;
      const prod = productLookup[`${sku}_${item?.order?.clientId}`];
      if (prod?.product?.covers) {
        prod.product.images =
          prod?.images && prod?.images.length > 0 ? prod?.images : prod?.product?.covers;
        delete prod?.product?.covers;
      }

      item.productId = prod?.id;
      item.productDetail = prod;
      item.productName = prod?.product?.name;

      const newOrderProductCombos: OrderProductComboVariant[] = [];
      if (prod?.product?.isCombo) {
        for (const ele of prod?.product?.combo) {
          if (ele?.variant?.images) {
            ele.variant.product.images =
              ele?.variant?.images && ele?.variant?.images.length > 0
                ? ele?.variant?.images
                : ele?.variant?.product?.covers;
            delete ele?.variant?.product?.covers;
          }
          const attributes = ele.variant.properties
            .map(x => {
              return `${x?.attributes?.name}: ${x?.name}`;
            })
            .join(', ');

          newOrderProductCombos.push({
            comboSku: prod?.sku,
            productId: prod?.productId,
            sku: ele.variant.sku,
            attributes: attributes,
            weight: ele.variant.weight,
            qty: ele.qty * item?.quantity,
            variantId: ele.variant.id,
            countryId: prod?.countryId.toString(),
            companyId: item?.order?.companyId,
            orderId: item?.order_id,
          });
        }
        item.orderProductCombos = newOrderProductCombos;
      }
    }

    const CHUNK_SIZE = 500;
    for (let i = 0; i < orderProducts.length; i += CHUNK_SIZE) {
      const chunk = orderProducts.slice(i, i + CHUNK_SIZE);
      await this.opRepository.save(chunk).catch(err => {
        console.log('update-product-after-change-wh', err?.driverError?.detail);
      });
    }

    return new Nack();
  }
}
