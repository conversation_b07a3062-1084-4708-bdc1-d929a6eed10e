import { Amqp<PERSON>onnection, RabbitRPC, defaultNackErrorHandler } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateReImportDto, ProductItemDto } from 'apps/ffm-order-api/src/dtos/re-import.dto';
import { LeadTime } from 'apps/ffm-order-api/src/entities/lead-time.entity';
import { OrderStatusHistories } from 'apps/ffm-order-api/src/entities/order-status-history.entity';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';
import { ReImportItem } from 'apps/ffm-order-api/src/entities/re-import-item.entity';
import { ReImport } from 'apps/ffm-order-api/src/entities/re-import.entity';
import { LeadTimeTypeEnum } from 'apps/ffm-order-api/src/enums/lead-time.enum';
import {
  <PERSON>sFilter,
  CasesProcessedFilter,
  ReImportTypeEnum,
  TypeReimportFilter,
} from 'apps/ffm-order-api/src/enums/re-import.enum';
import { TypeUpdateOrderStatus } from 'apps/ffm-order-api/src/enums/type-update-order-status.enum';
import { FilterReImport } from 'apps/ffm-order-api/src/filters/order.filter';
import { SlotWarehouses } from 'apps/ffm-order-api/src/read-entities/ffm-catalog/warehouses.entity';
import { plainToInstance } from 'class-transformer';
import {
  catalogFfmConnection,
  identityConnection,
  orderConnection,
} from 'core/constants/database-connection.constant';
import { User } from 'core/entities/identity/user.entity';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { compact, concat, find, isArray, isEmpty, isNil, reduce, split, uniq } from 'lodash';
import * as moment from 'moment-timezone';
import xlsx, { WorkSheet } from 'node-xlsx';
import { $enum } from 'ts-enum-util';
import { Brackets, In, Repository } from 'typeorm';
import { OrderService } from './order.service';
import { OrderProduct } from 'apps/ffm-order-api/src/entities/order-product.entity';
import { OrderProductComboVariant } from 'apps/ffm-order-api/src/entities/order-product-combo-variant.entity';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { StockInventoryInOrder } from './stock-inventory.service';

@Injectable()
export class ReImportService {
  constructor(
    @InjectRepository(Order, orderConnection)
    private odRepository: Repository<Order>,
    @InjectRepository(ReImport, orderConnection)
    private reImportRepository: Repository<ReImport>,
    @InjectRepository(ReImportItem, orderConnection)
    private reImportItemRepository: Repository<ReImportItem>,
    @InjectRepository(LeadTime, orderConnection)
    private leadTimeRepository: Repository<LeadTime>,
    @InjectRepository(SlotWarehouses, catalogFfmConnection)
    private whRepository: Repository<SlotWarehouses>,
    @InjectRepository(User, identityConnection)
    private uRepository: Repository<User>,
    @InjectRepository(OrderProduct, orderConnection)
    private opRepository: Repository<OrderProduct>,
    @InjectRepository(OrderProductComboVariant, orderConnection)
    private opcRepository: Repository<OrderProductComboVariant>,
    private ordersService: OrderService,
    private stockInventoryInOrder: StockInventoryInOrder,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  async queryReImportList(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ) {
    const { warehouseIds, query, carrierIds, from, to } = filters;
    const mQuery = this.odRepository.createQueryBuilder('od');
    mQuery.andWhere(`od.status = :status`, { status: OrderFFMStatus.Canceled });

    mQuery.leftJoin(ReImport, 'ri', `(od.id = ri.order_id)`);
    mQuery.andWhere('ri.order_id IS NULL');

    mQuery.leftJoin(OrderStatusHistories, 'logs', `(od.id = logs.order_id)`);
    mQuery.andWhere(`logs.status = :status`, { status: OrderFFMStatus.Canceled });
    mQuery.andWhere('logs.before_status IN (:...before_status)', {
      before_status: [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.Collecting],
    });

    mQuery.leftJoin('od.carriers', 'carriers');
    mQuery.leftJoin('carriers.carrier', 'carrier');
    mQuery.leftJoin('od.products', 'products');
    mQuery.andWhere('od.return_warehouse_id IN (:...warehouseIds)', { warehouseIds });
    mQuery.andWhere('od.company_id = :companyId', { companyId: request?.user?.companyId });

    // mQuery.andWhere('od.created_at >= :from', { from: new Date('2023-07-03') });
    mQuery.andWhere('od.created_at >= :from', {
      from:
        from ??
        moment()
          .subtract(1, 'months')
          .startOf('month')
          .toDate(),
    });

    mQuery.andWhere('od.created_at <= :to', {
      to:
        to ??
        moment()
          .endOf('day')
          .toDate(),
    });

    if (carrierIds) {
      // mQuery.andWhere(`carriers.status = 'activated'`);
      mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }

    if (query) {
      const splitSearch = split(decodeURIComponent(query), ' ').filter(item => item.trim() !== '');
      if (splitSearch.length > 0) {
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('od.displayId IN (:...similar)', {
              similar: splitSearch,
            }).orWhere('carriers.waybillNumber IN (:...similar)');
          }),
        );
      }
    }

    return mQuery;
  }

  async getListNew(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
    pagination?: PaginationOptions,
  ) {
    const countryId = headers['country-ids'];
    const { warehouseIds, query, carrierIds, from, to, type } = filters;
    const mQuery = this.odRepository.createQueryBuilder('od');

    if (pagination) {
      mQuery.take(pagination.limit).skip(pagination.skip);
    }

    mQuery.select([
      'od.id',
      'od.clientId',
      'od.displayId',
      'od.companyId',
      'od.status',
      'od.warehouseId',
      'od.lastUpdateStatus',
      'od.totalPrice',
      'carriers.id',
      'carriers.waybillNumber',
      'carriers.carrierId',
      'carrier.name',
      'products.productName',
      'products.quantity',
      'products.productDetail',
      'products.id',
      'logs.beforeStatus',
    ]);
    // mQuery.andWhere(`od.status = :status`, { status: OrderFFMStatus.Canceled });
    mQuery.andWhere(`od.status IN (:...statusList)`, {
      statusList: [OrderFFMStatus.Canceled, OrderFFMStatus.InReturn, OrderFFMStatus.Returned],
    });

    mQuery.leftJoin(ReImport, 'ri', `(od.id = ri.order_id)`);
    mQuery.andWhere('ri.order_id IS NULL');

    // mQuery.andWhere(`logs.status = :status`, { status: OrderFFMStatus.Canceled });
    // mQuery.andWhere('logs.before_status IN (:...before_status)', {
    //   before_status: [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.Collecting],
    // });

    if (!type) {
      mQuery.andWhere(
        new Brackets(qb => {
          qb.where('od.status <> :canceled', {
            canceled: OrderFFMStatus.Canceled,
          }).orWhere('(logs.status = :canceled AND logs.before_status IN (:...before_status))', {
            canceled: OrderFFMStatus.Canceled,
            before_status: [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.Collecting],
          });
        }),
      );
      mQuery.andWhere(
        new Brackets(qb => {
          qb.where('od.status <> :inreturn', {
            inreturn: OrderFFMStatus.InReturn,
          }).orWhere(
            `(od.status = :inreturn AND od.last_update_status < NOW() - (leadTime.rules * INTERVAL '1 day'))`,
            {
              inreturn: OrderFFMStatus.InReturn,
            },
          );
        }),
      );
      mQuery.andWhere(
        new Brackets(qb => {
          qb.where('od.status = :canceled and od.warehouse_id IN (:...warehouseIds)', {
            canceled: OrderFFMStatus.Canceled,
            warehouseIds,
          }).orWhere(
            'od.status IN (:...returned) and od.return_warehouse_id IN (:...warehouseIds)',
            {
              returned: [OrderFFMStatus.InReturn, OrderFFMStatus.Returned],
              warehouseIds,
            },
          );
        }),
      );
    } else {
      if (type == TypeReimportFilter.AwaitingPickUpToCanceled) {
        mQuery
          .andWhere('od.status = :canceled', { canceled: OrderFFMStatus.Canceled })
          .andWhere('logs.status = :canceled AND logs.before_status IN (:...before_status)', {
            canceled: OrderFFMStatus.Canceled,
            before_status: [OrderFFMStatus.Awaiting3PLPickup],
          });
        mQuery.andWhere('od.warehouse_id IN (:...warehouseIds)', { warehouseIds });
      }
      if (type == TypeReimportFilter.CollectingToCanceled) {
        mQuery
          .andWhere('od.status = :canceled', { canceled: OrderFFMStatus.Canceled })
          .andWhere('logs.status = :canceled AND logs.before_status IN (:...before_status)', {
            canceled: OrderFFMStatus.Canceled,
            before_status: [OrderFFMStatus.Collecting],
          });
        mQuery.andWhere('od.warehouse_id IN (:...warehouseIds)', { warehouseIds });
      }
      if (type == TypeReimportFilter.Returned) {
        mQuery.andWhere('od.status IN (:...returned)', {
          returned: [OrderFFMStatus.Returned],
        });
        mQuery.andWhere('od.return_warehouse_id IN (:...warehouseIds)', { warehouseIds });
      }
      if (type == TypeReimportFilter.InReturn) {
        mQuery.andWhere(
          `(od.status = :inreturn AND od.last_update_status < NOW() - (leadTime.rules * INTERVAL '1 day'))`,
          { inreturn: OrderFFMStatus.InReturn },
        );
        mQuery.andWhere('od.return_warehouse_id IN (:...warehouseIds)', { warehouseIds });
      }
    }

    mQuery.leftJoin('od.carriers', 'carriers');
    mQuery.leftJoin('carriers.carrier', 'carrier');
    mQuery.leftJoin('od.products', 'products');
    mQuery.leftJoinAndSelect(OrderStatusHistories, 'logs', `(od.id = logs.order_id)`);
    mQuery.leftJoinAndMapOne(
      'carriers.leadTime',
      LeadTime,
      'leadTime',
      `(carriers.carrier_id = leadTime.carrier_id AND leadTime.company_id = ${request?.user?.companyId} AND leadTime.type = ${LeadTimeTypeEnum.InReturnOrder} AND "leadTime"."country_id" = "od"."country_id")`,
    );
    mQuery.andWhere('od.company_id = :companyId', { companyId: request?.user?.companyId });
    // mQuery.andWhere('od.country_id =:countryId', { countryId });

    // mQuery.andWhere('od.created_at >= :from', { from: new Date('2023-07-03') });
    mQuery.andWhere('od.last_update_status >= :from', {
      from:
        from ??
        moment()
          .subtract(1, 'months')
          .startOf('month')
          .toDate(),
    });

    mQuery.andWhere('od.created_at <= :to', {
      to:
        to ??
        moment()
          .endOf('day')
          .toDate(),
    });

    if (carrierIds) {
      // mQuery.andWhere(`carriers.status = 'activated'`);
      mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }

    if (query) {
      const splitSearch = split(decodeURIComponent(query), ' ').filter(item => item.trim() !== '');
      if (splitSearch.length > 0) {
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('od.displayId IN (:...similar)', {
              similar: splitSearch,
            }).orWhere('carriers.waybillNumber IN (:...similar)');
          }),
        );
      }
    }

    mQuery.orderBy('od.lastUpdateStatus', 'DESC');
    const orders = await mQuery.getMany();
    const result = [];
    for (const item of orders) {
      if (item.status == OrderFFMStatus.Canceled) {
        result.push({
          ...item,
          beforeStatus: OrderFFMStatus[item?.before_status],
          lastCarrier: item?.lastCarrier,
        });
      } else {
        result.push({
          ...item,
          beforeStatus: OrderFFMStatus[item?.status],
          lastCarrier: item?.lastCarrier,
        });
      }
    }
    return result;
  }

  async countAwaitingReimport(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ) {
    const countryId = headers['country-ids'];
    const { warehouseIds, query, carrierIds, from, to, type } = filters;
    const mQuery = this.odRepository.createQueryBuilder('od');

    mQuery.select('od.id')
    // mQuery.select([
    //   'od.return_warehouse_id',
    //   `COUNT ( DISTINCT CASE WHEN "od"."status" = ${OrderFFMStatus.Canceled} and logs.before_status = ${OrderFFMStatus.Collecting} and logs.status = ${OrderFFMStatus.Canceled} THEN od."id" ELSE NULL END ) as CollectingToCanceled`,
    //   `COUNT ( DISTINCT CASE WHEN "od"."status" = ${OrderFFMStatus.Canceled} and logs.before_status = ${OrderFFMStatus.Awaiting3PLPickup} and logs.status = ${OrderFFMStatus.Canceled} THEN od."id" ELSE NULL END ) as Awaiting3PLPickupToCanceled`,
    //   `COUNT ( DISTINCT CASE WHEN "od"."status" IN (${OrderFFMStatus.InReturn}) THEN od."id" ELSE NULL END ) as InReturn`,
    //   `COUNT ( DISTINCT CASE WHEN "od"."status" IN (${OrderFFMStatus.Returned}) THEN od."id" ELSE NULL END ) as Returned`,
    // ]);
    mQuery.andWhere(`od.status IN (:...statusList)`, {
      statusList: [OrderFFMStatus.Canceled, OrderFFMStatus.InReturn, OrderFFMStatus.Returned],
    });

    mQuery.leftJoin(ReImport, 'ri', `(od.id = ri.order_id)`);
    mQuery.andWhere('ri.order_id IS NULL');

    if (!type) {
      mQuery.andWhere(
        new Brackets(qb => {
          qb.where('od.status <> :canceled', {
            canceled: OrderFFMStatus.Canceled,
          }).orWhere('(logs.status = :canceled AND logs.before_status IN (:...before_status))', {
            canceled: OrderFFMStatus.Canceled,
            before_status: [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.Collecting],
          });
        }),
      );
      mQuery.andWhere(
        new Brackets(qb => {
          qb.where('od.status <> :inreturn', {
            inreturn: OrderFFMStatus.InReturn,
          }).orWhere(
            `(od.status = :inreturn AND od.last_update_status < NOW() - (leadTime.rules * INTERVAL '1 day'))`,
            {
              inreturn: OrderFFMStatus.InReturn,
            },
          );
        }),
      );
      mQuery.andWhere(
        new Brackets(qb => {
          qb.where('od.status = :canceled and od.warehouse_id IN (:...warehouseIds)', {
            canceled: OrderFFMStatus.Canceled,
            warehouseIds,
          }).orWhere(
            'od.status IN (:...returned) and od.return_warehouse_id IN (:...warehouseIds)',
            {
              returned: [OrderFFMStatus.InReturn, OrderFFMStatus.Returned],
              warehouseIds,
            },
          );
        }),
      );
    } else {
      if (type == TypeReimportFilter.AwaitingPickUpToCanceled) {
        mQuery
          .andWhere('od.status = :canceled', { canceled: OrderFFMStatus.Canceled })
          .andWhere('logs.status = :canceled AND logs.before_status IN (:...before_status)', {
            canceled: OrderFFMStatus.Canceled,
            before_status: [OrderFFMStatus.Awaiting3PLPickup],
          });
        mQuery.andWhere('od.warehouse_id IN (:...warehouseIds)', { warehouseIds });
      }
      if (type == TypeReimportFilter.CollectingToCanceled) {
        mQuery
          .andWhere('od.status = :canceled', { canceled: OrderFFMStatus.Canceled })
          .andWhere('logs.status = :canceled AND logs.before_status IN (:...before_status)', {
            canceled: OrderFFMStatus.Canceled,
            before_status: [OrderFFMStatus.Collecting],
          });
        mQuery.andWhere('od.warehouse_id IN (:...warehouseIds)', { warehouseIds });
      }
      if (type == TypeReimportFilter.Returned) {
        mQuery.andWhere('od.status IN (:...returned)', {
          returned: [OrderFFMStatus.Returned],
        });
        mQuery.andWhere('od.return_warehouse_id IN (:...warehouseIds)', { warehouseIds });
      }
      if (type == TypeReimportFilter.InReturn) {
        mQuery.andWhere(
          `(od.status = :inreturn AND od.last_update_status < NOW() - (leadTime.rules * INTERVAL '1 day'))`,
          { inreturn: OrderFFMStatus.InReturn },
        );
        mQuery.andWhere('od.return_warehouse_id IN (:...warehouseIds)', { warehouseIds });
      }
    }

    mQuery.leftJoin('od.carriers', 'carriers');
    mQuery.leftJoin('carriers.carrier', 'carrier');
    mQuery.leftJoin('od.products', 'products');
    mQuery.leftJoin(OrderStatusHistories, 'logs', `(od.id = logs.order_id)`);
    mQuery.leftJoin(
      LeadTime,
      'leadTime',
      `(carriers.carrier_id = leadTime.carrier_id AND "leadTime"."country_id" = "od"."country_id" AND leadTime.company_id = ${request?.user?.companyId} AND leadTime.type = ${LeadTimeTypeEnum.InReturnOrder})`,
    );

    mQuery.andWhere('od.company_id = :companyId', { companyId: request?.user?.companyId });
    // mQuery.andWhere('od.country_id =:countryId', { countryId });

    mQuery.andWhere('od.last_update_status >= :from', {
      from:
        from ??
        moment()
          .subtract(1, 'months')
          .startOf('month')
          .toDate(),
    });

    mQuery.andWhere('od.created_at <= :to', {
      to:
        to ??
        moment()
          .endOf('day')
          .toDate(),
    });

    if (carrierIds) {
      mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }

    if (query) {
      const splitSearch = split(decodeURIComponent(query), ' ').filter(item => item.trim() !== '');
      if (splitSearch.length > 0) {
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('od.displayId IN (:...similar)', {
              similar: splitSearch,
            }).orWhere('carriers.waybillNumber IN (:...similar)');
          }),
        );
      }
    }

    // mQuery.groupBy('od.return_warehouse_id');
    const ids = await mQuery.getRawMany()

    if(isEmpty(ids)) return {
      return_warehouse_id: 0,
      collectingtocanceled: 0,
      awaiting3plpickuptocanceled: 0,
      inreturn: 0,
      returned: 0,
      noofproducts: 0,
      noofclients: 0,
      nooforders: 0,
      neededqtt: 0,
    }
    const countQb = this.odRepository.createQueryBuilder('od')
    .select([
      'od.return_warehouse_id',
      `COUNT ( DISTINCT CASE WHEN "od"."status" = ${OrderFFMStatus.Canceled} and logs.before_status = ${OrderFFMStatus.Collecting} and logs.status = ${OrderFFMStatus.Canceled} THEN od."id" ELSE NULL END ) as CollectingToCanceled`,
      `COUNT ( DISTINCT CASE WHEN "od"."status" = ${OrderFFMStatus.Canceled} and logs.before_status = ${OrderFFMStatus.Awaiting3PLPickup} and logs.status = ${OrderFFMStatus.Canceled} THEN od."id" ELSE NULL END ) as Awaiting3PLPickupToCanceled`,
      `COUNT ( DISTINCT CASE WHEN "od"."status" IN (${OrderFFMStatus.InReturn}) THEN od."id" ELSE NULL END ) as InReturn`,
      `COUNT ( DISTINCT CASE WHEN "od"."status" IN (${OrderFFMStatus.Returned}) THEN od."id" ELSE NULL END ) as Returned`,
      // `SUM(products.quantity) as NeededQtt`,
      `COUNT ( DISTINCT products."product_id" ) as NoOfProducts`,
      `COUNT ( DISTINCT od."client_id" ) as NoOfClients`,
      `COUNT ( DISTINCT od."id" ) as NoOfOrders`,
    ])
    .groupBy('od.return_warehouse_id')
    .andWhere('od.id IN (:...ids)', {ids: uniq(ids.map(x => Number(x.od_id)))})
    .leftJoin('od.products', 'products')
    .leftJoin(OrderStatusHistories, 'logs', `(od.id = logs.order_id)`);

    const sumQb = this.odRepository.createQueryBuilder('od')
    .select([
      `SUM(products.quantity) as NeededQtt`,
    ])
    .groupBy('od.return_warehouse_id')
    .andWhere('od.id IN (:...ids)', {ids: uniq(ids.map(x => Number(x.od_id)))})
    .leftJoin('od.products', 'products')

    const [count, sum] = await Promise.all([
      countQb.getRawMany(),
      sumQb.getRawMany(),
    ])
    const result = {
      return_warehouse_id: count[0].return_warehouse_id,
      collectingtocanceled: count[0].collectingtocanceled,
      awaiting3plpickuptocanceled: count[0].awaiting3plpickuptocanceled,
      inreturn: count[0].inreturn,
      returned: count[0].returned,
      noofproducts: count[0].noofproducts,
      noofclients: count[0].noofclients,
      nooforders: count[0].nooforders,
      neededqtt: sum[0].neededqtt,
    }
    return result;
  }

  async queryReImportForInReturn(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ) {
    const { warehouseIds, query, carrierIds, from, to } = filters;
    const countryId = headers['country-ids'];
    const mQuery = this.odRepository.createQueryBuilder('od');
    mQuery.andWhere('od.created_at >= :from', {
      from:
        from ??
        moment()
          .subtract(1, 'months')
          .startOf('month')
          .toDate(),
    });

    mQuery.andWhere('od.created_at <= :to', {
      to:
        to ??
        moment()
          .endOf('day')
          .toDate(),
    });

    mQuery.andWhere(`od.status IN (:...before_status)`, {
      before_status: [OrderFFMStatus.InReturn],
    });

    mQuery.leftJoin(ReImport, 'ri', `(od.id = ri.order_id)`);
    mQuery.andWhere('ri.order_id IS NULL');

    // mQuery.leftJoin(OrderStatusHistories, 'logs', `(od.id = logs.order_id)`);
    // mQuery.andWhere('logs.before_status IN (:...before_status)', {
    //   before_status: [OrderFFMStatus.InReturn],
    // });

    mQuery.leftJoin('od.carriers', 'carriers');
    mQuery.leftJoinAndMapOne(
      'carriers.leadTime',
      LeadTime,
      'leadTime',
      `(carriers.carrier_id = leadTime.carrier_id AND leadTime.country_id = '${countryId}' AND leadTime.company_id = ${request?.user?.companyId} AND leadTime.type = ${LeadTimeTypeEnum.InReturnOrder})`,
    );
    mQuery.leftJoin('od.products', 'products');
    mQuery.andWhere('od.return_warehouse_id IN (:...warehouseIds)', { warehouseIds });
    mQuery.andWhere('od.company_id = :companyId', { companyId: request?.user?.companyId });
    mQuery.andWhere('od.country_id =:countryId', { countryId });

    // mQuery.andWhere(`carriers.status = 'activated'`);
    mQuery.leftJoin('carriers.carrier', 'carrier');
    if (carrierIds) {
      mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }

    if (query) {
      const splitSearch = split(decodeURIComponent(query), ' ').filter(item => item.trim() !== '');
      if (splitSearch.length > 0) {
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('od.displayId IN (:...similar)', {
              similar: splitSearch,
            }).orWhere('carriers.waybillNumber IN (:...similar)');
          }),
        );
      }
    }

    return mQuery;
  }

  async queryReImportForReturn(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ) {
    const { warehouseIds, query, carrierIds, from, to } = filters;
    const countryId = headers['country-ids'];
    const mQuery = this.odRepository.createQueryBuilder('od');
    mQuery.andWhere('od.created_at >= :from', {
      from:
        from ??
        moment()
          .subtract(1, 'months')
          .startOf('month')
          .toDate(),
    });

    mQuery.andWhere('od.created_at <= :to', {
      to:
        to ??
        moment()
          .endOf('day')
          .toDate(),
    });
    mQuery.andWhere(`od.status IN (:...before_status)`, {
      before_status: [OrderFFMStatus.Returned],
    });

    mQuery.leftJoin(ReImport, 'ri', `(od.id = ri.order_id)`);
    mQuery.andWhere('ri.order_id IS NULL');

    // mQuery.leftJoin(OrderStatusHistories, 'logs', `(od.id = logs.order_id)`);
    // mQuery.andWhere('logs.before_status IN (:...before_status)', {
    //   before_status: [OrderFFMStatus.Returned],
    // });

    mQuery.leftJoin('od.carriers', 'carriers', `carriers.status = 'activated'`);
    mQuery.leftJoin('carriers.carrier', 'carrier');
    mQuery.leftJoinAndMapOne(
      'carriers.leadTime',
      LeadTime,
      'leadTime',
      `(carriers.carrier_id = leadTime.carrier_id AND leadTime.country_id = '${countryId}') AND leadTime.company_id = ${request?.user?.companyId} AND leadTime.type = ${LeadTimeTypeEnum.ReturnedOrder}`,
    );
    mQuery.leftJoin('od.products', 'products');
    mQuery.andWhere('od.return_warehouse_id IN (:...warehouseIds)', { warehouseIds });
    mQuery.andWhere('od.company_id = :companyId', { companyId: request?.user?.companyId });
    mQuery.andWhere('od.country_id =:countryId', { countryId });

    // mQuery.andWhere(`carriers.status = 'activated'`);
    if (carrierIds) {
      mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }

    if (query) {
      const splitSearch = split(decodeURIComponent(query), ' ').filter(item => item.trim() !== '');
      if (splitSearch.length > 0) {
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('od.displayId IN (:...similar)', {
              similar: splitSearch,
            }).orWhere('carriers.waybillNumber IN (:...similar)');
          }),
        );
      }
    }

    return mQuery;
  }

  async queryReImportForWithin(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ) {
    const { warehouseIds, query, carrierIds, from, to } = filters;
    const countryId = headers['country-ids'];
    const mQuery = this.odRepository.createQueryBuilder('od');
    mQuery.andWhere('od.created_at >= :from', {
      from:
        from ??
        moment()
          .subtract(1, 'months')
          .startOf('month')
          .toDate(),
    });

    mQuery.andWhere('od.created_at <= :to', {
      to:
        to ??
        moment()
          .endOf('day')
          .toDate(),
    });
    mQuery.andWhere(`od.status IN (:...before_status)`, {
      before_status: [OrderFFMStatus.Returned],
    });

    mQuery.leftJoin(ReImport, 'ri', `(od.id = ri.order_id)`);
    mQuery.andWhere('ri.order_id IS NULL');

    // mQuery.leftJoin(OrderStatusHistories, 'logs', `(od.id = logs.order_id)`);
    // mQuery.andWhere('logs.before_status IN (:...before_status)', {
    //   before_status: [OrderFFMStatus.Returned],
    // });

    mQuery.leftJoin('od.carriers', 'carriers', `carriers.status = 'activated'`);
    mQuery.leftJoin('carriers.carrier', 'carrier');
    mQuery.leftJoinAndMapOne(
      'carriers.leadTime',
      LeadTime,
      'leadTime',
      `(carriers.carrier_id = leadTime.carrier_id AND leadTime.country_id = '${countryId}') AND leadTime.company_id = ${request?.user?.companyId} AND leadTime.type = ${LeadTimeTypeEnum.ReturnedOrder}`,
    );
    mQuery.leftJoin('od.products', 'products');
    mQuery.andWhere('od.return_warehouse_id IN (:...warehouseIds)', { warehouseIds });
    mQuery.andWhere('od.company_id = :companyId', { companyId: request?.user?.companyId });
    mQuery.andWhere('od.country_id =:countryId', { countryId });
    mQuery.andWhere(`leadTime.id IS NULL`);

    if (carrierIds) {
      mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }

    if (query) {
      const splitSearch = split(decodeURIComponent(query), ' ').filter(item => item.trim() !== '');
      if (splitSearch.length > 0) {
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('od.displayId IN (:...similar)', {
              similar: splitSearch,
            }).orWhere('carriers.waybillNumber IN (:...similar)');
          }),
        );
      }
    }

    return mQuery;
  }
  async list(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const dataReImportForReturn = await this.getReImportForReturn(filters, headers, request);
    const dataReImportForCancel = filters?.typeFilters?.includes(
      CasesFilter.OrderCanceledDuringPreparation,
    )
      ? await this.getReImportForCancel(filters, headers, request)
      : {};

    return { dataReImportForCancel, dataReImportForReturn };
  }

  async getReImportForCancel(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ) {
    const mQuery = await this.queryReImportList(filters, headers, request);

    mQuery.select([
      'od.id',
      'od.clientId',
      'od.displayId',
      'od.companyId',
      'od.status',
      'od.warehouseId',
      'od.lastUpdateStatus',
      'od.totalPrice',
      'carriers.waybillNumber',
      'carriers.carrierId',
      'carrier.name',
      'products.productName',
      'products.quantity',
      'products.productDetail',
      'products.id',
    ]);

    mQuery.orderBy('od.last_update_status', 'ASC');

    const data = await mQuery.getMany();
    // console.log('🐔  ~ ReImportService ~ data:', mQuery?.getQueryAndParameters());

    const subQb = this.odRepository.createQueryBuilder('od');

    subQb.leftJoin(OrderStatusHistories, 'logs', `(od.id = logs.order_id)`);
    subQb.andWhere(`logs.status = :status`, { status: OrderFFMStatus.Canceled });
    subQb.andWhere('logs.before_status IN (:...before_status)', {
      before_status: [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.Collecting],
    });
    subQb.select(['od.id', 'logs.before_status as before_status']);
    const dataLogs = await subQb.getMany();

    const listBeforeStatus: Record<string, any> = reduce(
      dataLogs,
      (prev, item) => {
        prev[item.id] = $enum(OrderFFMStatus).getKeyOrDefault(Number(item.before_status), null);
        return prev;
      },
      {},
    );
    const result = data.reduce((temp, entry) => {
      const beforeStatus = listBeforeStatus[entry?.id];
      const { before_status, ...res } = entry;
      const beforeStatusKey = listBeforeStatus[entry?.id];
      if (!temp['Awaiting3PLPickup']) {
        temp['Awaiting3PLPickup'] = [];
      }
      if (!temp['Collecting']) {
        temp['Collecting'] = [];
      }
      temp[beforeStatusKey].unshift({ ...res, beforeStatus });
      return temp;
    }, {});

    return result;
  }

  async getReImportForReturn(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ) {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    const mQueryInReturn = await this.queryReImportForInReturn(filters, headers, request);

    mQueryInReturn.select([
      'od.id',
      'od.clientId',
      'od.displayId',
      'od.companyId',
      'od.totalPrice',
      'od.status',
      'od.warehouseId',
      'od.lastUpdateStatus',
      'carriers.waybillNumber',
      'carriers.carrierId',
      'carrier.name',
      'products.productName',
      'products.quantity',
      'products.productDetail',
      'products.id',
      'leadTime.carrierId',
      'leadTime.type',
      'leadTime.rules',
    ]);

    mQueryInReturn.orderBy('od.last_update_status', 'ASC');

    const dataInReturn = filters?.typeFilters?.includes(CasesFilter.OrdersOnTheWayBackToWarehouse)
      ? await mQueryInReturn.getMany()
      : [];

    const mQueryReturn = await this.queryReImportForReturn(filters, headers, request);

    mQueryReturn.select([
      'od.id',
      'od.clientId',
      'od.displayId',
      'od.companyId',
      'od.totalPrice',
      'od.status',
      'od.warehouseId',
      'od.lastUpdateStatus',
      'carriers.waybillNumber',
      'carriers.carrierId',
      'carrier.name',
      'products.productName',
      'products.quantity',
      'products.productDetail',
      'products.id',
      'leadTime.carrierId',
      'leadTime.type',
      'leadTime.rules',
    ]);

    mQueryReturn.orderBy('od.last_update_status', 'ASC');

    const dataReturn = filters?.typeFilters?.includes(CasesFilter.OrdersReturnedToWarehouse)
      ? await mQueryReturn.getMany()
      : [];

    const leadTime = await this.leadTimeRepository
      .createQueryBuilder('lt')
      .andWhere('lt.company_id = :companyId', { companyId })
      .andWhere('lt.country_id = :countryId', { countryId })
      .getMany();

    const mQueryWithin = await this.queryReImportForWithin(filters, headers, request);
    mQueryWithin.select([
      'od.id',
      'od.clientId',
      'od.displayId',
      'od.companyId',
      'od.status',
      'od.warehouseId',
      'od.lastUpdateStatus',
      'od.totalPrice',
      'carriers.waybillNumber',
      'carriers.carrierId',
      'carrier.name',
      'products.productName',
      'products.quantity',
      'products.productDetail',
      'products.id',
      'leadTime.carrierId',
      'leadTime.type',
      'leadTime.rules',
    ]);

    mQueryWithin.orderBy('od.last_update_status', 'ASC');
    const dataWithin = filters?.typeFilters?.includes(CasesFilter.OrdersReturnedToWarehouse)
      ? await mQueryWithin.getMany()
      : [];

    const InReturnForMoreThan = [];
    const ReturnForMoreThan = [];
    const WithinTheReturn = [];
    for (const item of dataInReturn) {
      const timeChange = moment(new Date()).diff(item?.lastUpdateStatus);
      if (!item?.lastCarrier?.leadTime) continue;
      if (moment.duration(timeChange).asDays() > item?.lastCarrier?.leadTime?.rules)
        InReturnForMoreThan.unshift({ ...item, timeChange });
    }

    for (const item of dataReturn) {
      const timeChange = moment(new Date()).diff(item?.lastUpdateStatus);
      if (!item?.lastCarrier?.leadTime) continue;
      if (moment.duration(timeChange).asDays() > item?.lastCarrier?.leadTime?.rules) {
        ReturnForMoreThan.unshift({ ...item, timeChange });
      } else {
        WithinTheReturn.unshift({ ...item, timeChange });
      }
    }

    for (const item of dataWithin) {
      const timeChange = moment(new Date()).diff(item?.lastUpdateStatus);
      WithinTheReturn.unshift({ ...item, timeChange });
    }
    const result: Record<string, any> = {
      MoreThan: {},
      Within: WithinTheReturn.sort((a, b) => a?.timeChange - b?.timeChange),
    };

    for (const item of leadTime) {
      result.MoreThan[item?.carrierId] = { InReturnForMoreThan: [], ReturnForMoreThan: [] };
    }

    for (const item of leadTime) {
      result.MoreThan[item?.carrierId].InReturnForMoreThan =
        InReturnForMoreThan.filter(
          x => Number(x.carriers[0].carrierId) == Number(item?.carrierId),
        ) ?? [];
      result.MoreThan[item?.carrierId].ReturnForMoreThan =
        ReturnForMoreThan.filter(x => Number(x.carriers[0].carrierId) == Number(item?.carrierId)) ??
        [];
    }

    return result;
  }

  async getCountOrders(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<number> {
    const mQuery = await this.queryReImportList(filters, headers, request);
    mQuery.select(['od.id']);
    const dataCancel = await mQuery.getCount();

    const mQueryInReturn = await this.queryReImportForInReturn(filters, headers, request);
    // mQueryInReturn.select(['od.id']);
    mQueryInReturn.andWhere(`leadTime.carrier_id IS NOT NULL`);
    mQueryInReturn.select([
      'od.id',
      'od.lastUpdateStatus',
      'carriers.waybillNumber',
      'leadTime.rules',
    ]);

    const dataInReturn = await mQueryInReturn.getMany();

    const mQueryWithin = await this.queryReImportForWithin(filters, headers, request);
    mQueryWithin.select(['od.id']);

    const dataWithin = await mQueryWithin.getCount();

    const InReturnForMoreThan = [];
    for (const item of dataInReturn) {
      const timeChange = moment(new Date()).diff(item?.lastUpdateStatus);
      if (!item?.lastCarrier?.leadTime) continue;
      if (moment.duration(timeChange).asDays() > item?.lastCarrier?.leadTime?.rules)
        InReturnForMoreThan[item?.id] = item;
    }
    const countDataInReturn = compact(InReturnForMoreThan)?.length;

    const mQueryReturn = await this.queryReImportForReturn(filters, headers, request);
    mQueryReturn.select(['od.id']);
    mQueryReturn.andWhere(`leadTime.carrier_id IS NOT NULL`);
    const dataReturn = await mQueryReturn.getCount();

    const result = dataCancel + countDataInReturn + dataReturn + dataWithin;
    return result;
  }

  async createReImport(
    data: CreateReImportDto,
    headers: Record<string, any>,
    request: Record<string, any>,
  ): Promise<ReImport> {
    const countryId = headers['country-ids'];
    const order = await this.odRepository
      .createQueryBuilder('od')
      .leftJoin('od.carriers', 'carriers')
      .leftJoin('od.products', 'products')
      .andWhere('od.id = :orderId', { orderId: data?.orderId })
      .andWhere('od.company_id = :companyId', { companyId: request?.user?.companyId })
      .select([
        'od.id',
        'od.displayId',
        'od.warehouseId',
        'od.returnWarehouseId',
        'od.clientId',
        'od.status',
        'od.externalId',
        'od.countryId',
        'carriers.waybillNumber',
        'products.productDetail',
        'products.quantity',
        'products.productName',
        'products.id',
      ])
      .getOne();

    const warehouseId =
      data?.type == ReImportTypeEnum.Cancel ? order?.warehouseId : order?.returnWarehouseId;
    if (headers['country-ids'].includes(warehouseId))
      throw new BadRequestException('Đơn hàng này không nằm trong kho hàng chỉ định');

    console.log(order);

    const reImport = plainToInstance(ReImport, {
      orderId: order?.id,
      creatorId: request?.user?.id,
      lastUpdatedBy: request?.user?.id,
      waybillNumber: order?.lastCarrier?.waybillNumber,
      orderDisplayId: order?.displayId,
      warehouseId: warehouseId,
      companyId: request?.user?.companyId,
      clientId: order?.clientId,
      statusBeforeChanged: data?.statusBeforeChanged,
      type: data?.type,
    });
    //-------------------------------
    let productExtend = [];
    for (const item of order?.products) {
      if (item?.productDetail?.product?.isCombo != true) {
        productExtend.push({
          ...item,
          productId: item?.productDetail?.id,
          itemId: item?.id,
          quantity: 1,
        });
      } else {
        const combo = item?.productDetail?.product?.combo?.map(x => {
          return {
            ...x,
            quantity: x.qty,
            productDetail: x?.variant,
            productName: x?.variant?.product?.name,
            productId: x?.variant?.id,
            productComboId: item?.productDetail?.id,
            itemId: item?.id,
          };
        });
        productExtend = productExtend.concat(combo);
      }
    }
    //-------------------------------
    const productLookup = {};
    const productIds = [];
    for (const prod of data?.products) {
      if (!productIds.includes(`${prod?.productId}_${prod?.itemId}`)) {
        productIds.push(`${prod?.productId}_${prod?.itemId}`);
        productLookup[`${prod?.productId}_${prod?.itemId}`] = prod;
      } else {
        productLookup[`${prod?.productId}_${prod?.itemId}`].good += prod?.good;
        productLookup[`${prod?.productId}_${prod?.itemId}`].damaged += prod?.damaged;
        productLookup[`${prod?.productId}_${prod?.itemId}`].damagedBy3pl += prod?.damagedBy3pl;
        productLookup[`${prod?.productId}_${prod?.itemId}`].lost += prod?.lost;
        productLookup[`${prod?.productId}_${prod?.itemId}`].lostBy3pl += prod?.lostBy3pl;
        productLookup[`${prod?.productId}_${prod?.itemId}`].needed += prod?.needed;
      }
    }

    const products = [];
    for (const prod of productExtend) {
      const prodId = prod?.productComboId ? prod?.productComboId : prod?.productDetail?.id;
      products.push({
        ...prod,
        good: prod?.quantity * productLookup[`${prodId}_${prod?.itemId}`]?.good,
        damaged: prod?.quantity * productLookup[`${prodId}_${prod?.itemId}`]?.damaged,
        damagedBy3pl: prod?.quantity * productLookup[`${prodId}_${prod?.itemId}`]?.damagedBy3pl,
        lost: prod?.quantity * productLookup[`${prodId}_${prod?.itemId}`]?.lost,
        lostBy3pl: prod?.quantity * productLookup[`${prodId}_${prod?.itemId}`]?.lostBy3pl,
        needed: prod?.quantity * productLookup[`${prodId}_${prod?.itemId}`]?.needed,
      });
    }

    if (!isEmpty(products))
      reImport.products = products.map(it => {
        const item = plainToInstance(ReImportItem, {
          orderId: data?.orderId,
          productId: it?.productId,
          productName: it?.productName,
          sku: it?.productDetail?.originSku,
          prefix: it?.productDetail?.prefix,
          good: it?.good,
          damaged: it?.damaged,
          damagedBy3pl: it?.damagedBy3pl,
          lost: it?.lost,
          lostBy3pl: it?.lostBy3pl,
          needed: it?.needed,
          properties: it?.productDetail?.properties,
          itemId: it?.id || it?.itemId,
        });
        if (it?.good + it?.damaged + it?.damagedBy3pl + it?.lost + it?.lostBy3pl != it?.needed)
          throw new BadRequestException(
            `Tổng số lượng Good, Damaged, Lost của sản phẩm ${it?.productName} chưa bằng Needed!`,
          );
        return item;
      });

    // throw new BadRequestException(reImport.products)
    const totalDamaged = reduce(
      reImport?.products,
      (p: any, n: any) => {
        return (p += n?.damaged);
      },
      0,
    );
    const totalDamagedBy3pl = reduce(
      reImport?.products,
      (p: any, n: any) => {
        return (p += n?.damagedBy3pl);
      },
      0,
    );
    const totalLost = reduce(
      reImport?.products,
      (p: any, n: any) => {
        return (p += n?.lost);
      },
      0,
    );
    const totalLostBy3pl = reduce(
      reImport?.products,
      (p: any, n: any) => {
        return (p += n?.lostBy3pl);
      },
      0,
    );
    let newStatus: OrderFFMStatus = null;
    if (reImport?.type == ReImportTypeEnum.Cancel) {
      if (totalLost > 0) {
        newStatus = OrderFFMStatus.LostByWH;
      } else if (totalDamaged > 0 && totalLost == 0) {
        newStatus = OrderFFMStatus.DamagedByWH;
      }
    } else if (reImport?.type == ReImportTypeEnum.Return) {
      if (totalLostBy3pl > 0) {
        newStatus = OrderFFMStatus.LostBy3PL;
      } else if (totalLostBy3pl == 0 && totalDamagedBy3pl > 0) {
        newStatus = OrderFFMStatus.ReturnedDamagedBy3PL;
      } else if (totalLostBy3pl == 0 && totalDamagedBy3pl == 0 && totalLost > 0) {
        newStatus = OrderFFMStatus.LostByWH;
      } else if (
        totalLostBy3pl == 0 &&
        totalDamagedBy3pl == 0 &&
        totalLost == 0 &&
        totalDamaged > 0
      ) {
        newStatus = OrderFFMStatus.DamagedByWH;
      } else if (
        totalDamaged == 0 &&
        totalDamagedBy3pl == 0 &&
        totalLost == 0 &&
        totalLostBy3pl == 0
      ) {
        newStatus = OrderFFMStatus.ReturnedStocked;
      }
    }
    // const res: Record<string, any> = await this.amqpConnection.request({
    //   exchange: 'stock-inventory-service',
    //   routingKey: 'reimport-change-stock',
    //   payload: {
    //     user: request?.user,
    //     countryId,
    //     reImport,
    //     nextStatus: newStatus ? newStatus : order?.status,
    //     type: data?.type,
    //     currentStatus: order?.status,
    //     updatedAt: moment(order?.lastUpdateStatus ?? order?.updatedAt)?.valueOf(),
    //   },
    //   timeout: 5000,
    // });
    const payloadReimportProd = [];
    for (const item of reImport.products) {
      payloadReimportProd.push({
        orderId: item?.orderId,
        itemId: item?.itemId,
        productId: item?.productId,
        productName: item?.productName,
        sku: item?.sku,
        good: item?.good,
        damaged: item?.damaged,
        damagedBy3pl: item?.damagedBy3pl,
        lost: item?.lost,
        lostBy3pl: item?.lostBy3pl,
        needed: item?.needed,
      })
    }
    const payloadReimport = {
      countryId,
      clientId: order?.clientId,
      orderId: order?.id,
      warehouseId,
      orderDisplayId: order?.displayId,
      products: payloadReimportProd,
    } 
    let res;
    if(order?.countryId == countryId){
      res = await this.stockInventoryInOrder.reimportChangeStockInventory({
        user: request?.user,
        reImport: payloadReimport,
        nextStatus: newStatus ? newStatus : order?.status,
        type: data?.type,
        currentStatus: order?.status,
        updatedAt: moment(order?.lastUpdateStatus ?? order?.updatedAt)?.valueOf(),
      });
    }else{
      res = await this.stockInventoryInOrder.reimportChangeStockInventoryMultiMarket({
        user: request?.user,
        reImport: payloadReimport,
        nextStatus: newStatus ? newStatus : order?.status,
        type: data?.type,
        currentStatus: order?.status,
        updatedAt: moment(order?.lastUpdateStatus ?? order?.updatedAt)?.valueOf(),
      });
    }

    if (res?.status === 200) {
      console.log('🐔  ~ ReImportService ~ newStatus:', newStatus);
      if (newStatus) {
        await this.odRepository.update(data?.orderId, {
          status: newStatus,
          lastUpdatedBy: request?.user?.id,
          typeUpdateStatus:
            data?.type == ReImportTypeEnum.Cancel
              ? TypeUpdateOrderStatus.ReImportOrderCancel
              : TypeUpdateOrderStatus.ReImportOrderReturned,
        });
        // console.log('🐔  ~ ReImportService ~ newStatus:', data?.orderId, {
        //   status: newStatus,
        //   lastUpdatedBy: request?.user?.id,
        //   typeUpdateStatus:
        //     data?.type == ReImportTypeEnum.Cancel
        //       ? TypeUpdateOrderStatus.ReImportOrderCancel
        //       : TypeUpdateOrderStatus.ReImportOrderReturned,
        // });
        if (!!order?.externalId) {
          await this.amqpConnection.publish('ffm-sync-order', 'sync-order-2-1', {
            id: data?.orderId,
            user: request?.user,
            updatedAt: new Date(),
          });
        }
        if (
          order?.id &&
          [OrderFFMStatus.ReturnedStocked, OrderFFMStatus.ReturnedCompleted].includes(newStatus)
        ) {
          await this.amqpConnection.publish('ffm-order', 'ffm-queue-auto-generate-remittance', {
            orderId: order?.id,
          });
        }
      }
    } else {
      const mess = res?.message == 'not found SKU in return Warehouse' ? res?.message : 'Error inventory calc!'
      throw new BadRequestException(mess);
    }
    console.log(reImport);
    if (data?.orderId) {
      await this.amqpConnection.publish('ffm-order', 'order-update-ticket-status', {
        id: data.orderId,
        user: request.user,
        status: newStatus,
      });
    }
    if (OrderFFMStatus.ReturnedStocked == newStatus) {
      await this.ordersService.removeNeedRetryTicketWarningByOrderStatus(data?.orderId);
    }
    return await this.reImportRepository.save(reImport).catch(err => {
      throw new BadRequestException(err?.driverError);
    });
  }

  async getReImports(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
    pagination?: PaginationOptions,
  ): Promise<any> {
    const { from, query, to, warehouseIds, typeProcessedFilters, carrierIds, type } = filters;
    const mQuery = this.reImportRepository.createQueryBuilder('ri');
    // mQuery.select([
    //   'ri.*',
    //   'products.damaged',
    //   'products.damagedBy3pl',
    //   'products.good',
    //   'products.id',
    //   'products.lost',
    //   'products.lostBy3pl',
    //   'products.needed',
    //   'products.productId',
    //   'products.productName',
    //   'products.sku'
    // ]);
    mQuery.addSelect([
      'carriers.carrierId',
      'carriers.carrierId',
      'carrier.name',
      'od.status',
      'od.id',
      'od.totalPrice',
    ]);

    if (pagination) {
      mQuery.take(pagination.limit).skip(pagination.skip);
    }

    mQuery.leftJoinAndSelect('ri.products', 'products');

    mQuery.andWhere('ri.warehouse_id IN (:...warehouseIds)', { warehouseIds });
    mQuery.andWhere('ri.company_id = :companyId', { companyId: request?.user?.companyId });

    if (from)
      mQuery.andWhere('ri.created_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      mQuery.andWhere('ri.created_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });

    if (!isEmpty(typeProcessedFilters)) {
      if (!typeProcessedFilters.includes(CasesProcessedFilter.OrderCanceledDuringPreparation)) {
        mQuery.andWhere(`ri.type = ${ReImportTypeEnum.Return}`);
      }
      if (
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedInGoodCondition) &&
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButLost) &&
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButDamaged)
      ) {
        mQuery.andWhere(`ri.type = ${ReImportTypeEnum.Cancel}`);
      }

      if (
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedInGoodCondition) ||
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButLost) ||
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButDamaged)
      ) {
        mQuery.andWhere(
          new Brackets(qb => {
            if (typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButLost)) {
              qb.orWhere(`(products.lost > 0 OR products.lost_by_3pl > 0)`);
            }
            if (typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButDamaged)) {
              qb.orWhere(`(products.damaged > 0 OR products.damaged_by_3pl > 0)`);
            }
          }),
        );

        if (typeProcessedFilters.includes(CasesProcessedFilter.ReturnedInGoodCondition)) {
          const subQ = this.reImportRepository.createQueryBuilder('ri');
          subQ.select('ri.id as id');
          subQ.andWhere('ri.warehouse_id IN (:...warehouseIds)', { warehouseIds });
          subQ.andWhere('ri.company_id = :companyId', { companyId: request?.user?.companyId });
          subQ.leftJoin('ri.products', 'products');

          if (from)
            subQ.andWhere('ri.created_at >= :from', {
              from:
                from ??
                moment()
                  .subtract(30, 'days')
                  .startOf('day')
                  .toDate(),
            });
          if (to)
            subQ.andWhere('ri.created_at <= :to', {
              to:
                to ??
                moment()
                  .endOf('day')
                  .toDate(),
            });

          subQ.andWhere(
            new Brackets(qb => {
              if (!typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButLost)) {
                qb.orWhere(`(products.lost > 0 OR products.lost_by_3pl > 0)`);
              }
              if (!typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButDamaged)) {
                qb.orWhere(`(products.damaged > 0 OR products.damaged_by_3pl > 0)`);
              }
            }),
          );
          mQuery.leftJoin(`(${subQ.getQuery()})`, 'sub', 'sub.id = ri.id', subQ.getParameters());
          mQuery.andWhere('sub.id is null');
        }
      }
    }

    if (type) {
      if (type == TypeReimportFilter.CollectingToCanceled) {
        mQuery.andWhere(`ri.type = :type`, { type: ReImportTypeEnum.Cancel });
        mQuery.andWhere(`ri.status_before_changed = :beforeStatus`, {
          beforeStatus: OrderFFMStatus.Collecting,
        });
      }
      if (type == TypeReimportFilter.AwaitingPickUpToCanceled) {
        mQuery.andWhere(`ri.type = :type`, { type: ReImportTypeEnum.Cancel });
        mQuery.andWhere(`ri.status_before_changed = :beforeStatus`, {
          beforeStatus: OrderFFMStatus.Awaiting3PLPickup,
        });
      }
      if (type == TypeReimportFilter.Returned) {
        mQuery.andWhere(`ri.type = :type`, { type: ReImportTypeEnum.Return });
        mQuery.andWhere(`ri.status_before_changed = :beforeStatus`, {
          beforeStatus: OrderFFMStatus.Returned,
        });
      }
      if (type == TypeReimportFilter.InReturn) {
        mQuery.andWhere(`ri.type = :type`, { type: ReImportTypeEnum.Return });
        mQuery.andWhere(`ri.status_before_changed = :beforeStatus`, {
          beforeStatus: OrderFFMStatus.InReturn,
        });
      }
    }
    mQuery.leftJoin('ri.order', 'od');
    // mQuery.leftJoinAndSelect(Order, 'od', `(od.id = ri.order_id)`);
    mQuery.leftJoin('od.carriers', 'carriers');
    mQuery.leftJoinAndSelect('carriers.carrier', 'carrier');
    // mQuery.andWhere(`carriers.status = 'activated'`);
    if (carrierIds) {
      mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }

    if (query) {
      const splitSearch = split(query, ' ').filter(item => item.trim() !== '');
      if (splitSearch.length > 0) {
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('od.displayId IN (:...similar)', {
              similar: splitSearch,
            }).orWhere('carriers.waybillNumber IN (:...similar)');
          }),
        );
      }
    }
    mQuery.orderBy('ri.createdAt', 'DESC');
    const data = await mQuery.getMany();
    return data;
  }

  async countProcessedReimport(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { from, query, to, warehouseIds, carrierIds, type, typeProcessedFilters } = filters;
    const mQuery = this.reImportRepository.createQueryBuilder('ri');
    mQuery.select(['ri.id']);
    mQuery.leftJoin('ri.products', 'products');

    mQuery.andWhere('ri.warehouse_id IN (:...warehouseIds)', { warehouseIds });
    mQuery.andWhere('ri.company_id = :companyId', { companyId: request?.user?.companyId });

    if (from)
      mQuery.andWhere('ri.created_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      mQuery.andWhere('ri.created_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });
    if (!isEmpty(typeProcessedFilters)) {
      if (!typeProcessedFilters.includes(CasesProcessedFilter.OrderCanceledDuringPreparation)) {
        mQuery.andWhere(`ri.type = ${ReImportTypeEnum.Return}`);
      }
      if (
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedInGoodCondition) &&
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButLost) &&
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButDamaged)
      ) {
        mQuery.andWhere(`ri.type = ${ReImportTypeEnum.Cancel}`);
      }

      if (
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedInGoodCondition) ||
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButLost) ||
        !typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButDamaged)
      ) {
        mQuery.andWhere(
          new Brackets(qb => {
            if (typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButLost)) {
              qb.orWhere(`(products.lost > 0 OR products.lost_by_3pl > 0)`);
            }
            if (typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButDamaged)) {
              qb.orWhere(`(products.damaged > 0 OR products.damaged_by_3pl > 0)`);
            }
          }),
        );

        if (typeProcessedFilters.includes(CasesProcessedFilter.ReturnedInGoodCondition)) {
          const subQ = this.reImportRepository.createQueryBuilder('ri');
          subQ.select('ri.id as id');
          subQ.andWhere('ri.warehouse_id IN (:...warehouseIds)', { warehouseIds });
          subQ.andWhere('ri.company_id = :companyId', { companyId: request?.user?.companyId });
          subQ.leftJoin('ri.products', 'products');

          if (from)
            subQ.andWhere('ri.created_at >= :from', {
              from:
                from ??
                moment()
                  .subtract(30, 'days')
                  .startOf('day')
                  .toDate(),
            });
          if (to)
            subQ.andWhere('ri.created_at <= :to', {
              to:
                to ??
                moment()
                  .endOf('day')
                  .toDate(),
            });

          subQ.andWhere(
            new Brackets(qb => {
              if (!typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButLost)) {
                qb.orWhere(`(products.lost > 0 OR products.lost_by_3pl > 0)`);
              }
              if (!typeProcessedFilters.includes(CasesProcessedFilter.ReturnedButDamaged)) {
                qb.orWhere(`(products.damaged > 0 OR products.damaged_by_3pl > 0)`);
              }
            }),
          );
          mQuery.leftJoin(`(${subQ.getQuery()})`, 'sub', 'sub.id = ri.id', subQ.getParameters());
          mQuery.andWhere('sub.id is null');
        }
      }
    }

    if (type) {
      if (type == TypeReimportFilter.CollectingToCanceled) {
        mQuery.andWhere(`ri.type = :type`, { type: ReImportTypeEnum.Cancel });
        mQuery.andWhere(`ri.status_before_changed = :beforeStatus`, {
          beforeStatus: OrderFFMStatus.Collecting,
        });
      }
      if (type == TypeReimportFilter.AwaitingPickUpToCanceled) {
        mQuery.andWhere(`ri.type = :type`, { type: ReImportTypeEnum.Cancel });
        mQuery.andWhere(`ri.status_before_changed = :beforeStatus`, {
          beforeStatus: OrderFFMStatus.Awaiting3PLPickup,
        });
      }
      if (type == TypeReimportFilter.Returned) {
        mQuery.andWhere(`ri.type = :type`, { type: ReImportTypeEnum.Return });
        mQuery.andWhere(`ri.status_before_changed = :beforeStatus`, {
          beforeStatus: OrderFFMStatus.Returned,
        });
      }
      if (type == TypeReimportFilter.InReturn) {
        mQuery.andWhere(`ri.type = :type`, { type: ReImportTypeEnum.Return });
        mQuery.andWhere(`ri.status_before_changed = :beforeStatus`, {
          beforeStatus: OrderFFMStatus.InReturn,
        });
      }
    }
    mQuery.leftJoin(Order, 'od', `(od.id = ri.order_id)`);
    mQuery.leftJoin('od.carriers', 'carriers');
    // mQuery.leftJoin('carriers.carrier', 'carrier');
    // mQuery.andWhere(`carriers.status = 'activated'`);
    if (carrierIds) {
      mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    }

    if (query) {
      const splitSearch = split(query, ' ').filter(item => item.trim() !== '');
      if (splitSearch.length > 0) {
        mQuery.andWhere(
          new Brackets(qb => {
            qb.where('od.displayId IN (:...similar)', {
              similar: splitSearch,
            }).orWhere('carriers.waybillNumber IN (:...similar)');
          }),
        );
      }
    }
    // mQuery.groupBy('ri.warehouse_id');
    const ids = await mQuery.getRawMany();
    if(isEmpty(ids)) return {};
    const result = this.reImportRepository.createQueryBuilder('ri')
    .select([
      `COUNT ( DISTINCT products."product_id" ) as NoOfProducts`,
      `COUNT ( DISTINCT ri."client_id" ) as NoOfClients`,
      `COUNT ( DISTINCT ri."order_id" ) as NoOfOrders`,
      `SUM(products.needed) as NeededQtt`,
    ])
    .leftJoin('ri.products', 'products')
    .andWhere('ri.id IN (:...ids)', {ids: uniq(ids.map(x => Number(x.ri_id)))})
    .groupBy('ri.warehouse_id')
    .getRawMany();

    return result;
  }

  async syncReImport() {
    //   const mQuery = this.reImportRepository.createQueryBuilder('r');
    //   mQuery.andWhere('r.company_id = :companyId', { companyId: 3 });
    //   mQuery.andWhere('r.id <= :id', { id: 188 });
    //   const data = await mQuery.getMany();

    //   data?.forEach((item: any) => {
    //     this.amqpConnection.publish('stock-inventory-service', 'return-order-stock', {
    //       id: item?.id,
    //       user: {
    //         companyId: 3,
    //         id: item?.creatorId,
    //         warehouses: [`${item?.warehouseId}`],
    //       },
    //     });
    //   });

    return 'Oce';
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 'get-return-order',
    queue: 'ffm-queue-get-return-order',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async getReturnOrder({ id, user }) {
    const { companyId } = user;

    const mQuery = this.reImportRepository.createQueryBuilder('r');
    mQuery
      .andWhere({
        companyId,
      })
      .leftJoinAndSelect('r.products', 'products');

    if (!!id) {
      mQuery.andWhere({
        id,
      });
    }
    const returnOrder = await mQuery.getOne();
    return !!returnOrder ? returnOrder : false;
  }

  @RabbitRPC({
    exchange: 'ffm-order',
    routingKey: 're-import-order-cancel',
    queue: 'ffm-queue-re-import-order-cancel',
    allowNonJsonMessages: true,
    errorHandler: defaultNackErrorHandler,
  })
  async getListReturnOrder({ ids, user }) {
    const { companyId } = user;

    const mQuery = this.reImportRepository.createQueryBuilder('r');
    mQuery.andWhere({
      companyId,
    });

    if (!!ids) {
      mQuery.andWhere({
        id: In(ids),
      });
    }
    mQuery.select(['r.id', 'r.orderId', 'r.orderDisplayId']);

    return mQuery.getMany();
  }

  async exportAwaitingReimport(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const whIds = headers['warehouse-ids']?.split(',');
    const result = await this.getListNew(filters, headers, request);
    let params = [];

    const [whs, users] = await Promise.all([
      !!result
        ? this.whRepository.find({
            where: {
              id: In(whIds),
              bizId: companyId,
            },
          })
        : [],
      !!result ? this.uRepository.createQueryBuilder().getMany() : [],
    ]);

    let index = 1;
    params = reduce(
      result,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          const wh: any =
            next?.warehouseId && whs?.length > 0
              ? find(whs, ['id', next?.warehouseId?.toString()])
              : {};
          const sku = next?.products.map(x => x?.productDetail?.originSku).join(', ');
          const productName = next?.products.map(x => x?.productName).join(', ');

          const totalNeed = reduce(
            next?.products,
            (p: any, n: any) => {
              p += n?.quantity;
              return p;
            },
            0,
          );

          const clientName =
            next?.clientId && users?.length > 0 ? find(users, ['id', next?.clientId])?.name : {};

          const from = new Date(next?.lastUpdateStatus);
          const difDays = moment().diff(moment(from), 'days');
          const difHours = moment().diff(moment(from), 'hours');
          const difMinutes = moment().diff(moment(from), 'minutes');
          const difSeconds = moment().diff(moment(from), 'seconds');
          const changeTime =
            difDays != 0
              ? `${difDays} days`
              : difHours != 0
              ? `${difHours} hours`
              : difMinutes != 0
              ? `${difMinutes} mins`
              : `${difSeconds} seconds`;

          prev.push([
            index++,
            wh?.name ?? '',
            next?.displayId ?? '',
            next?.carriers[0]?.waybillNumber ?? '',
            next?.status == OrderFFMStatus.Canceled
              ? 'Order canceled during preparation'
              : 'Order returned by 3PL',
            next?.carriers[0]?.carrier?.name ?? '',
            next?.status ? [$enum(OrderFFMStatus).getKeyOrDefault(Number(next?.status), null)] : '',
            changeTime ?? '',
            clientName,
            productName,
            sku ?? '',
            totalNeed ?? '',
            next?.totalPrice,
            moment(next?.lastUpdateStatus)
              .tz('Asia/Ho_Chi_Minh')
              .format('DD/MM/YYYY'),
          ]);
        }
        return prev;
      },
      [
        [
          'No.',
          'Warehouse',
          'SO Code',
          'Waybill',
          'Case',
          'Carrier',
          'Status',
          'Cancellation/Return Time',
          'Client',
          'Product Name',
          'SKU',
          'Qtt. Needed',
          'Total Price',
          'Last Update status',
        ],
      ],
    );

    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push({
      name: 'AwaitingReimport',
      data: params,
      options: {},
    });
    const buffer = xlsx.build(xlsxSheets);
    return buffer;
  }

  async exportProcessed(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const whIds = headers['warehouse-ids']?.split(',');
    const result = await this.getReImports(filters, headers, request);
    // return result;
    let productIds = [];
    const orderIds = [];
    for (const item of result) {
      if (!isNil(item)) {
        productIds = productIds.concat(item?.products?.map(x => x?.productId));
        orderIds.push(item?.orderId);
      }
    }
    const productOrders = await this.opRepository
      .createQueryBuilder('op')
      .where('op.order_id In (:...orderIds)', { orderIds })
      .getMany();
    const productExtend = {};
    for (const item of productOrders) {
      if (item?.productDetail?.product?.isCombo == true) {
        const combo = item?.productDetail?.product?.combo?.map(x => ({
          ...x,
          productComboId: item?.id,
        }));
        productExtend[`${item?.productId}_${item?.order_id}`] = combo;
      } else {
        productExtend[`${item?.productId}_${item?.order_id}`] = item;
      }
    }

    let lastData = [];
    for (const element of result) {
      if (!isNil(element)) {
        const { products, ...data } = element;
        lastData = lastData?.concat(
          products?.map(x => ({
            ...data,
            good: x?.good,
            damaged: x?.damaged,
            damagedBy3pl: x?.damagedBy3pl,
            lost: x?.lost,
            lostBy3pl: x?.lostBy3pl,
            needed: x?.needed,
            product: x?.productId,
            sku: x?.sku,
            productName: x?.productName,
          })),
        );
      }
    }
    let params = [];

    const [whs, users] = await Promise.all([
      !!result
        ? this.whRepository.find({
            where: {
              id: In(whIds),
              bizId: companyId,
            },
          })
        : [],
      !!result
        ? this.uRepository
            .createQueryBuilder()
            // .where('id IN (:...uIds)', { uIds })
            .getMany()
        : [],
    ]);

    let index = 1;
    params = reduce(
      lastData,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          const wh: any =
            next?.warehouseId && whs?.length > 0
              ? find(whs, ['id', next?.warehouseId?.toString()])
              : {};
          const actor =
            next?.creatorId && users?.length > 0 ? find(users, ['id', next?.creatorId])?.name : {};
          const clientName =
            next?.clientId && users?.length > 0 ? find(users, ['id', next?.clientId])?.name : {};

          prev.push([
            index++,
            wh?.name ?? '',
            next?.orderDisplayId ?? '',
            next?.waybillNumber ?? '',
            [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.Collecting].includes(
              next?.statusBeforeChanged,
            )
              ? 'Order canceled during preparation'
              : 'Order returned by 3PL',
            // condition ?? '',
            next?.order?.carriers[0]?.carrier?.name ?? '',
            next?.order?.status
              ? [$enum(OrderFFMStatus).getKeyOrDefault(Number(next?.order?.status), null)]
              : '',
            clientName,
            next?.productName,
            next?.sku ?? '',
            next?.order?.totalPrice ?? '',
            next?.good,
            next?.damagedBy3pl,
            next?.damaged,
            next?.lostBy3pl,
            next?.lost,
            actor ?? '',
            moment(next?.createdAt)
              .tz('Asia/Ho_Chi_Minh')
              .format('DD/MM/YYYY') ?? '',
          ]);
        }
        return prev;
      },
      [
        [
          'No.',
          'Warehouse',
          'SO Code',
          'Waybill',
          'Case',
          // 'Condition',
          'Carrier',
          'Status',
          'Client',
          'Product Name',
          'SKU',
          'Total Price',
          'Good',
          'Damaged By 3PL',
          'Damaged By WH',
          'Lost BY 3PL',
          'Lost BY WH',
          'Actor',
          'Time',
        ],
      ],
    );

    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push({
      name: 'Processed',
      data: params,
      options: {},
    });
    const buffer = xlsx.build(xlsxSheets);
    return buffer;
  }

  async getCountOrderDetails(
    filters: FilterReImport,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<number> {
    const countryId = headers['country-ids'];
    const { warehouseIds, from, to } = filters;

    const mQuery = this.odRepository.createQueryBuilder('od');
    mQuery.select(['od.returnWarehouseId', 'COUNT(DISTINCT od.ID)']);
    mQuery.andWhere(`od.status IN (:...statusList)`, {
      statusList: [OrderFFMStatus.Canceled, OrderFFMStatus.InReturn, OrderFFMStatus.Returned],
    });

    mQuery.leftJoin(ReImport, 'ri', `(od.id = ri.order_id)`);
    mQuery.andWhere('ri.order_id IS NULL');
    mQuery.andWhere(
      new Brackets(qb => {
        qb.where('od.status <> :canceled', {
          canceled: OrderFFMStatus.Canceled,
        }).orWhere('(logs.status = :canceled AND logs.before_status IN (:...before_status))', {
          canceled: OrderFFMStatus.Canceled,
          before_status: [OrderFFMStatus.Awaiting3PLPickup, OrderFFMStatus.Collecting],
        });
      }),
    );
    mQuery.andWhere(
      new Brackets(qb => {
        qb.where('od.status <> :inreturn', {
          inreturn: OrderFFMStatus.InReturn,
        }).orWhere(
          `(od.status = :inreturn AND od.last_update_status < NOW() - (leadTime.rules * INTERVAL '1 day'))`,
          {
            inreturn: OrderFFMStatus.InReturn,
          },
        );
      }),
    );
    mQuery.andWhere(
      new Brackets(qb => {
        qb.where('od.status = :canceled and od.warehouse_id IN (:...warehouseIds)', {
          canceled: OrderFFMStatus.Canceled,
          warehouseIds,
        }).orWhere(
          'od.status IN (:...returned) and od.return_warehouse_id IN (:...warehouseIds)',
          {
            returned: [OrderFFMStatus.InReturn, OrderFFMStatus.Returned],
            warehouseIds,
          },
        );
      }),
    );
    mQuery.leftJoin('od.carriers', 'carriers');
    mQuery.leftJoin('carriers.carrier', 'carrier');
    mQuery.leftJoin('od.products', 'products');
    mQuery.leftJoin(OrderStatusHistories, 'logs', `(od.id = logs.order_id)`);
    mQuery.leftJoin(
      LeadTime,
      'leadTime',
      `(carriers.carrier_id = leadTime.carrier_id AND leadTime.country_id = '${countryId}' AND leadTime.company_id = ${request?.user?.companyId} AND leadTime.type = ${LeadTimeTypeEnum.InReturnOrder})`,
    );
    mQuery.andWhere('od.company_id = :companyId', { companyId: request?.user?.companyId });
    // mQuery.andWhere('od.country_id =:countryId', { countryId });

    mQuery.andWhere('od.last_update_status >= :from', {
      from:
        from ??
        moment()
          .subtract(1, 'months')
          .startOf('month')
          .toDate(),
    });

    mQuery.andWhere('od.created_at <= :to', {
      to:
        to ??
        moment()
          .endOf('day')
          .toDate(),
    });
    mQuery.groupBy('od.returnWarehouseId');
    const result = await mQuery.getRawMany();
    return result.reduce((prev: number, item) => {
      prev[item?.od_return_warehouse_id] = Number(item?.count);
      return prev;
    }, {});
  }
}
