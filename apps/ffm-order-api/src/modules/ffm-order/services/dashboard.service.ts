import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { orderConnection } from 'core/constants/database-connection.constant';
import { Order } from 'apps/ffm-order-api/src/entities/order.entity';
import { Brackets, Repository } from 'typeorm';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { RaiseTicket } from 'apps/ffm-order-api/src/entities/raise-ticket.entity';
import {
  FilterDashboardTicket,
  FilterDashboardTicketReturn,
  TypeFilter,
  FilterDashboardGeneralInformation,
} from 'apps/ffm-order-api/src/filters/dashboard.filter';
import * as moment from 'moment-timezone';
import { CreateOrdersFilterDto } from 'apps/ffm-order-api/src/dtos/filter.dto';
import { FilterCollection } from 'apps/ffm-order-api/src/entities/filter-collection.entity';
import { plainToInstance } from 'class-transformer';
import { isEmpty, isNil, reduce } from 'lodash';
import { OrderStatusHistories } from 'apps/ffm-order-api/src/entities/order-status-history.entity';
import { TOTAL_REASON_RETURN_TICKET } from 'apps/ffm-order-api/src/constants/raise-ticket.constants';
import { UserStatus } from 'core/enums/user-status.enum';
import { Permission } from 'core/enums/permission-ffm.enum';
import { TypeOfDashBoardReturnTicket } from 'apps/ffm-order-api/src/enums/dashboard.enum';
import { RaiseTicketNoteEnum } from 'apps/ffm-order-api/src/enums/raise-ticket.enum';
import { $enum } from 'ts-enum-util';
import { User } from 'core/entities/identity/user.entity';
import { Carrier } from 'apps/ffm-order-api/src/entities/carrier.entity';
import { RaiseTicketNote } from 'apps/ffm-order-api/src/entities/raise-ticket-note.entity';
import xlsx, { WorkSheet } from 'node-xlsx';
import { OrderCarrier } from 'apps/ffm-order-api/src/entities/order-carrier.entity';
import { RETURNED_STATUS } from 'apps/ffm-order-api/src/constants/order.constants';
import { OrderFFMStatus } from 'core/enums/order-ffm-status.enum';
import { Product } from 'apps/ffm-order-api/src/read-entities/ffm-catalog/product.entity';
import { CARRIER_BY_COUNTRIES } from 'apps/ffm-order-api/src/constants/carriers.constants';
import { CarrierCode } from 'core/enums/carrier-code.enum';
import { OrderTag } from 'apps/ffm-order-api/src/entities/order-tag.entity';
import { TagMethodType } from 'apps/ffm-order-api/src/enums/order-search-recipient.enum';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(RaiseTicket, orderConnection)
    private ticketRepository: Repository<RaiseTicket>,
    @InjectRepository(Carrier, orderConnection)
    private carrierRepository: Repository<Carrier>,
    @InjectRepository(RaiseTicketNote, orderConnection)
    private noteRepository: Repository<RaiseTicketNote>,
    @InjectRepository(OrderCarrier, orderConnection)
    private orderCarrierRepository: Repository<OrderCarrier>,
    @InjectRepository(Order, orderConnection)
    private orderRepository: Repository<Order>,
    @InjectRepository(FilterCollection, orderConnection)
    private filterRepo: Repository<FilterCollection>,
    @InjectRepository(OrderTag, orderConnection)
    private otRepository: Repository<OrderTag>,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  async getDashboardTicket(
    // pagination: PaginationOptions,
    filters: FilterDashboardTicket,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const { from, to, carrierIds, clientIds, productIds, staffIds } = filters;
    const mQuery = this.ticketRepository.createQueryBuilder('rt');
    mQuery.select(`DATE("rt"."created_at" AT TIME ZONE '-7')`, 'rt_created_at');
    mQuery.leftJoin('rt.order', 'order');
    mQuery.leftJoin('rt.notes', 'notes');
    mQuery.leftJoin('order.carriers', 'carriers');
    mQuery.leftJoin('order.products', 'products');
    mQuery.leftJoin(
      OrderStatusHistories,
      'logs',
      `(order.id = logs.order_id AND logs.status = 17)`,
    );

    mQuery.addSelect('COUNT(DISTINCT "rt"."id")', 'received_ticket');
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "rt"."status" = 5 THEN "rt"."id" ELSE NULL END)',
      'number_of_resolved_ticket',
    );
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "order"."status" IN (19, 20, 21, 22) AND "logs"."updated_at" > "notes"."created_at" THEN "order"."id" ELSE NULL END )',
      'number_of_returned_processed_order',
    );
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "order"."status" IN (19, 20, 21, 22) AND ( "logs"."updated_at" < "notes"."created_at" OR "notes"."id" IS NULL ) THEN "order"."id" ELSE NULL END )',
      'number_of_returned_unprocessed_order',
    );
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "order"."id" IS NOT NULL THEN "order"."id" ELSE NULL END )',
      'proceed_order',
    );
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "order"."status" IN (17, 24) AND "rt"."assignee" IS NOT NULL AND "logs"."updated_at" > "notes"."created_at" THEN "order"."id" ELSE NULL END )',
      'number_of_delivered_processed_order',
    );
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "order"."status" IN (17, 24) AND ("logs"."updated_at" < "notes"."created_at" OR "notes"."id" IS NULL) THEN "order"."id" ELSE NULL END )',
      'number_of_delivered_unprocessed_order',
    );

    mQuery.andWhere({
      companyId,
    });

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds))
        mQuery.andWhere('order.country_id IN (:...countryIds)', { countryIds });
    }

    if (from)
      mQuery.andWhere('rt.created_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      mQuery.andWhere('rt.created_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });

    if (carrierIds) mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    if (clientIds) {
      mQuery.andWhere('order.client_id IN (:...clientIds)', { clientIds });
    }
    if (productIds) {
      mQuery.andWhere('products.product_id IN (:...productIds)', { productIds });
    }
    if (staffIds) {
      mQuery.andWhere('rt.assignee IN (:...staffIds)', { staffIds });
    }
    mQuery.groupBy('rt_created_at');
    return await mQuery.getRawMany();
  }

  async getDashboardTicketByStaff(
    // pagination: PaginationOptions,
    filters: FilterDashboardTicket,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const { from, to, carrierIds, clientIds, productIds, staffIds } = filters;
    const countryIds = headers['country-ids']?.split(',');

    const payload: Record<string, any> = {
      filter: {
        roleIds: [Permission.ptManager, Permission.ptView],
        countryIds,
        status: [UserStatus.active],
        companyIds: [companyId],
      },
    };
    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-filter',
      payload: payload,
      timeout: 10000,
    });
    const userIds = data.map(x => x.id);
    const users = data as User[];
    const userLookup: Record<string, User> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );

    const mQuery = this.ticketRepository.createQueryBuilder('rt');
    mQuery.select(`rt.assignee`, 'assignee');
    mQuery.leftJoin('rt.order', 'order');
    mQuery.leftJoin('rt.notes', 'notes');
    mQuery.leftJoin('order.carriers', 'carriers');
    mQuery.leftJoin('order.products', 'products');
    mQuery.leftJoin(
      OrderStatusHistories,
      'logs',
      `(order.id = logs.order_id AND logs.status = 17)`,
    );

    mQuery.addSelect('COUNT(DISTINCT "rt"."id")', 'received_ticket');
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "rt"."status" = 5 THEN "rt"."id" ELSE NULL END)',
      'number_of_resolved_ticket',
    );
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "order"."status" IN (19, 20, 21, 22) AND "logs"."updated_at" > "notes"."created_at" THEN "order"."id" ELSE NULL END )',
      'number_of_returned_processed_order',
    );
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "order"."status" IN (19, 20, 21, 22) AND ( "logs"."updated_at" < "notes"."created_at" OR "notes"."id" IS NULL ) THEN "order"."id" ELSE NULL END )',
      'number_of_returned_unprocessed_order',
    );
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "order"."id" IS NOT NULL THEN "order"."id" ELSE NULL END )',
      'proceed_order',
    );
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "order"."status" IN (17, 24) AND "rt"."assignee" IS NOT NULL AND "logs"."updated_at" > "notes"."created_at" THEN "order"."id" ELSE NULL END )',
      'number_of_delivered_processed_order',
    );
    mQuery.addSelect(
      'COUNT(DISTINCT CASE WHEN "order"."status" IN (17, 24) AND ("logs"."updated_at" < "notes"."created_at" OR "notes"."id" IS NULL) THEN "order"."id" ELSE NULL END )',
      'number_of_delivered_unprocessed_order',
    );

    mQuery.andWhere({
      companyId,
    });

    if (!isNil(headers)) {
      if (!isEmpty(countryIds))
        mQuery.andWhere('order.country_id IN (:...countryIds)', { countryIds });
    }

    if (from)
      mQuery.andWhere('rt.created_at >= :from', {
        from:
          from ??
          moment()
            .subtract(30, 'days')
            .startOf('day')
            .toDate(),
      });
    if (to)
      mQuery.andWhere('rt.created_at <= :to', {
        to:
          to ??
          moment()
            .endOf('day')
            .toDate(),
      });

    if (carrierIds) mQuery.andWhere('carriers.carrier_id IN (:...carrierIds)', { carrierIds });
    if (clientIds) {
      mQuery.andWhere('order.client_id IN (:...clientIds)', { clientIds });
    }
    if (productIds) {
      mQuery.andWhere('products.product_id IN (:...productIds)', { productIds });
    }
    if (staffIds) {
      const staffIdsFilter = staffIds.filter(item => userIds.includes(Number(item)));
      mQuery.andWhere('rt.assignee IN (:...staffIds)', { staffIds: staffIdsFilter });
    } else {
      mQuery.andWhere(`rt.assignee IN (:...staffIds)`, { staffIds: userIds });
    }

    mQuery.groupBy('rt.assignee');
    const result = await mQuery.getRawMany();
    for (const item of result) {
      item[`name`] = userLookup[item?.assignee]?.name;
    }
    return result;
  }

  async createLeadsManualFilter(
    data: CreateOrdersFilterDto,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<FilterCollection> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    const createdBy = request?.user?.id;
    if (!countryId) throw new BadRequestException(`country-ids is required`);
    const filter = plainToInstance(FilterCollection, { ...data, companyId, countryId, createdBy });
    const result = await this.filterRepo.save(filter).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });
    return result;
  }

  async getLeadsManualFilter(
    filters: TypeFilter,
    headers?: Record<string, string>,
    request?: Record<string, any>,
  ): Promise<FilterCollection[]> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    const createdBy = request?.user?.id;
    if (!countryId) throw new BadRequestException(`country-ids is required`);
    const qb = this.filterRepo
      .createQueryBuilder('f')
      .where('f.countryId = :countryId', { countryId })
      .andWhere('f.companyId = :companyId', { companyId })
      .andWhere('f.createdBy = :createdBy')
      .andWhere('f.type = :type', { type: filters?.type })
      .setParameters({ companyId, countryId, createdBy });

    return qb.getMany();
  }

  async deleteLeadsManualFilter(filterId: number, request?: Record<string, any>) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException("User doesn't belong to any companies");

    const filter = await this.filterRepo.findOne({ id: filterId });
    if (!filter) throw new NotFoundException('Không tìm thấy bộ lọc yêu cầu');

    if (companyId !== filter.companyId) throw new UnauthorizedException();

    return this.filterRepo.softDelete(filter.id);
  }

  async getDashboardTicketReturn(
    filters: FilterDashboardTicketReturn,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const countryIds = headers['country-ids']?.split(',');
    const { from, to, isFilterByOrderCreatedAt } = filters;

    const mQuerySub = this.orderCarrierRepository
      .createQueryBuilder('oc')
      .select('order.id', 'oid')
      .addSelect('max("oc"."created_at" )', 'carrier_created_at')
      .where(`order.company_id = ${companyId}`)
      .leftJoin('oc.order', 'order')
      .groupBy('oid');

    const lastNoteQuery = this.noteRepository
      .createQueryBuilder('note')
      .select(`order.id`)
      .addSelect(`MAX ( "note"."id" )`, `note_id`)
      .leftJoin(`note.ticket`, `rt`)
      .leftJoin('rt.order', 'order')
      .where(`rt.company_id = ${companyId}`)
      .andWhere('order.country_id IN (:...countryIds)', { countryIds })
      .andWhere(`rt.status = 5`)
      .andWhere(`rt.type = 2`)
      .groupBy(`order.id`);
    if (isFilterByOrderCreatedAt) {
      if (from)
        lastNoteQuery.andWhere('order.created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        lastNoteQuery.andWhere('order.created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }

    const mQuery = this.orderRepository
      .createQueryBuilder('order')
      .select(`note.type`, `type`)
      .addSelect(`COUNT(DISTINCT "order"."id")`, `total`)
      .leftJoin('order.tickets', 'rt')
      .leftJoin('rt.notes', 'note')
      .innerJoin(
        `(${lastNoteQuery.getQuery()})`,
        'last_note',
        'last_note.note_id = note.id',
        lastNoteQuery.getParameters(),
      )
      .where(`order.company_id = ${companyId}`)
      .andWhere(`order.status IN (:...RETURNED_STATUS)`, { RETURNED_STATUS })
      .andWhere(`note."type" IN (:...TOTAL_REASON_RETURN_TICKET)`, { TOTAL_REASON_RETURN_TICKET })
      .andWhere(`note."id" IS NOT NULL`)
      .groupBy(`note.type`);

    if (!isNil(headers)) {
      if (!isEmpty(countryIds)) {
        mQuery.andWhere('order.country_id IN (:...countryIds)', { countryIds });
        mQuerySub.andWhere('order.country_id IN (:...countryIds)', { countryIds });
      }
    }
    if (isFilterByOrderCreatedAt) {
      if (from)
        mQuery.andWhere('order.created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        mQuery.andWhere('order.created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }

    if (!isFilterByOrderCreatedAt) {
      mQuery.innerJoin(
        `(${mQuerySub.getQuery()})`,
        'latest_carrier',
        'latest_carrier.oid = order.id',
        mQuerySub.getParameters(),
      );
      if (from)
        mQuery.andWhere('latest_carrier.carrier_created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        mQuery.andWhere('latest_carrier.carrier_created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }
    const response = await mQuery.getRawMany();
    const result = {};
    for (const item of response) {
      result[`${$enum(RaiseTicketNoteEnum).getKeyOrDefault(item?.type, null)}`] = item?.total ?? 0;
    }
    return result;
  }

  async query2(
    filters: FilterDashboardTicketReturn,
    headers?: Record<string, any>,
    request?: Record<string, any>,
    repo?: any,
    fields?: any,
  ) {
    const { companyId } = request?.user;
    const { from, to, isFilterByOrderCreatedAt } = filters;
    const countryIds = headers['country-ids']?.split(',');

    const mQuerySub = this.orderCarrierRepository
      .createQueryBuilder('oc')
      .select('order.id', 'oid')
      .addSelect('max("oc"."created_at" )', 'carrier_created_at')
      .where(`order.company_id = ${companyId}`)
      .leftJoin('oc.order', 'order')
      .groupBy('oid');

    const lastNoteQuery = this.noteRepository
      .createQueryBuilder('note')
      .select(`order.id`)
      .addSelect(`MAX ( "note"."id" )`, `note_id`)
      .leftJoin(`note.ticket`, `rt`)
      .leftJoin('rt.order', 'order')
      .where(`rt.company_id = ${companyId}`)
      .andWhere('order.country_id IN (:...countryIds)', { countryIds })
      .groupBy(`order.id`);
    if (isFilterByOrderCreatedAt) {
      if (from)
        lastNoteQuery.andWhere('order.created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        lastNoteQuery.andWhere('order.created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }

    if (!isFilterByOrderCreatedAt) {
      lastNoteQuery.innerJoin(
        `(${mQuerySub.getQuery()})`,
        'latest_carrier',
        'latest_carrier.oid = order.id',
        mQuerySub.getParameters(),
      );
      if (from)
        lastNoteQuery.andWhere('latest_carrier.carrier_created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        lastNoteQuery.andWhere('latest_carrier.carrier_created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }
    const mQuery = this.orderRepository
      .createQueryBuilder('order')
      .select('note.id', 'nid')
      .addSelect(`COUNT(DISTINCT rt."id")`, `total`)
      .leftJoin('order.tickets', 'rt')
      .leftJoin('rt.notes', 'note')
      .innerJoin(
        `(${lastNoteQuery.getQuery()})`,
        'last_note',
        'last_note.note_id = note.id',
        lastNoteQuery.getParameters(),
      )
      .where(`rt.company_id = ${companyId}`)
      .andWhere(`note."id" IS NOT NULL`)
      .andWhere(`order.status != 0`)
      .groupBy('note.id');
    if (fields == 'product_sku') {
      mQuery.addSelect(`products.product_detail->'product'->>'sku'`, `product_sku`);
      mQuery
        .addSelect(`products.product_detail->'product'->>'name'`, `name`)
        .addGroupBy(`product_sku`)
        .addGroupBy(`name`)
        .leftJoin('order.products', 'products');
    } else if (fields == 'clientAndStaff') {
      mQuery
        .addSelect(`rt.assignee`, `assignee`)
        .addSelect(`order.client_id`, `client_id`)
        .addGroupBy(`rt.assignee`)
        .addGroupBy(`order.client_id`)
        .leftJoin('order.carriers', 'carriers');
    } else {
      mQuery
        .addSelect(`${repo}.${fields}`, `${fields}`)
        .addGroupBy(`${repo}.${fields}`)
        .leftJoin('order.carriers', 'carriers');
    }

    if (!isNil(headers)) {
      const countryIds = headers['country-ids']?.split(',');
      if (!isEmpty(countryIds)) {
        mQuery.andWhere('order.country_id IN (:...countryIds)', { countryIds });
        mQuerySub.andWhere('order.country_id IN (:...countryIds)', { countryIds });
      }
    }
    if (isFilterByOrderCreatedAt) {
      if (from)
        mQuery.andWhere('order.created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        mQuery.andWhere('order.created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }

    if (!isFilterByOrderCreatedAt) {
      mQuery.innerJoin(
        `(${mQuerySub.getQuery()})`,
        'latest_carrier',
        'latest_carrier.oid = order.id',
        mQuerySub.getParameters(),
      );
      if (from)
        mQuery.andWhere('latest_carrier.carrier_created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        mQuery.andWhere('latest_carrier.carrier_created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }
    return mQuery;
  }

  async getDashboardTicketReturnByReason(
    filters: FilterDashboardTicketReturn,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    // const { companyId } = request?.user;
    // const countryIds = headers['country-ids']?.split(',');

    // By Roles ---------------------------------------------------------------
    // const payload: Record<string, any> = {
    //   filter: {
    //     roleIds: [Permission.ptManager, Permission.ptView],
    //     countryIds,
    //     status: [UserStatus.active],
    //     companyIds: [companyId],
    //   },
    // };

    // const { data: result } = await this.amqpConnection.request<Record<string, any>>({
    //   exchange: 'identity-service-roles',
    //   routingKey: 'get-users-by-filter',
    //   payload: payload,
    //   timeout: 10000,
    // });
    // const userIds = result.map(x => x.id);

    // const queryByRoles = this.orderRepository
    //   .createQueryBuilder('order')
    //   .select(`sub_table.assignee`)
    //   .addSelect(`COUNT(DISTINCT order.id)`, `order_return`)
    //   .andWhere(`sub_table.assignee IS NOT NULL`)
    //   .andWhere(`order.status != 0`)
    //   .andWhere(
    //     `order.status IN (18, 19, 20, 21, 22) AND note."type" IN (${TOTAL_REASON_RETURN_TICKET})`,
    //   )
    //   .andWhere(`"sub_table"."assignee" IN (:...userIds)`, { userIds: userIds })
    //   .leftJoin('order.tickets', 'rt');
    // const subQueryByRoles = await this.query2(filters, headers, request, 'rt', 'assignee');
    // queryByRoles
    //   .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
    //   .leftJoin(
    //     `(${subQueryByRoles.getQuery()})`,
    //     'sub_table',
    //     'sub_table.nid = note.id',
    //     subQueryByRoles.getParameters(),
    //   )
    //   .groupBy(`sub_table.assignee`)
    //   // .orderBy('percent', 'DESC')
    //   .limit(5);

    // // const dataByRoles = await queryByRoles.getRawMany();

    let dataByRoles = await this.getDashboardTicketReturnDetail(
      { ...filters, type: TypeOfDashBoardReturnTicket.ptExecutives },
      headers,
      request,
    );
    dataByRoles = dataByRoles.sort((a, b) => b.percentReturn - a.percentReturn).slice(0, 5);
    // By Client ---------------------------------------------------------------
    // const queryByClient = this.orderRepository
    //   .createQueryBuilder('order')
    //   .select(`sub_table.client_id`)
    //   .addSelect(`COUNT(DISTINCT rt."id")`, `ticket_return`)
    //   .andWhere(`sub_table.client_id IS NOT NULL`)
    //   .andWhere(`order.status != 0`)
    //   .andWhere(`rt.status = 5`)
    //   .andWhere(`rt.type = 2`)
    //   .andWhere(
    //     `order.status IN (18, 19, 20, 21, 22) AND note."type" IN (${TOTAL_REASON_RETURN_TICKET})`,
    //   )
    //   .leftJoin('order.tickets', 'rt');

    // const subQueryByClient = await this.query2(filters, headers, request, 'order', 'client_id');
    // queryByClient
    //   .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
    //   .leftJoin(
    //     `(${subQueryByClient.getQuery()})`,
    //     'sub_table',
    //     'sub_table.nid = note.id',
    //     subQueryByClient.getParameters(),
    //   )
    //   .groupBy(`sub_table.client_id`);
    // // .orderBy('percent', 'DESC')
    // // .limit(5);

    // const dataByClient = await queryByClient.getRawMany();
    // const clientIds = dataByClient.map(x => x.client_id);

    // let returnOrderByClientQuery = this.orderRepository
    //   .createQueryBuilder('order')
    //   .select(`order.client_id`);
    // returnOrderByClientQuery = await this.subQuery(
    //   returnOrderByClientQuery,
    //   filters,
    //   headers,
    //   request,
    // );
    // returnOrderByClientQuery = returnOrderByClientQuery
    //   .andWhere(`order.client_id IN (:...clientIds)`, {
    //     clientIds,
    //   })
    //   .groupBy('client_id');
    // const xxx = await returnOrderByClientQuery.getRawMany();
    // const resultDataByClient = [];
    // for (const item of xxx) {
    //   for (const e of dataByClient) {
    //     if (item.client_id === e.client_id) {
    //       resultDataByClient.push({
    //         client_id: item.client_id,
    //         ticket_return: e.ticket_return,
    //         totalOrder: item.totalOrder,
    //         order_return: item.order_return,
    //       });
    //     }
    //   }
    // }
    let resultDataByClient = await this.getDashboardTicketReturnDetail(
      { ...filters, type: TypeOfDashBoardReturnTicket.client },
      headers,
      request,
    );
    resultDataByClient = resultDataByClient
      .sort((a, b) => b.percentReturn - a.percentReturn)
      .slice(0, 5);
    // return resultDataByClient;
    // By Product ---------------------------------------------------------------
    // const queryByProduct = this.orderRepository
    //   .createQueryBuilder('order')
    //   .select(`sub_table.product_sku`)
    //   .addSelect(`sub_table.name`)
    //   .addSelect(`COUNT(DISTINCT sub_table."nid")`, `total`)
    //   .addSelect(
    //     `COUNT (DISTINCT CASE WHEN order.status IN (18, 19, 20, 21, 22) AND note."type" IN (${TOTAL_REASON_RETURN_TICKET}) THEN order.id ELSE NULL END )`,
    //     `order_return`,
    //   )
    //   .addSelect(
    //     `ROUND((COUNT(DISTINCT CASE WHEN order.status IN (18, 19, 20, 21, 22) AND note."type" IN (${TOTAL_REASON_RETURN_TICKET}) THEN order.id ELSE NULL END) * 1.0 / COUNT(DISTINCT order.id)) * 100, 2)`,
    //     `percent`,
    //   )
    //   .andWhere(`sub_table.product_sku IS NOT NULL`)
    //   .andWhere(`order.status != 0`)
    //   .leftJoin('order.tickets', 'rt');

    // const subQueryByProduct = await this.query2(filters, headers, request, '', 'product_sku');

    // queryByProduct
    //   .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
    //   .leftJoin(
    //     `(${subQueryByProduct.getQuery()})`,
    //     'sub_table',
    //     'sub_table.nid = note.id',
    //     subQueryByProduct.getParameters(),
    //   )
    //   .groupBy(`sub_table.product_sku`)
    //   .addGroupBy(`sub_table.name`)
    //   .orderBy('percent', 'DESC')
    //   .limit(5);

    // const dataByProduct = await queryByProduct.getRawMany();
    // for (const item of dataByProduct) {
    //   item[`id`] = item?.product_sku;
    //   delete item.product_sku;
    // }
    let dataByProduct = await this.getDashboardTicketReturnDetail(
      { ...filters, type: TypeOfDashBoardReturnTicket.product },
      headers,
      request,
    );
    dataByProduct = dataByProduct.sort((a, b) => b.percentReturn - a.percentReturn).slice(0, 5);
    // By Carrier --------------------------------------------------- ------------
    // const queryByCarrier = this.orderRepository
    //   .createQueryBuilder('order')
    //   .select(`sub_table.carrier_id`)
    //   .addSelect(`COUNT(DISTINCT sub_table."nid")`, `total`)
    //   .addSelect(
    //     `COUNT (DISTINCT CASE WHEN order.status IN (18, 19, 20, 21, 22) AND note."type" IN (${TOTAL_REASON_RETURN_TICKET}) THEN order.id ELSE NULL END )`,
    //     `order_return`,
    //   )
    //   .addSelect(
    //     `ROUND((COUNT(DISTINCT CASE WHEN order.status IN (18, 19, 20, 21, 22) AND note."type" IN (${TOTAL_REASON_RETURN_TICKET}) THEN order.id ELSE NULL END) * 1.0 / COUNT(DISTINCT order.id)) * 100, 2)`,
    //     `percent`,
    //   )
    //   .andWhere(`sub_table.carrier_id IS NOT NULL`)
    //   .andWhere(`order.status != 0`)
    //   .leftJoin('order.tickets', 'rt');

    // const subQueryByCarrier = await this.query2(
    //   filters,
    //   headers,
    //   request,
    //   'carriers',
    //   'carrier_id',
    // );

    // queryByCarrier
    //   .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
    //   .leftJoin(
    //     `(${subQueryByCarrier.getQuery()})`,
    //     'sub_table',
    //     'sub_table.nid = note.id',
    //     subQueryByCarrier.getParameters(),
    //   )
    //   .groupBy(`sub_table.carrier_id`)
    //   .orderBy('percent', 'DESC')
    //   .limit(5);

    // const dataByCarrier = await queryByCarrier.getRawMany();
    let dataByCarrier = await this.getDashboardTicketReturnDetail(
      { ...filters, type: TypeOfDashBoardReturnTicket.carrier },
      headers,
      request,
    );
    dataByCarrier = dataByCarrier.sort((a, b) => b.percentReturn - a.percentReturn).slice(0, 5);
    // By Region ---------------------------------------------------------------
    // const queryByRegion = this.orderRepository
    //   .createQueryBuilder('order')
    //   .select(`sub_table.recipient_province_id`)
    //   .addSelect(`order.recipient_province`)
    //   .addSelect(`COUNT(DISTINCT sub_table."nid")`, `total`)
    //   .addSelect(
    //     `COUNT (DISTINCT CASE WHEN order.status IN (18, 19, 20, 21, 22) AND note."type" IN (${TOTAL_REASON_RETURN_TICKET}) THEN order.id ELSE NULL END )`,
    //     `order_return`,
    //   )
    //   .addSelect(
    //     `ROUND((COUNT(DISTINCT CASE WHEN order.status IN (18, 19, 20, 21, 22) AND note."type" IN (${TOTAL_REASON_RETURN_TICKET}) THEN order.id ELSE NULL END) * 1.0 / COUNT(DISTINCT order.id)) * 100, 2)`,
    //     `percent`,
    //   )
    //   .andWhere(`sub_table.recipient_province_id IS NOT NULL`)
    //   .andWhere(`order.status != 0`)
    //   .leftJoin('order.tickets', 'rt');

    // const subQueryByRegion = await this.query2(
    //   filters,
    //   headers,
    //   request,
    //   'order',
    //   'recipient_province_id',
    // );

    // queryByRegion
    //   .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
    //   .leftJoin(
    //     `(${subQueryByRegion.getQuery()})`,
    //     'sub_table',
    //     'sub_table.nid = note.id',
    //     subQueryByRegion.getParameters(),
    //   )
    //   .groupBy(`sub_table.recipient_province_id`)
    //   .addGroupBy(`order.recipient_province`)
    //   .orderBy('percent', 'DESC')
    //   .limit(5);
    // const dataByRegion = await queryByRegion.getRawMany();
    let dataByRegion = await this.getDashboardTicketReturnDetail(
      { ...filters, type: TypeOfDashBoardReturnTicket.region },
      headers,
      request,
    );
    dataByRegion = dataByRegion.sort((a, b) => b.percentReturn - a.percentReturn).slice(0, 5);

    const data = { dataByRoles, resultDataByClient, dataByProduct, dataByCarrier, dataByRegion };
    return data;
  }

  async addField(query) {
    query
      .addSelect(
        `COUNT (DISTINCT CASE WHEN rt.status IN (5) AND note."type" IN (${TOTAL_REASON_RETURN_TICKET}) THEN rt.id ELSE NULL END )`,
        `ticketReturn`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 7 THEN sub_table.nid ELSE NULL END)`,
        `CanNotCall`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 8 THEN sub_table.nid ELSE NULL END)`,
        `ArriveEarly`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 9 THEN sub_table.nid ELSE NULL END)`,
        `PoorNetwork`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 10 THEN sub_table.nid ELSE NULL END)`,
        `RiderIsFault`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 11 THEN sub_table.nid ELSE NULL END)`,
        `WrongCode`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 12 THEN sub_table.nid ELSE NULL END)`,
        `TooLongToWait`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 13 THEN sub_table.nid ELSE NULL END)`,
        `CxNotAroundOutOfTown`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 14 THEN sub_table.nid ELSE NULL END)`,
        `DenyOrder`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 15 THEN sub_table.nid ELSE NULL END)`,
        `DoubleOrder`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 16 THEN sub_table.nid ELSE NULL END)`,
        `BuyInAnotherShop`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 17 THEN sub_table.nid ELSE NULL END)`,
        `InsistOnOpening`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 18 THEN sub_table.nid ELSE NULL END)`,
        `ODZ`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 19 THEN sub_table.nid ELSE NULL END)`,
        `OutOfBudget`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 20 THEN sub_table.nid ELSE NULL END)`,
        `NotAsExpected`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 21 THEN sub_table.nid ELSE NULL END)`,
        `IncorrectNumber`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 22 THEN sub_table.nid ELSE NULL END)`,
        `NoOrder`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 23 THEN sub_table.nid ELSE NULL END)`,
        `WrongAddress`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 24 THEN sub_table.nid ELSE NULL END)`,
        `WrongCOD`,
      )
      .addSelect(
        `COUNT(DISTINCT CASE WHEN note."type" = 25 THEN sub_table.nid ELSE NULL END)`,
        `Emergency`,
      )
      .andWhere(`rt.type = 2`);
    return query;
  }
  async getDashboardTicketReturnDetail(
    filters: FilterDashboardTicketReturn,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { companyId } = request?.user;
    const { clientIds: clientFilter, ptIds, productIds, regionIds, carrierIds, type } = filters;
    const countryIds = headers['country-ids']?.split(',');
    let result: any;

    if (!type) {
      throw new BadRequestException('You must input Type pls!');
    }

    const payload: Record<string, any> = {
      filter: {
        roleIds: [Permission.ptManager, Permission.ptView],
        countryIds,
        status: [UserStatus.active],
        companyIds: [companyId],
      },
    };

    const { data } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service-roles',
      routingKey: 'get-users-by-filter',
      payload: payload,
      timeout: 10000,
    });
    const userIds = data.map(x => x.id);
    const users = data as User[];
    const userLookup: Record<string, User> = reduce(
      users,
      (prev, item) => {
        prev[item.id] = item;
        return prev;
      },
      {},
    );
    switch (type) {
      case TypeOfDashBoardReturnTicket.ptExecutives:
        let queryByRoles = this.orderRepository
          .createQueryBuilder('order')
          .select(`sub_table.assignee`)
          .addSelect(`COUNT(DISTINCT sub_table."nid")`, `orderReturn`)
          .andWhere(`sub_table.assignee IS NOT NULL`)
          .andWhere(`order.status IN (:...RETURNED_STATUS)`, { RETURNED_STATUS })
          .andWhere(`note."type" IN (:...TOTAL_REASON_RETURN_TICKET)`, {
            TOTAL_REASON_RETURN_TICKET,
          })
          .leftJoin('order.tickets', 'rt');
        if (ptIds) {
          const ptIdsFilter = ptIds.filter(item => userIds.includes(Number(item)));
          queryByRoles.andWhere(`"sub_table"."assignee" IN (:...userIds)`, {
            userIds: ptIdsFilter,
          });
        } else {
          queryByRoles.andWhere(`"sub_table"."assignee" IN (:...userIds)`, { userIds: userIds });
        }

        const subQueryByRoles = await this.query2(filters, headers, request, 'rt', 'assignee');

        queryByRoles
          .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
          .leftJoin(
            `(${subQueryByRoles.getQuery()})`,
            'sub_table',
            'sub_table.nid = note.id',
            subQueryByRoles.getParameters(),
          )
          .groupBy(`sub_table.assignee`)
          .orderBy('COUNT(DISTINCT sub_table."nid")', 'DESC');

        queryByRoles = await this.addField(queryByRoles);
        result = await queryByRoles.getRawMany();
        let returnOrderByStaffQuery = this.orderRepository
          .createQueryBuilder('order')
          .select(`rt.assignee`, `assignee`)
          .andWhere(`rt.type = 2`)
          .leftJoin('order.tickets', 'rt');
        returnOrderByStaffQuery = await this.subQuery(
          returnOrderByStaffQuery,
          filters,
          headers,
          request,
        );
        returnOrderByStaffQuery
          .andWhere(`"rt"."assignee" IN (:...userIds)`, { userIds: userIds })
          .groupBy('assignee');

        const orderReturnByStaff = await returnOrderByStaffQuery.getRawMany();
        for (const item of orderReturnByStaff) {
          result.forEach((e, i) => {
            if (item.assignee === e.assignee) {
              result[i] = { ...e, totalOrder: item.totalOrder };
            }
          });
        }
        for (const item of result) {
          item[`name`] = userLookup[item?.assignee]?.name;
          item[`id`] = item?.assignee;
          item[`percentReturn`] = Math.floor((item?.ticketReturn / item?.totalOrder) * 10000) / 100;
          delete item.assignee;
        }

        break;
      case TypeOfDashBoardReturnTicket.client:
        let queryByClient = this.orderRepository
          .createQueryBuilder('order')
          .select(`sub_table.client_id`)
          .addSelect(`COUNT(DISTINCT sub_table."nid")`, `orderReturn`)
          .andWhere(`sub_table.client_id IS NOT NULL`)
          .andWhere(`order.status IN (:...RETURNED_STATUS)`, { RETURNED_STATUS })
          .andWhere(`note."type" IN (:...TOTAL_REASON_RETURN_TICKET)`, {
            TOTAL_REASON_RETURN_TICKET,
          })
          .leftJoin('order.tickets', 'rt');

        if (clientFilter) {
          queryByClient.andWhere(`"sub_table"."client_id" IN (:...clientIds)`, {
            clientIds: clientFilter,
          });
        }

        const subQueryByClient = await this.query2(filters, headers, request, 'order', 'client_id');

        queryByClient
          .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
          .leftJoin(
            `(${subQueryByClient.getQuery()})`,
            'sub_table',
            'sub_table.nid = note.id',
            subQueryByClient.getParameters(),
          )
          .groupBy(`sub_table.client_id`)
          .orderBy('COUNT(DISTINCT sub_table."nid")', 'DESC');

        queryByClient = await this.addField(queryByClient);
        result = await queryByClient.getRawMany();

        const clientIds = result.map(x => x.client_id) ?? [];

        let returnOrderByClientQuery = this.orderRepository
          .createQueryBuilder('order')
          .select(`order.client_id`)
          .andWhere(`rt.type = 2`)
          .leftJoin('order.tickets', 'rt');
        returnOrderByClientQuery = await this.subQuery(
          returnOrderByClientQuery,
          filters,
          headers,
          request,
        );
        if (clientIds.length > 0) {
          returnOrderByClientQuery.andWhere(`order.client_id IN (:...clientIds)`, {
            clientIds,
          });
        }
        returnOrderByClientQuery.groupBy('client_id');

        const orderReturns = await returnOrderByClientQuery.getRawMany();

        for (const item of orderReturns) {
          result.forEach((e, i) => {
            if (item.client_id === e.client_id) {
              result[i] = { ...e, totalOrder: item.totalOrder };
            }
          });
        }
        if (clientIds.length > 0) {
          const { data: client } = await this.amqpConnection.request<Record<string, any>>({
            exchange: 'identity-service-roles',
            routingKey: 'get-users-by-filter',
            payload: {
              filter: {
                ids: clientIds,
                countryIds,
                status: [UserStatus.active],
                companyIds: [companyId],
              },
            },
            timeout: 10000,
          });
          const clients = client as User[];
          const clientLookup: Record<string, User> = reduce(
            clients,
            (prev, item) => {
              prev[item.id] = item;
              return prev;
            },
            {},
          );

          for (const item of result) {
            item[`name`] = clientLookup[item?.client_id]?.name;
            item[`id`] = item?.client_id;
            item[`percentReturn`] =
              Math.floor((item?.ticketReturn / item?.totalOrder) * 10000) / 100;
            delete item.client_id;
          }
        } else result = [];

        break;
      case TypeOfDashBoardReturnTicket.product:
        let queryByProduct = this.orderRepository
          .createQueryBuilder('order')
          .select(`sub_table.product_sku`)
          .addSelect(`sub_table.name`)
          .addSelect(`COUNT(DISTINCT sub_table."nid")`, `orderReturn`)
          .andWhere(`sub_table.product_sku IS NOT NULL`)
          .andWhere(`order.status IN (:...RETURNED_STATUS)`, { RETURNED_STATUS })
          .andWhere(`note."type" IN (:...TOTAL_REASON_RETURN_TICKET)`, {
            TOTAL_REASON_RETURN_TICKET,
          })
          .leftJoin('order.tickets', 'rt');

        if (productIds) {
          queryByProduct.andWhere(`"sub_table"."product_sku" IN (:...productIds)`, { productIds });
        }
        const subQueryByProduct = await this.query2(filters, headers, request, '', 'product_sku');

        queryByProduct
          .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
          .leftJoin(
            `(${subQueryByProduct.getQuery()})`,
            'sub_table',
            'sub_table.nid = note.id',
            subQueryByProduct.getParameters(),
          )
          .groupBy(`sub_table.product_sku`)
          .addGroupBy(`sub_table.name`)
          .orderBy('COUNT(DISTINCT sub_table."nid")', 'DESC');

        queryByProduct = await this.addField(queryByProduct);
        result = await queryByProduct.getRawMany();
        const productSkus = result.map(x => x.product_sku);
        let returnOrderByProductQuery = this.orderRepository
          .createQueryBuilder('order')
          .select(`products.product_detail->'product'->>'sku'`, `product_sku`)
          .leftJoin('order.products', 'products')
          .andWhere(`rt.type = 2`)
          .leftJoin('order.tickets', 'rt');
        returnOrderByProductQuery = await this.subQuery(
          returnOrderByProductQuery,
          filters,
          headers,
          request,
        );
        if (productSkus.length > 0) {
          returnOrderByProductQuery.andWhere(
            `products.product_detail->'product'->>'sku' IN (:...productSkus)`,
            {
              productSkus,
            },
          );
        }
        returnOrderByProductQuery.groupBy('product_sku');

        const orderReturnByProduct = await returnOrderByProductQuery.getRawMany();
        for (const item of orderReturnByProduct) {
          result.forEach((e, i) => {
            if (item.product_sku === e.product_sku) {
              result[i] = { ...e, totalOrder: item.totalOrder };
            }
          });
        }
        for (const item of result) {
          item[`id`] = item?.product_sku;
          item[`percentReturn`] = Math.floor((item?.ticketReturn / item?.totalOrder) * 10000) / 100;
          delete item.product_sku;
        }
        break;
      case TypeOfDashBoardReturnTicket.carrier:
        let queryByCarrier = this.orderRepository
          .createQueryBuilder('order')
          .select(`sub_table.carrier_id`)
          .addSelect(`COUNT(DISTINCT sub_table."nid")`, `orderReturn`)
          .andWhere(`sub_table.carrier_id IS NOT NULL`)
          .andWhere(`order.status IN (:...RETURNED_STATUS)`, { RETURNED_STATUS })
          .andWhere(`note."type" IN (:...TOTAL_REASON_RETURN_TICKET)`, {
            TOTAL_REASON_RETURN_TICKET,
          })
          .andWhere(`rt.type = 2`)
          .leftJoin('order.tickets', 'rt');

        if (carrierIds) {
          queryByCarrier.andWhere(`"sub_table"."carrier_id" IN (:...carrierIds)`, { carrierIds });
        }
        const subQueryByCarrier = await this.query2(
          filters,
          headers,
          request,
          'carriers',
          'carrier_id',
        );

        queryByCarrier
          .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
          .leftJoin(
            `(${subQueryByCarrier.getQuery()})`,
            'sub_table',
            'sub_table.nid = note.id',
            subQueryByCarrier.getParameters(),
          )
          .groupBy(`sub_table.carrier_id`)
          .orderBy('COUNT(DISTINCT sub_table."nid")', 'DESC');

        queryByCarrier = await this.addField(queryByCarrier);
        result = await queryByCarrier.getRawMany();
        const listCarrier = await this.carrierRepository.find();
        const carrierLookup: Record<string, User> = reduce(
          listCarrier,
          (prev, item) => {
            prev[item.id] = item;
            return prev;
          },
          {},
        );

        let returnOrderByCarrierQuery = this.orderRepository
          .createQueryBuilder('order')
          .select(`carriers.carrier_id`)
          .leftJoin('order.carriers', 'carriers');
        returnOrderByCarrierQuery = await this.subQuery(
          returnOrderByCarrierQuery,
          filters,
          headers,
          request,
        );
        returnOrderByCarrierQuery.groupBy('carrier_id');

        const orderReturnByCarrier = await returnOrderByCarrierQuery.getRawMany();

        for (const item of orderReturnByCarrier) {
          result.forEach((e, i) => {
            if (item.carrier_id === e.carrier_id) {
              result[i] = { ...e, totalOrder: item.totalOrder };
            }
          });
        }
        for (const item of result) {
          item[`name`] = carrierLookup[item?.carrier_id]?.name;
          item[`id`] = item?.carrier_id;
          item[`percentReturn`] = Math.floor((item?.ticketReturn / item?.totalOrder) * 10000) / 100;
          delete item.carrier_id;
        }

        break;
      case TypeOfDashBoardReturnTicket.region:
        let queryByRegion = this.orderRepository
          .createQueryBuilder('order')
          .select(`sub_table.recipient_province_id`)
          .addSelect(`order.recipient_province`)
          .addSelect(`COUNT(DISTINCT sub_table."nid")`, `orderReturn`)
          .andWhere(`sub_table.recipient_province_id IS NOT NULL`)
          .andWhere(`order.status IN (:...RETURNED_STATUS)`, { RETURNED_STATUS })
          .andWhere(`note."type" IN (:...TOTAL_REASON_RETURN_TICKET)`, {
            TOTAL_REASON_RETURN_TICKET,
          })
          .leftJoin('order.tickets', 'rt');

        if (regionIds) {
          queryByRegion.andWhere(`"sub_table"."recipient_province_id" IN (:...regionIds)`, {
            regionIds,
          });
        }
        const subQueryByRegion = await this.query2(
          filters,
          headers,
          request,
          'order',
          'recipient_province_id',
        );

        queryByRegion
          .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
          .leftJoin(
            `(${subQueryByRegion.getQuery()})`,
            'sub_table',
            'sub_table.nid = note.id',
            subQueryByRegion.getParameters(),
          )
          .groupBy(`sub_table.recipient_province_id`)
          .addGroupBy(`order.recipient_province`)
          .orderBy('COUNT(DISTINCT sub_table."nid")', 'DESC');

        queryByRegion = await this.addField(queryByRegion);
        result = await queryByRegion.getRawMany();

        let returnOrderByRegionQuery = this.orderRepository
          .createQueryBuilder('order')
          .select(`order.recipient_province_id`)
          .andWhere(`rt.type = 2`)
          .leftJoin('order.tickets', 'rt');
        returnOrderByRegionQuery = await this.subQuery(
          returnOrderByRegionQuery,
          filters,
          headers,
          request,
        );
        returnOrderByRegionQuery.groupBy('recipient_province_id');

        const orderReturnByRegion = await returnOrderByRegionQuery.getRawMany();

        for (const item of orderReturnByRegion) {
          result.forEach((e, i) => {
            if (item.recipient_province_id === e.recipient_province_id) {
              result[i] = { ...e, totalOrder: item.totalOrder };
            }
          });
        }
        for (const item of result) {
          item[`name`] = item?.recipient_province;
          item[`id`] = item?.recipient_province_id;
          item[`percentReturn`] = Math.floor((item?.ticketReturn / item?.totalOrder) * 10000) / 100;
          delete item.recipient_province_id;
          delete item.recipient_province;
        }
        break;

      case TypeOfDashBoardReturnTicket.clientAndStaff:
        let queryClientAndStaff = this.orderRepository
          .createQueryBuilder('order')
          .select(`sub_table.client_id`)
          .addSelect(`sub_table.assignee`)
          .addSelect(`COUNT(DISTINCT sub_table."nid")`, `orderReturn`)
          .andWhere(`sub_table.assignee IS NOT NULL`)
          .andWhere(`sub_table.client_id IS NOT NULL`)
          .andWhere(`order.status IN (:...RETURNED_STATUS)`, { RETURNED_STATUS })
          .andWhere(`note."type" IN (:...TOTAL_REASON_RETURN_TICKET)`, {
            TOTAL_REASON_RETURN_TICKET,
          })
          .leftJoin('order.tickets', 'rt');
        if (ptIds) {
          const ptIdsFilter = ptIds.filter(item => userIds.includes(Number(item)));
          queryClientAndStaff.andWhere(`"sub_table"."assignee" IN (:...userIds)`, {
            userIds: ptIdsFilter,
          });
        } else {
          queryClientAndStaff.andWhere(`"sub_table"."assignee" IN (:...userIds)`, {
            userIds: userIds,
          });
        }

        if (clientFilter) {
          queryClientAndStaff.andWhere(`"sub_table"."client_id" IN (:...clientIds)`, {
            clientIds: clientFilter,
          });
        }
        const subQueryClientAndStaff = await this.query2(
          filters,
          headers,
          request,
          '',
          'clientAndStaff',
        );

        queryClientAndStaff
          .leftJoin(RaiseTicketNote, 'note', `note.ticket_id = rt.id`)
          .leftJoin(
            `(${subQueryClientAndStaff.getQuery()})`,
            'sub_table',
            'sub_table.nid = note.id',
            subQueryClientAndStaff.getParameters(),
          )
          .groupBy(`sub_table.client_id`)
          .addGroupBy(`sub_table.assignee`)
          .orderBy('sub_table.client_id', 'DESC');

        queryClientAndStaff = await this.addField(queryClientAndStaff);
        result = await queryClientAndStaff.getRawMany();
        const clientIdss = result.map(x => x.client_id) ?? [];

        let returnOrderByClientAndStaffQuery = this.orderRepository
          .createQueryBuilder('order')
          .select(`order.client_id`)
          .addSelect(`rt.assignee`, `assignee`)
          .andWhere(`rt.type = 2`)
          .leftJoin('order.tickets', 'rt');
        returnOrderByClientAndStaffQuery = await this.subQuery(
          returnOrderByClientAndStaffQuery,
          filters,
          headers,
          request,
        );
        if (clientIdss.length > 0) {
          returnOrderByClientAndStaffQuery.andWhere(`order.client_id IN (:...clientIds)`, {
            clientIds: clientIdss,
          });
        }
        returnOrderByClientAndStaffQuery
          .andWhere(`"rt"."assignee" IN (:...userIds)`, {
            userIds,
          })
          .groupBy('order.client_id')
          .addGroupBy('rt.assignee');

        const orderReturnByClientAndStaff = await returnOrderByClientAndStaffQuery.getRawMany();
        for (const item of orderReturnByClientAndStaff) {
          result.forEach((e, i) => {
            if (item.client_id == e.client_id && item.assignee == e.assignee) {
              result[i] = { ...e, totalOrder: item.totalOrder };
            }
          });
        }
        for (const item of result) {
          item[`clientName`] = userLookup[item?.client_id]?.name;
          item[`assigneeName`] = userLookup[item?.assignee]?.name;
          item[`percentReturn`] = Math.floor((item?.ticketReturn / item?.totalOrder) * 10000) / 100;
        }
        break;
    }
    return result;
  }

  async exportDashboardTicketReturn(
    filters: FilterDashboardTicketReturn,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const { type } = filters;

    if (!type) {
      throw new BadRequestException('You must input Type pls!');
    }
    let params = [];

    const data = await this.getDashboardTicketReturnDetail(filters, headers, request);
    if (type == TypeOfDashBoardReturnTicket.clientAndStaff) {
      params = reduce(
        data,
        (prev: (string | number)[][], next) => {
          if (!isNil(next)) {
            prev.push([
              next?.clientName ?? '',
              next?.assigneeName ?? '',
              next?.orderReturn ?? 0,
              next?.ticketReturn ?? 0,
              next?.ArriveEarly ?? 0,
              next?.BuyInAnotherShop ?? 0,
              next?.CanNotCall ?? 0,
              next?.CxNotAroundOutOfTown ?? 0,
              next?.DenyOrder ?? 0,
              next?.DoubleOrder ?? 0,
              next?.Emergency ?? 0,
              next?.IncorrectNumber ?? 0,
              next?.InsistOnOpening ?? 0,
              next?.NoOrder ?? 0,
              next?.NotAsExpected ?? 0,
              next?.ODZ ?? 0,
              next?.OutOfBudget ?? 0,
              next?.PoorNetwork ?? 0,
              next?.RiderIsFault ?? 0,
              next?.TooLongToWait ?? 0,
              next?.WrongAddress ?? 0,
              next?.WrongCOD ?? 0,
              next?.WrongCode ?? 0,
            ]);
          }
          return prev;
        },
        [
          [
            'Client',
            'Staff',
            'Return Orders',
            'Resolved Tickets',
            'Arrive early',
            'Buy in another shop',
            'Can not call',
            'Cx Not around/Out of town',
            'Deny order',
            'Double order',
            'Emergency',
            'Incorrect number',
            'Insist on opening',
            `No order (sale's fault)`,
            'Not as expected',
            'ODZ',
            'Out of budget',
            'Poor Network',
            `Rider's Fault`,
            'Too long to wait',
            'Wrong Address',
            'Wrong COD',
            'Wrong code',
          ],
        ],
      );
    } else {
      params = reduce(
        data,
        (prev: (string | number)[][], next) => {
          if (!isNil(next)) {
            prev.push([
              next?.name ?? '',
              next?.orderReturn ?? 0,
              next?.ticketReturn ?? 0,
              next?.ArriveEarly ?? 0,
              next?.BuyInAnotherShop ?? 0,
              next?.CanNotCall ?? 0,
              next?.CxNotAroundOutOfTown ?? 0,
              next?.DenyOrder ?? 0,
              next?.DoubleOrder ?? 0,
              next?.Emergency ?? 0,
              next?.IncorrectNumber ?? 0,
              next?.InsistOnOpening ?? 0,
              next?.NoOrder ?? 0,
              next?.NotAsExpected ?? 0,
              next?.ODZ ?? 0,
              next?.OutOfBudget ?? 0,
              next?.PoorNetwork ?? 0,
              next?.RiderIsFault ?? 0,
              next?.TooLongToWait ?? 0,
              next?.WrongAddress ?? 0,
              next?.WrongCOD ?? 0,
              next?.WrongCode ?? 0,
            ]);
          }
          return prev;
        },
        [
          [
            'Name',
            'Return Orders',
            'Resolved Tickets',
            'Arrive early',
            'Buy in another shop',
            'Can not call',
            'Cx Not around/Out of town',
            'Deny order',
            'Double order',
            'Emergency',
            'Incorrect number',
            'Insist on opening',
            `No order (sale's fault)`,
            'Not as expected',
            'ODZ',
            'Out of budget',
            'Poor Network',
            `Rider's Fault`,
            'Too long to wait',
            'Wrong Address',
            'Wrong COD',
            'Wrong code',
          ],
        ],
      );
    }
    const dataReturnTicket = await this.getDashboardTicketReturn(filters, headers, request);
    let totalData = 0;
    Object.keys(dataReturnTicket).forEach(key => {
      totalData += Number(dataReturnTicket[key]);
    });
    const dataReturnTicketParse = [['Lý do', 'Số liệu', 'Tỉ trọng %']];
    Object.keys(dataReturnTicket).forEach(key => {
      dataReturnTicketParse.push([
        key,
        dataReturnTicket[key],
        ((Number(dataReturnTicket[key]) / totalData) * 100)?.toFixed(2),
      ]);
    });

    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push({
      name: data?.type,
      data: params,
      options: {},
    });

    xlsxSheets.push({
      name: 'Lý do hoàn',
      data: dataReturnTicketParse,
      options: {},
    });
    const buffer = xlsx.build(xlsxSheets);
    return buffer;
  }

  async subQuery(
    query: any,
    filters: FilterDashboardTicketReturn,
    headers?: Record<string, any>,
    request?: Record<string, any>,
    // repo?: any,
    // fields?: any,
  ) {
    const { companyId } = request?.user;
    const { from, to, isFilterByOrderCreatedAt } = filters;
    const countryIds = headers['country-ids']?.split(',');

    const mQuerySub = this.orderCarrierRepository
      .createQueryBuilder('oc')
      .select('order.id', 'oid')
      .addSelect('max("oc"."created_at" )', 'carrier_created_at')
      .where(`order.company_id = ${companyId}`)
      .leftJoin('oc.order', 'order')
      .groupBy('oid');

    query
      .addSelect(`COUNT(DISTINCT "order"."id")`, `totalOrder`)
      .andWhere(`order.company_id = ${companyId}`)
      .andWhere(`order.status NOT IN (${OrderFFMStatus.Canceled}, ${OrderFFMStatus.Draft})`)
      .andWhere('order.country_id IN (:...countryIds)', { countryIds });
    if (isFilterByOrderCreatedAt) {
      if (from)
        query.andWhere('order.created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        query.andWhere('order.created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }

    if (!isFilterByOrderCreatedAt) {
      query.innerJoin(
        `(${mQuerySub.getQuery()})`,
        'latest_carrier',
        'latest_carrier.oid = order.id',
        mQuerySub.getParameters(),
      );
      if (from)
        query.andWhere('latest_carrier.carrier_created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        query.andWhere('latest_carrier.carrier_created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }
    return query;
  }

  async checkConditionQuery(headers, companyId, countryId, sql: any, filters: FilterDashboardGeneralInformation) {
    const {
      from,
      to,
      carrierIds,
      clientIds,
      productIds,
      regionIds,
      isFilterByOrderCreatedAt,
      projectIds,
      tagIds,
    } = filters;
    const statusComplated = [
      OrderFFMStatus.PickedUp3PL,
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.Delivered,
      OrderFFMStatus.AwaitingReturn,
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedCompleted,
      OrderFFMStatus.DeliveredCompleted,
      OrderFFMStatus.ReturnedStocked,
    ];
    sql
      .leftJoin(`o.carriers`, `carriers`)
      .leftJoin(`o.products`, `products`)
      .andWhere(
        new Brackets(qb => {
          qb.where(`"carriers"."status" = 'activated'`).orWhere('carriers."id" IS NULL');
        }),
      );
    if (isFilterByOrderCreatedAt) {
      sql.andWhere('o.status <> 0');
      if (from)
        sql.andWhere('o.created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        sql.andWhere('o.created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    } else {
      sql.andWhere('o.status IN (:...statusComplated)', { statusComplated });
      sql.leftJoin(OrderStatusHistories, 'osh', `(o.id = osh.order_id AND osh.status = 12)`);
      if (from)
        sql.andWhere('osh.updated_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        sql.andWhere('osh.updated_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }

    if (carrierIds) {
      sql.andWhere(`carriers.carrierId IN (:...carrierIds)`, { carrierIds });
    }

    if (clientIds) {
      sql.andWhere(`o.clientId IN (:...clientIds)`, { clientIds });
    }

    if (projectIds) {
      sql.andWhere(`o.external_project_id IN (:...projectIds)`, { projectIds });
    }

    if (productIds) {
      sql.andWhere(`products.product_detail -> 'product' ->> 'id' IN (:...productIds)`, {
        productIds,
      });
    }

    if (regionIds) {
      sql.andWhere(`o.recipient_province_id IN (:...regionIds)`, { regionIds });
    }

    if (tagIds) {
      const whIds = headers['warehouse-ids']?.split(',');

      const subQb = this.otRepository
        .createQueryBuilder('ot')
        .select('ot.id_order', 'id_order')
        .leftJoin(Order, 'order', 'order.id = ot.id_order')
        .where('ot.id_tag IN (:...tagIds)', { tagIds: filters.tagIds })
        .andWhere('order.companyId = :companyId', { companyId })
        .groupBy('ot.id_order');

      if (!isEmpty(whIds)) {
        subQb.andWhere('order.warehouseId IN (:...whIds)', { whIds });
      }
      subQb.andWhere('order.countryId = :countryId', { countryId });
      if (!!from)
        subQb.andWhere('order.createdAt >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (!!to)
        subQb.andWhere('order.createdAt <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
      if (filters.tagMethod == TagMethodType.Include) {
        sql.leftJoin('o.tags', 'tags');
        sql.andWhere('tags.id IN (:...tagIds)', { tagIds: filters.tagIds });
      }

      if (filters.tagMethod == TagMethodType.Exclude) {
        sql.andWhere(`o.id NOT IN (${subQb.getQuery()})`).setParameters(subQb.getParameters());
      }
    }
    return sql;
  }

  async getGeneralInformation(
    filters: FilterDashboardGeneralInformation,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    const {
      from,
      to,
      carrierIds,
      clientIds,
      productIds,
      regionIds,
      isFilterByOrderCreatedAt,
      projectIds,
      tagIds,
    } = filters;
    const Delivered = [OrderFFMStatus.Delivered, OrderFFMStatus.DeliveredCompleted];
    const InDelivered = [
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.AwaitingReturn,
    ];
    const Returned = [
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedStocked,
      OrderFFMStatus.ReturnedCompleted,
    ];
    const statusComplated = [
      OrderFFMStatus.PickedUp3PL,
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.Delivered,
      OrderFFMStatus.AwaitingReturn,
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedCompleted,
      OrderFFMStatus.DeliveredCompleted,
      OrderFFMStatus.ReturnedStocked,
    ];
    const InboundProcessing = [
      OrderFFMStatus.AwaitingStock,
      OrderFFMStatus.Reconfirm,
      OrderFFMStatus.Confirmed,
      OrderFFMStatus.AwaitingCollection,
      OrderFFMStatus.Collecting,
      OrderFFMStatus.Awaiting3PLPickup,
    ];
    const Canceled = [OrderFFMStatus.Draft, OrderFFMStatus.Canceled];
    const sql = this.orderRepository
      .createQueryBuilder('o')
      .select(
        `COUNT ( DISTINCT CASE WHEN o.status NOT IN (${Canceled}) THEN o."id" ELSE NULL END )`,
        `GrandTotal`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${InboundProcessing}) THEN o."id" ELSE NULL END )`,
        `InboundProcessing`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status = ${OrderFFMStatus.PickedUp3PL} THEN o."id" ELSE NULL END )`,
        `PickedUp3PL`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${InDelivered}) THEN o."id" ELSE NULL END )`,
        `InDelivery`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${Delivered}) THEN o."id" ELSE NULL END )`,
        `Delivered`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${Returned}) THEN o."id" ELSE NULL END )`,
        `Returned`,
      )
      .where('o.countryId =:countryId', { countryId })
      .andWhere('o.companyId =:companyId', { companyId });
    sql.leftJoin(`o.carriers`, `carriers`).leftJoin(`o.products`, `products`);
    if (isFilterByOrderCreatedAt) {
      sql.andWhere('o.status <> 0');
      if (from)
        sql.andWhere('o.created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        sql.andWhere('o.created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    } else {
      sql.andWhere('o.status IN (:...statusComplated)', { statusComplated });
      sql.leftJoin(OrderStatusHistories, 'osh', `(o.id = osh.order_id AND osh.status = 12)`);
      if (from)
        sql.andWhere('osh.updated_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        sql.andWhere('osh.updated_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }

    if (carrierIds) {
      sql.andWhere(`carriers.carrierId IN (:...carrierIds)`, { carrierIds });
      sql.andWhere(`"carriers"."status" = 'activated'`);
    }

    if (clientIds) {
      sql.andWhere(`o.clientId IN (:...clientIds)`, { clientIds });
    }

    if (projectIds) {
      sql.andWhere(`o.external_project_id IN (:...projectIds)`, { projectIds });
    }

    if (productIds) {
      sql.andWhere(`products.product_detail -> 'product' ->> 'id' IN (:...productIds)`, {
        productIds,
      });
    }

    if (regionIds) {
      sql.andWhere(`o.recipient_province_id IN (:...regionIds)`, { regionIds });
    }
    if (tagIds) {
      const whIds = headers['warehouse-ids']?.split(',');

      const subQb = this.otRepository
        .createQueryBuilder('ot')
        .select('ot.id_order', 'id_order')
        .leftJoin(Order, 'order', 'order.id = ot.id_order')
        .where('ot.id_tag IN (:...tagIds)', { tagIds: filters.tagIds })
        .andWhere('order.companyId = :companyId', { companyId })
        .groupBy('ot.id_order');

      if (!isEmpty(whIds)) {
        subQb.andWhere('order.warehouseId IN (:...whIds)', { whIds });
      }
      subQb.andWhere('order.countryId = :countryId', { countryId });
      if (!!from)
        subQb.andWhere('order.createdAt >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (!!to)
        subQb.andWhere('order.createdAt <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
      if (filters.tagMethod == TagMethodType.Include) {
        sql.leftJoin('o.tags', 'tags');
        sql.andWhere('tags.id IN (:...tagIds)', { tagIds: filters.tagIds });
      }

      if (filters.tagMethod == TagMethodType.Exclude) {
        sql.andWhere(`o.id NOT IN (${subQb.getQuery()})`).setParameters(subQb.getParameters());
      }
    }
    return sql.getRawMany();
  }

  async getCarrierInformation(
    filters: FilterDashboardGeneralInformation,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    const {
      from,
      to,
      carrierIds,
      clientIds,
      productIds,
      regionIds,
      isFilterByOrderCreatedAt,
      projectIds,
      tagIds,
    } = filters;
    const statusComplated = [
      OrderFFMStatus.PickedUp3PL,
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.Delivered,
      OrderFFMStatus.AwaitingReturn,
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedCompleted,
      OrderFFMStatus.DeliveredCompleted,
      OrderFFMStatus.ReturnedStocked,
    ];
    const Canceled = [OrderFFMStatus.Draft, OrderFFMStatus.Canceled];
    const carriers = await this.carrierRepository
      .createQueryBuilder()
      .where(`code IN (:...codes)`, {
        codes: [...CARRIER_BY_COUNTRIES?.[countryId], CarrierCode.system],
      })
      .orderBy('created_at', 'ASC')
      .getMany();

    const sql = this.orderRepository
      .createQueryBuilder('o')
      .select(
        `COUNT ( DISTINCT CASE WHEN  o.status NOT IN (${Canceled}) AND ( "carriers"."id" IS NULL OR ((latest_carrier.waybill_number = '' OR latest_carrier.waybill_number IS NULL) AND latest_carrier.status = 'activated' ) OR latest_carrier.status = 'canceled' ) THEN o."id" ELSE NULL END)`,
        `UnassignCarrier`,
      );

    const statusDelivered = [OrderFFMStatus.Delivered, OrderFFMStatus.DeliveredCompleted];
    const statusReturned = [
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedStocked,
      OrderFFMStatus.ReturnedCompleted,
    ];

    for (const item of carriers) {
      sql.addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status NOT IN (${Canceled}) AND carrier."id" = ${item?.id} AND (carriers.waybill_number IS NOT NULL AND carriers.waybill_number != '' AND latest_carrier.status = 'activated' ) THEN o."id" ELSE NULL END )`,
        `${item?.code}`,
      );
      sql.addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${statusDelivered}) AND carrier."id" = ${item?.id} AND (carriers.waybill_number IS NOT NULL AND carriers.waybill_number != '' AND latest_carrier.status = 'activated' ) THEN o."id" ELSE NULL END )`,
        `delivered_${item?.code}`,
      );
      sql.addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${statusReturned}) AND carrier."id" = ${item?.id} AND (carriers.waybill_number IS NOT NULL AND carriers.waybill_number != '' AND latest_carrier.status = 'activated' ) THEN o."id" ELSE NULL END )`,
        `returned_${item?.code}`,
      );
    }
    sql
      .leftJoin(`o.carriers`, `carriers`)
      .leftJoin(`o.products`, `products`)
      .leftJoin(`carriers.carrier`, `carrier`)
      .leftJoin(OrderCarrier, 'latest_carrier', `(latest_carrier.id = o.currentCarrierId)`)
      .andWhere('o.countryId =:countryId', { countryId })
      .andWhere('o.companyId =:companyId', { companyId });

    if (isFilterByOrderCreatedAt) {
      sql.andWhere('o.status <> 0');
      if (from)
        sql.andWhere('o.created_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        sql.andWhere('o.created_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    } else {
      sql.andWhere('o.status IN (:...statusComplated)', { statusComplated });
      sql.leftJoin(OrderStatusHistories, 'osh', `(o.id = osh.order_id AND osh.status = 12)`);
      if (from)
        sql.andWhere('osh.updated_at >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (to)
        sql.andWhere('osh.updated_at <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
    }

    if (carrierIds) {
      sql.andWhere(`carriers.carrierId IN (:...carrierIds)`, { carrierIds });
    }

    if (clientIds) {
      sql.andWhere(`o.clientId IN (:...clientIds)`, { clientIds });
    }

    if (projectIds) {
      sql.andWhere(`o.external_project_id IN (:...projectIds)`, { projectIds });
    }

    if (productIds) {
      sql.andWhere(`products.product_detail -> 'product' ->> 'id' IN (:...productIds)`, {
        productIds,
      });
    }

    if (regionIds) {
      sql.andWhere(`o.recipient_province_id IN (:...regionIds)`, { regionIds });
    }

    // if (tagIds) {
    //   sql.leftJoin(`o.tags`, `tags`);
    //   sql.andWhere(`tags.id IN (:...tagIds)`, { tagIds });
    // }
    if (tagIds) {
      const whIds = headers['warehouse-ids']?.split(',');

      const subQb = this.otRepository
        .createQueryBuilder('ot')
        .select('ot.id_order', 'id_order')
        .leftJoin(Order, 'order', 'order.id = ot.id_order')
        .where('ot.id_tag IN (:...tagIds)', { tagIds: filters.tagIds })
        .andWhere('order.companyId = :companyId', { companyId })
        .groupBy('ot.id_order');

      if (!isEmpty(whIds)) {
        subQb.andWhere('order.warehouseId IN (:...whIds)', { whIds });
      }
      if (!isEmpty(countryId)) subQb.andWhere('order.countryId = :countryId', { countryId });
      if (!!from)
        subQb.andWhere('order.createdAt >= :from', {
          from:
            from ??
            moment()
              .subtract(30, 'days')
              .startOf('day')
              .toDate(),
        });
      if (!!to)
        subQb.andWhere('order.createdAt <= :to', {
          to:
            to ??
            moment()
              .endOf('day')
              .toDate(),
        });
      if (filters.tagMethod == TagMethodType.Include) {
        sql.leftJoin('o.tags', 'tags');
        sql.andWhere('tags.id IN (:...tagIds)', { tagIds: filters.tagIds });
      }

      if (filters.tagMethod == TagMethodType.Exclude) {
        sql.andWhere(`o.id NOT IN (${subQb.getQuery()})`).setParameters(subQb.getParameters());
      }
    }
    const data = await sql.getRawMany();
    const unassignCarrier = data[0].UnassignCarrier;
    delete data[0].UnassignCarrier;
    const result = {};
    const _3pl = [];
    let currentObject = {};
    for (const item of data) {
      Object.keys(item).forEach(key => {
        currentObject[key] = item[key];
        if (Object.keys(currentObject).length === 3) {
          _3pl.push({ [`${Object.keys(currentObject)[0]}`]: currentObject });
          currentObject = {};
        }
      });
    }
    result['3pl'] = _3pl;
    result['UnassignCarrier'] = unassignCarrier;

    return result;
  }
  async getCarrierInformationDetail(
    filters: FilterDashboardGeneralInformation,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    const OutForDelivery = [
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.AwaitingReturn,
    ];
    const Delivered = [OrderFFMStatus.Delivered, OrderFFMStatus.DeliveredCompleted];
    const Returned = [
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedStocked,
      OrderFFMStatus.ReturnedCompleted,
    ];
    const Canceled = [OrderFFMStatus.Draft, OrderFFMStatus.Canceled];
    const InboundProcessing = [
      OrderFFMStatus.AwaitingCollection,
      OrderFFMStatus.Collecting,
      OrderFFMStatus.Awaiting3PLPickup,
    ];

    let sql = this.orderRepository
      .createQueryBuilder('o')

      .select(`carrier.code`, `code`)
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN  o.status NOT IN (${Canceled}) AND (carriers.id IS NOT NULL AND carriers.status = 'activated') THEN o."id" ELSE NULL END)`,
        `TotalAssigned`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${InboundProcessing}) THEN o."id" ELSE NULL END )`,
        `InboundProcessing`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status = (${OrderFFMStatus.PickedUp3PL}) THEN o."id" ELSE NULL END )`,
        `3PLPickedUp`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${OutForDelivery}) THEN o."id" ELSE NULL END )`,
        `OutForDelivery`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${Delivered}) THEN o."id" ELSE NULL END )`,
        `Delivered`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${Returned}) THEN o."id" ELSE NULL END )`,
        `Returned`,
      );
    sql = await this.checkConditionQuery(headers, companyId, countryId, sql, filters);
    sql
      .leftJoin(`carriers.carrier`, `carrier`)
      .andWhere('o.countryId =:countryId', { countryId })
      .andWhere('o.companyId =:companyId', { companyId })
      .andWhere('carrier.code IS NOT NULL')
      .andWhere('carriers.waybill_number IS NOT NULL')
      .andWhere(`carriers.waybill_number != ''`)
      .groupBy(`carrier.code`);
    return sql.getRawMany();
  }

  async getClientInformationDetail(
    filters: FilterDashboardGeneralInformation,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    const OutForDelivery = [
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.AwaitingReturn,
    ];
    const Delivered = [OrderFFMStatus.Delivered, OrderFFMStatus.DeliveredCompleted];
    const Returned = [
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedStocked,
      OrderFFMStatus.ReturnedCompleted,
    ];
    const InboundProcessing = [
      OrderFFMStatus.AwaitingStock,
      OrderFFMStatus.Reconfirm,
      OrderFFMStatus.Confirmed,
      OrderFFMStatus.AwaitingCollection,
      OrderFFMStatus.Collecting,
      OrderFFMStatus.Awaiting3PLPickup,
    ];
    const Canceled = [OrderFFMStatus.Draft, OrderFFMStatus.Canceled];
    let sql = this.orderRepository.createQueryBuilder('o');
    sql
      .select(`o.client_id`, `client_id`)
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status NOT IN (${Canceled}) THEN o."id" ELSE NULL END )`,
        `total`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${InboundProcessing}) THEN o."id" ELSE NULL END )`,
        `InboundProcessing`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status = (${OrderFFMStatus.PickedUp3PL}) THEN o."id" ELSE NULL END )`,
        `3PLPickedUp`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${OutForDelivery}) THEN o."id" ELSE NULL END )`,
        `outForDelivery`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${Delivered}) THEN o."id" ELSE NULL END )`,
        `delivered`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${Returned}) THEN o."id" ELSE NULL END )`,
        `returned`,
      );
    sql = await this.checkConditionQuery(headers, companyId, countryId, sql, filters);
    sql
      .andWhere('o.countryId =:countryId', { countryId })
      .andWhere('o.companyId =:companyId', { companyId })
      .andWhere('o.client_id IS NOT NULL')
      .groupBy(`o.client_id`);

    const data = await sql.getRawMany();
    const clientIds = data.map(x => x.client_id) ?? [];
    if (clientIds.length > 0) {
      const { data: client } = await this.amqpConnection.request<Record<string, any>>({
        exchange: 'identity-service-roles',
        routingKey: 'get-users-by-filter',
        payload: {
          filter: {
            ids: clientIds,
            countryId,
            status: [UserStatus.active],
            companyIds: [companyId],
          },
        },
        timeout: 10000,
      });
      const clients = client as User[];
      const clientLookup: Record<string, User> = reduce(
        clients,
        (prev, item) => {
          prev[item.id] = item;
          return prev;
        },
        {},
      );

      for (const item of data) {
        item[`name`] = clientLookup[item?.client_id]?.name;
      }
    }
    return data;
  }

  async getRegionInformation(
    filters: FilterDashboardGeneralInformation,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    const PickedUPOrders = [
      OrderFFMStatus.PickedUp3PL,
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.Delivered,
      OrderFFMStatus.AwaitingReturn,
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedCompleted,
      OrderFFMStatus.DeliveredCompleted,
      OrderFFMStatus.ReturnedStocked,
    ];
    const OutForDelivery = [
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.AwaitingReturn,
    ];
    const Delivered = [OrderFFMStatus.Delivered, OrderFFMStatus.DeliveredCompleted];
    const Returned = [
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedStocked,
      OrderFFMStatus.ReturnedCompleted,
    ];
    const InboundProcessing = [
      OrderFFMStatus.AwaitingStock,
      OrderFFMStatus.Reconfirm,
      OrderFFMStatus.Confirmed,
      OrderFFMStatus.AwaitingCollection,
      OrderFFMStatus.Collecting,
      OrderFFMStatus.Awaiting3PLPickup,
    ];
    const Canceled = [OrderFFMStatus.Draft, OrderFFMStatus.Canceled];

    let sql = this.orderRepository.createQueryBuilder('o');
    sql
      .select(`o.recipient_province_id`, `recipient_province_id`)
      .addSelect(`o.recipient_province`, `name`)
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status NOT IN (${Canceled}) THEN o."id" ELSE NULL END )`,
        `total`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${InboundProcessing}) THEN o."id" ELSE NULL END )`,
        `inbound_processing`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status = (${OrderFFMStatus.PickedUp3PL}) THEN o."id" ELSE NULL END )`,
        `3pl_picked_up`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${PickedUPOrders}) THEN o."id" ELSE NULL END )`,
        `picked_up_orders`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${OutForDelivery}) THEN o."id" ELSE NULL END )`,
        `out_for_delivery`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${Delivered}) THEN o."id" ELSE NULL END )`,
        `delivered`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status IN (${Returned}) THEN o."id" ELSE NULL END )`,
        `returned`,
      );
    sql = await this.checkConditionQuery(headers, companyId, countryId, sql, filters);
    sql
      .andWhere('o.countryId =:countryId', { countryId })
      .andWhere('o.companyId =:companyId', { companyId })
      .groupBy(`o.recipient_province_id`)
      .addGroupBy(`o.recipient_province`);
    const result = await sql.getRawMany();
    return result;
  }

  async getProductInformation(
    filters: FilterDashboardGeneralInformation,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];

    let sql = this.orderRepository.createQueryBuilder('o');
    sql
      .select(
        `((replace(replace((products.product_detail -> 'product' ->> 'categories')::text,'[','{'),']','}'))::text[])[1]`,
        `categories`,
      )
      .addSelect(`SUM ( products.quantity )`, `total`)
      .where('o.countryId =:countryId', { countryId });
    sql = await this.checkConditionQuery(headers, companyId, countryId, sql, filters);

    sql
      .andWhere('o.companyId =:companyId', { companyId })
      .andWhere(`products.product_detail -> 'product' ->> 'categories' IS NOT NULL`)
      .groupBy(`products.product_detail -> 'product' ->> 'categories'`)
      .orderBy(`total`, `DESC`);

    return sql.getRawMany();
  }

  async getProductInformationDetail(
    filters: FilterDashboardGeneralInformation,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const companyId = request?.user?.companyId;
    const countryId = headers['country-ids'];
    const OutForDelivery = [
      OrderFFMStatus.InTransit,
      OrderFFMStatus.Stocked3PL,
      OrderFFMStatus.InDelivery,
      OrderFFMStatus.FailedDelivery,
      OrderFFMStatus.AwaitingReturn,
    ];
    const Delivered = [OrderFFMStatus.Delivered, OrderFFMStatus.DeliveredCompleted];
    const Returned = [
      OrderFFMStatus.InReturn,
      OrderFFMStatus.Returned,
      OrderFFMStatus.ReturnedStocked,
      OrderFFMStatus.ReturnedCompleted,
    ];
    const Canceled = [OrderFFMStatus.Draft, OrderFFMStatus.Canceled];

    let sql = this.orderRepository
      .createQueryBuilder('o')
      .select(`"products"."product_detail" -> 'product' ->> 'name'`, `product_name`)
      .addSelect(`"products"."product_detail" -> 'product' ->> 'clientId'`, `clientId`)
      .addSelect(
        `((replace(replace((products.product_detail -> 'product' ->> 'categories')::text,'[','{'),']','}'))::text[])[1]`,
        `categories`,
      )
      .addSelect(
        `COUNT ( DISTINCT CASE WHEN o.status NOT IN (${Canceled}) THEN o."id" ELSE NULL END )`,
        `orders`,
      )
      .addSelect(`SUM ( products.quantity )`, `quantity`)
      .addSelect(
        `SUM ( CASE WHEN o.status IN (${OutForDelivery}) THEN products.quantity ELSE 0 END )`,
        `out_for_delivery`,
      )
      .addSelect(
        `SUM ( CASE WHEN o.status IN (${Delivered}) THEN products.quantity ELSE 0 END )`,
        `delivered`,
      )
      .addSelect(
        `SUM ( CASE WHEN o.status IN (${Returned}) THEN products.quantity ELSE 0 END )`,
        `returned`,
      )
      .where('o.countryId =:countryId', { countryId })
      .andWhere('o.companyId =:companyId', { companyId })
      // .andWhere(`products.product_detail -> 'product' ->> 'categories' IS NOT NULL`)
      .groupBy(`"products"."product_detail" -> 'product' ->> 'name'`)
      .addGroupBy(`"products"."product_detail" -> 'product' ->> 'clientId'`)
      .addGroupBy(`products.product_detail -> 'product' ->> 'categories'`)
      .orderBy(`"products"."product_detail" -> 'product' ->> 'name'`, `ASC`);
    sql = await this.checkConditionQuery(headers, companyId, countryId, sql, filters);
    const result = await sql.getRawMany();
    const clientIds = result.map(x => x?.clientId);

    if (clientIds.length > 0) {
      const { data: client } = await this.amqpConnection.request<Record<string, any>>({
        exchange: 'identity-service-roles',
        routingKey: 'get-users-by-filter',
        payload: {
          filter: {
            ids: clientIds,
            countryId,
            status: [UserStatus.active],
            companyIds: [companyId],
          },
        },
        timeout: 10000,
      });
      const clients = client as User[];
      const clientLookup: Record<string, User> = reduce(
        clients,
        (prev, item) => {
          prev[item.id] = item;
          return prev;
        },
        {},
      );

      for (const item of result) {
        item[`clientName`] = clientLookup[item?.clientId]?.name;
      }
    }
    return result;
  }

  async exportCarrierAndClientInformation(
    filters: FilterDashboardGeneralInformation,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const carrierData = await this.getCarrierInformationDetail(filters, headers, request);
    let clientData = await this.getClientInformationDetail(filters, headers, request);
    clientData = clientData.sort((x, y) => x.name.localeCompare(y.name));

    let idxClient = 1;
    let idxCarrier = 1;
    let carrierParams = [];
    let clientParams = [];

    carrierParams = reduce(
      carrierData,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            idxCarrier++,
            next?.code ?? '',
            next?.TotalAssigned ?? '',
            next?.InboundProcessing ?? '',
            next['3PLPickedUp'] ?? '',
            next?.OutForDelivery ?? '',
            next?.Delivered ?? '',
            next?.Returned ?? '',
          ]);
        }
        return prev;
      },
      [
        [
          'No',
          'Carrier name',
          'Total',
          'Inbound Processing',
          '3PL Picked Up',
          'Out for delivery',
          'Delivered',
          'Returned',
        ],
      ],
    );
    clientParams = reduce(
      clientData,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            idxClient++,
            next?.name ?? '',
            next?.total ?? '',
            next?.InboundProcessing ?? '',
            next['3PLPickedUp'] ?? '',
            next?.outForDelivery ?? '',
            next?.delivered ?? '',
            next?.returned ?? '',
          ]);
        }
        return prev;
      },
      [
        [
          'No',
          'Client name',
          'Total orders',
          'Inbound Processing',
          '3PLPickedUp',
          'Out for delivery',
          'Delivered',
          'Returned',
        ],
      ],
    );

    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push({
      name: 'Carriers',
      data: carrierParams,
      options: {},
    });
    xlsxSheets.push({
      name: 'Clients',
      data: clientParams,
      options: {},
    });
    const buffer = xlsx.build(xlsxSheets);
    return buffer;
  }

  async exportRegionInformation(
    filters: FilterDashboardGeneralInformation,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const data = await this.getRegionInformation(filters, headers, request);
    let idx = 1;
    let params = [];

    params = reduce(
      data,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            idx++,
            next?.name ?? '',
            next?.total ?? '',
            next?.inbound_processing ?? '',
            next['3pl_picked_up'] ?? '',
            next?.picked_up_orders ?? '',
            next?.out_for_delivery ?? '',
            next?.delivered ?? '',
            next?.returned ?? '',
          ]);
        }
        return prev;
      },
      [
        [
          'No',
          'Provinces',
          'Orders',
          'Inbound Processing',
          '3PL Picked Up',
          'Picked Up Orders',
          'Out for delivery',
          'Delivered',
          'Returned',
        ],
      ],
    );

    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push({
      name: 'Regions',
      data: params,
      options: {},
    });
    const buffer = xlsx.build(xlsxSheets);
    return buffer;
  }

  async exportProductInformation(
    filters: FilterDashboardGeneralInformation,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<any> {
    const data = await this.getProductInformationDetail(filters, headers, request);
    let idx = 1;
    let params = [];

    params = reduce(
      data,
      (prev: (string | number)[][], next) => {
        if (!isNil(next)) {
          prev.push([
            idx++,
            next?.product_name ?? '',
            next?.clientName ?? '',
            next?.categories ?? '',
            next?.orders ?? '',
            next?.quantity ?? '',
            next?.out_for_delivery ?? '',
            next?.delivered ?? '',
            next?.returned ?? '',
          ]);
        }
        return prev;
      },
      [
        [
          'No',
          'Product',
          'Client',
          'Category',
          'Orders',
          'Quantity',
          'Out for delivery',
          'Delivered',
          'Returned',
        ],
      ],
    );

    const xlsxSheets: WorkSheet[] = [];
    xlsxSheets.push({
      name: 'Products',
      data: params,
      options: {},
    });
    const buffer = xlsx.build(xlsxSheets);
    return buffer;
  }
}
