import { BadGatewayException, BadRequestException, Injectable } from '@nestjs/common';
import * as Crypto from 'crypto';
import axios from 'axios';
import { Order } from '../../../entities/order.entity';
import { IWarehouse } from '../services/order-partners.service';
import * as moment from 'moment-timezone';
import { isNull, reduce } from 'lodash';
import { OrderCarrier } from '../../../entities/order-carrier.entity';
import { DistrictData, ProvinceData } from 'apps/ffm-order-api/src/constants/address-th-jnt-bangkok.contants';
@Injectable()
export class JntBangkokClient {
  private privateKey: string;
  private customerCode: string;
  private password: string;
  private apiAccount: string;

  constructor(privateKey: string, customerCode: string, password: string, apiAccount: string) {
    this.privateKey = privateKey;
    this.customerCode = customerCode;
    this.password = password;
    this.apiAccount = apiAccount;
  }

  private request = axios.create({
    baseURL: process.env.JNT_BANGKOK_API,
  });
  public async createOrder(order: Order, warehouse: IWarehouse, carrierId: string): Promise<{data?: any; error?: any}> {
    let whCity = warehouse.addressSplit[1]?.split('/')[0].trim(),
        whProv = warehouse.addressSplit[2]?.split('/')[0].trim(),
        whAddress = warehouse.fullAddress,
        whName = warehouse.name,
        whPhone = warehouse.phoneNumber,
        whPostCode = warehouse.postCode;
    if(warehouse?.isOnSender){
      const warehouseAddress = warehouse?.senderInformations?.find(x => x.carrierId == carrierId);
      if(warehouseAddress){
        whCity = DistrictData[warehouseAddress?.senderDistrict];
        whProv = ProvinceData[warehouseAddress?.senderProvince] ;
        whAddress = warehouseAddress?.senderAddress;
        whName = warehouseAddress?.senderName;
        whPhone = warehouseAddress?.senderPhone;
        whPostCode = warehouseAddress?.senderPostCode;
      }
    }
    const password = await this.getPassword(this.customerCode, this.password);
    const data = {
      actionType: 'add',
      customerCode: this.customerCode,
      password,
      txlogisticId: order.displayId,
      orderType: 1,
      serviceType: 6,
      deliveryType: 1,
      expressType: 'EZ',
      sender: {
        name: whName,
        postCode: whPostCode,
        mobile: whPhone,
        // phone: warehouse.phoneNumber,
        city: whCity,
        prov: whProv,
        address: whAddress,
      },
      receiver: {
        name: order.recipientName,
        postCode: order.recipientPostCode,
        mobile: order.recipientPhone,
        // phone: order.recipientPhone,
        city: DistrictData[order.recipientDistrict],
        prov: ProvinceData[order.recipientProvince],
        address: order.recipientAddress,
      },
      remark: order.products
        .map((item) => {
          const { product: { name }, originSku } = item.productDetail || {};
          return `${name} - ${originSku} x ${item.quantity}`;
        })
        .join(', ')
        .substring(0, 200),
      // items: order.products
      //   .map((item) => {
      //     const { originSku } = item.productDetail || {};
      //     return {
      //       itemName: `${originSku} x ${item?.quantity}`,
      //       number: `${item?.quantity}`,
      //       itemValue: `${item?.quantity * item?.price}`,
      //     };
      //   }),
      item:[{
        itemName: order.products.map(x=> {return `${x.productDetail.originSku} x ${x?.quantity}`}).join(', '),
        number: reduce(
          order.products,
          (p: any, n: any) => {
            p += n?.quantity;
            return p;
          },
          0,
        ),
        itemValue: order?.subTotal,
      }],
      packageInfo: {
        packageQuantity: 1,
        weight: Math.round((order?.totalWeight / 1000) * 100) / 100,
      },
      codInfo: {
        codValue: `${order?.totalPrice}` || '0',
      },
      createOrderTime: moment.tz('Asia/Bangkok').format('yyyy-MM-DD HH:mm:ss'),
      sendStartTime: moment.tz('Asia/Bangkok').format('yyyy-MM-DD HH:mm:ss'),
      sendEndTime: moment.tz('Asia/Bangkok').add(24, 'hours').format('yyyy-MM-DD HH:mm:ss'),
    };
    const headersSign = await this.getHeadersSign(this.privateKey, JSON.stringify(data))
    console.log("🚀🚀🚀 ~ JntBangkokClient ~ createOrder ~ data:", data);
    // throw new BadRequestException(data)
    try {
      const response = await this.request.post('webopenplatformapi/api/order/addOrder', new URLSearchParams({bizContent: JSON.stringify(data)}).toString(),{
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'digest': headersSign,
          'apiAccount': this.apiAccount,
          'timestamp': 7,
        }
      });
      console.log('rod JNT Bangkok post', response.data);
      if(!response.data.data) return {error: response?.data?.msg}
      return {data: response.data};
    } catch (e) {
      console.log('rod error JNT Bangkok post', e);
      return {
        error: e?.response?.msg || {},
      };
    }
  }

  public async cancelOrder(order: OrderCarrier) {
    const password = await this.getPassword(this.customerCode, this.password);
    const data = {
      customerCode: this.customerCode,
      password: password,
      txlogisticId: order?.order?.displayId,
      billCode: order?.waybillNumber,
      reason: 'System canceled',
    };
    const headersSign = await this.getHeadersSign(this.privateKey, JSON.stringify(data))

    try {
      const response = await this.request.post('webopenplatformapi/api/order/cancelOrder', new URLSearchParams({bizContent: JSON.stringify(data)}).toString(),{
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'digest': headersSign,
          'apiAccount': this.apiAccount,
          'timestamp': 7,
        }
      });
      console.log('cancel rod JNT Bangkok post', response.data);
      if(isNull(response.data.data)) return {error: response?.data?.msg}
      return response.data.data;
    } catch (e) {
      console.log('cancel rod error JNT Bangkok post', e);
      return {
        error: e?.response?.msg || {},
      };
    }
  }

  public async trackingOrder(order: Order): Promise<{data?: any; error?: any}> {
    const password = await this.getPassword(this.customerCode, this.password);
    const data = {
      customerCode: this.customerCode,
      txlogisticId: order?.lastCarrier?.waybillNumber,
      password: password,
      "lang": "th"
    };
    const headersSign = await this.getHeadersSign(this.privateKey, JSON.stringify(data))

    try {
      const response = await this.request.post('webopenplatformapi/api/logistics/trace', new URLSearchParams({bizContent: JSON.stringify(data)}).toString(),{
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'digest': headersSign,
          'apiAccount': this.apiAccount,
          'timestamp': 7,
        }
      });
      console.log('rod JNT Bangkok post', response.data);
      if(isNull(response.data.data)) return {error: response?.data?.msg}
      return response.data;
    } catch (e) {
      console.log('rod error JNT Bangkok post', e);
      return {
        error: e?.response?.msg || {},
      };
    }
  }


  private async getPassword(customerCode: string, pwd: string){
    const password = await this.request.post('openplatformweb/help-center/accountSign', {customerCode, pwd}, {
        headers: {
          'Content-Type': `application/json`,
        },
      }); 
    return password?.data?.data;
  }

  private async getHeadersSign(privateKey: string, json: string){
    const bizData = `${json}${privateKey}`;
    try {
      const md5Hash = Crypto.createHash('md5').update(bizData, 'utf8').digest();
      const sign = md5Hash.toString('base64');
      return sign;
    } catch (e) {
      throw new Error(e.message);
    }
  }




}
