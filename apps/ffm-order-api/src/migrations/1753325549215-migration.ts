import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1753325549215 implements MigrationInterface {
    name = 'migration1753325549215'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "marketplace_variant" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "id" SERIAL NOT NULL, "platform_id" character varying, "sku" character varying NOT NULL, "client_id" character varying, "creator_id" character varying, "last_editor_id" character varying, "status" integer NOT NULL DEFAULT '1', "biz_id" character varying, "avatar" character varying, "country_id" integer, "price" double precision NOT NULL DEFAULT '0', "inventory" double precision NOT NULL DEFAULT '0', "id_product" integer, CONSTRAINT "PK_1135853be9091bbdcf522c04965" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "marketplace_product" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "id" SERIAL NOT NULL, "platform_id" character varying, "name" character varying, "client_id" character varying, "creator_id" character varying, "last_editor_id" character varying, "status" integer NOT NULL DEFAULT '1', "biz_id" character varying, "country_id" integer, "cover" text array, CONSTRAINT "PK_60298e5d40ab98812ce8c7ad779" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "marketplace_variant" ADD CONSTRAINT "FK_0496151661ad6e37ba2d1d2674a" FOREIGN KEY ("id_product") REFERENCES "marketplace_product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "marketplace_variant" DROP CONSTRAINT "FK_0496151661ad6e37ba2d1d2674a"`);
        await queryRunner.query(`DROP TABLE "marketplace_product"`);
        await queryRunner.query(`DROP TABLE "marketplace_variant"`);
    }

}
