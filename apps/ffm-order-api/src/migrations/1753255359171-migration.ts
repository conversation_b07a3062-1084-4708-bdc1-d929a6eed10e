import {MigrationInterface, QueryRunner} from "typeorm";

export class migration1753255359171 implements MigrationInterface {
    name = 'migration1753255359171'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "marketplace_integration" ADD "platform_cipher" character varying(250)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "marketplace_integration" DROP COLUMN "platform_cipher"`);
    }

}
