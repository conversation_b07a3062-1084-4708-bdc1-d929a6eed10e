import { MigrationInterface, QueryRunner } from 'typeorm';

export class migration1752131585108 implements MigrationInterface {
  name = 'migration1752131585108';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE OR REPLACE FUNCTION "public"."fn_log_system"()
    RETURNS "pg_catalog"."trigger" AS $BODY$ DECLARE
          changes hstore;
      before_changes hstore;
      after_changes hstore;
      parent_table_name TEXT;
      record_id TEXT;
      record_id_column_name TEXT;
      parent_id TEXT;
      parent_id_column_name TEXT;
      parent_id_ref_column_name TEXT;
      ACTION TEXT;
      array_fields_remove TEXT [];
      array_fields_fee TEXT [];
      array_fields_customer TEXT [];
      array_fields_customer_proof_of_change TEXT [];
      array_fields_fee_proof_of_change TEXT [];
      creator_column_name TEXT;
      TYPE TEXT;
      event TEXT;
      type_status TEXT;
      event_status TEXT;
      creator_id TEXT;
      type_create TEXT;
      type_update_status TEXT;
      status INT;
      BEGIN
              type_create := 'type_create';
          type_update_status := 'type_update_status';
          ACTION := TG_OP;
          parent_table_name := TG_ARGV [ 0 ];
          creator_column_name := TG_ARGV [ 1 ];
          record_id_column_name := TG_ARGV [ 2 ];
          parent_id_column_name := TG_ARGV [ 3 ];-- name column parent_id in table item
          array_fields_fee := TG_ARGV [ 4 ]:: TEXT [];
          parent_id_ref_column_name := TG_ARGV [ 5 ];-- name column id parent table
          array_fields_customer := TG_ARGV [ 6 ]:: TEXT [];
          array_fields_customer_proof_of_change := TG_ARGV [ 7 ]:: TEXT [];
          array_fields_fee_proof_of_change := TG_ARGV [ 8 ]:: TEXT [];
          IF
              parent_table_name IS NULL 
              OR parent_table_name = 'null' THEN
                  parent_table_name := NULL;
              
          END IF;
          IF
              record_id_column_name IS NULL 
              OR record_id_column_name = 'null' THEN
                  record_id_column_name := 'id';
              
          END IF;
          IF
              parent_id_ref_column_name IS NULL 
              OR parent_id_ref_column_name = 'null' THEN
                  parent_id_ref_column_name := 'id';
              
          END IF;
          IF
              creator_column_name IS NULL 
              OR creator_column_name = 'null' THEN
                  creator_column_name := 'last_updated_by';
              
          END IF;
          IF
              array_fields_fee IS NULL THEN
                  array_fields_fee := ARRAY []:: TEXT [];
              
          END IF;
          IF
              array_fields_customer IS NULL THEN
                  array_fields_customer := ARRAY []:: TEXT [];
              
          END IF;
          IF
              TG_TABLE_NAME = 'orders' THEN
              IF
                  TG_OP = 'INSERT' THEN
                  IF
                      hstore ( NEW ) -> type_create = '1' THEN
                          event := 'Create';
                      TYPE := 'Sales Order';
                      ELSE event := 'Create';
                      TYPE := 'Sales Order (by Bulk Actions)';
                      
                  END IF;
                  ELSE event := 'Change';
                  TYPE := 'Fee';
                  
              END IF;
              
          END IF;
          IF
              TG_TABLE_NAME = 'order_products' THEN
                  event := 'Change';
              TYPE := 'Product';
              
          END IF;
          IF
              TG_TABLE_NAME = 'notes' THEN
                  event := 'Add';
              TYPE := 'Messages';
              
          END IF;
          IF
              TG_TABLE_NAME = 'order_carriers' THEN
                  event := 'Change';
              TYPE := 'Carrier Information';
              
          END IF;
          IF
              EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = TG_TABLE_NAME AND COLUMN_NAME = creator_column_name ) THEN
              IF
                  TG_OP = 'DELETE' THEN
                      creator_id := hstore ( OLD ) -> creator_column_name;
                  ELSE creator_id := hstore ( NEW ) -> creator_column_name;
                  
              END IF;
              ELSE
              IF
                  creator_id IS NULL 
                  AND creator_column_name IS NOT NULL 
                  AND parent_table_name IS NOT NULL 
                  AND parent_id_ref_column_name IS NOT NULL THEN
                  EXECUTE'SELECT ' || QUOTE_IDENT( creator_column_name ) || ' "creator_id" FROM ' || QUOTE_IDENT( parent_table_name ) || ' WHERE ' || QUOTE_IDENT( parent_id_ref_column_name ) || '::TEXT = $1' INTO creator_id USING hstore ( CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END ) -> parent_id_column_name;
      
      END IF;
      
      END IF;
      RAISE NOTICE'query %',
      'SELECT ' || QUOTE_IDENT( creator_column_name ) || ' "creator_id" FROM ' || QUOTE_IDENT( parent_table_name ) || ' WHERE ' || QUOTE_IDENT( parent_id_column_name ) || '::TEXT = $1';
      RAISE NOTICE'parent_id_column_name %',
      parent_id_column_name;
      RAISE NOTICE'parent_id_ref_column_name %',
      parent_id_ref_column_name;
      RAISE NOTICE'parent_id %',
      hstore ( CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END ) -> parent_id_column_name;
      RAISE NOTICE'creator_column_name %',
      creator_column_name;
      RAISE NOTICE'creator_id %',
      creator_id;
      IF
          ( TG_OP = 'DELETE' ) THEN
              record_id := hstore ( OLD ) -> record_id_column_name;
          parent_id := hstore ( OLD ) -> parent_id_column_name;
          IF
              parent_id = 'null' THEN
                  parent_id := NULL;
              
          END IF;
          EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, before_changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9))' USING parent_table_name,
          parent_id,
          TG_OP,
          TG_TABLE_NAME,
          record_id,
          hstore_to_array ( hstore ( OLD ) ),
          creator_id,
          TYPE,
          event;
          ELSE record_id := hstore ( NEW ) -> record_id_column_name;
          parent_id := hstore ( NEW ) -> parent_id_column_name;
          IF
              parent_id = 'null' THEN
                  parent_id := NULL;
              
          END IF;
          after_changes := hstore ( NEW );
          IF
              ( TG_OP <> 'INSERT' ) THEN
                  before_changes := hstore ( OLD );
              changes := hstore ( NEW ) - hstore ( OLD );
              ELSE changes := hstore ( NEW );
              
          END IF;
          IF
              ( TG_TABLE_NAME = 'orders' AND TG_OP <> 'INSERT' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'warehouse_id' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  ARRAY [ NEW.warehouse_id ],
                  ARRAY [ NEW.warehouse_id ],
                  ARRAY [ OLD.warehouse_id ],
                  creator_id,
                  'Change',
                  'Warehouse';
              
          END IF;
          IF
              ( TG_TABLE_NAME = 'orders' AND TG_OP <> 'INSERT' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'return_warehouse_id' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  ARRAY [ NEW.return_warehouse_id ],
                  ARRAY [ NEW.return_warehouse_id ],
                  ARRAY [ OLD.return_warehouse_id ],
                  creator_id,
                  'Change',
                  'Return Warehouse';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND ( ARRAY_LENGTH( array_fields_customer, 1 ) > 0 ) AND ( changes ) ?| array_fields_customer ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  hstore_to_array ( slice ( changes, array_fields_customer_proof_of_change ) ),
                  hstore_to_array ( slice ( hstore ( NEW ), array_fields_customer_proof_of_change ) ),
                  hstore_to_array ( slice ( before_changes, array_fields_customer_proof_of_change ) ),
                  creator_id,
                  'Change',
                  'Recipient';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND ( ARRAY_LENGTH( array_fields_fee, 1 ) > 0 ) AND ( changes ) ?| array_fields_fee ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  hstore_to_array ( slice ( changes, array_fields_fee_proof_of_change ) ),
                  hstore_to_array ( slice ( hstore ( NEW ), array_fields_fee_proof_of_change ) ),
                  hstore_to_array ( slice ( before_changes, array_fields_fee_proof_of_change ) ),
                  creator_id,
                  'Change',
                  'Fee';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'internal_note' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  ARRAY [ NEW.internal_note ],
                  ARRAY [ NEW.internal_note ],
                  ARRAY [ OLD.internal_note ],
                  creator_id,
                  'Change',
                  'Internal Notes';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'waybill_note' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  hstore_to_array ( hstore ( NEW ) ),
                  ARRAY [ NEW.waybill_note ],
                  ARRAY [ OLD.waybill_note ],
                  creator_id,
                  'Change',
                  'Waybill Note';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'type' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  hstore_to_array ( hstore ( NEW ) ),
                  ARRAY [ NEW.type ],
                  ARRAY [ OLD.type ],
                  creator_id,
                  'Change',
                  'Order Type';
              
          END IF;
          IF
              ( TG_TABLE_NAME <> 'orders' ) THEN
              IF
                  ( ARRAY_LENGTH( array_fields_fee, 1 ) > 0 ) THEN
                      array_fields_remove := ( array_fields_remove || array_fields_fee );
                  
              END IF;
              changes := DELETE ( changes, array_fields_remove );
              after_changes := DELETE ( after_changes, array_fields_remove );
              before_changes := DELETE ( before_changes, array_fields_remove );
              ELSE before_changes := slice ( before_changes, array_fields_fee );
              changes := '';
              after_changes := slice ( after_changes, array_fields_fee );
              
          END IF;
          IF
              ( changes <> '' ) THEN
              IF
                  ( TG_TABLE_NAME = 'order_carriers' ) THEN
                  IF
                      ( exist ( hstore ( NEW ), 'type_update' ) ) THEN
                      IF
                          ( NEW.type_update = 2 ) THEN
                              event := 'Changed';
                          TYPE := 'Carrier Information (by Bulk Actions)';
                          
                      END IF;
                      IF
                          ( NEW.type_update = 3 ) THEN
                              event := 'Request Outbound Delivery';
                          TYPE := '';
                          
                      END IF;
                      IF
                          ( NEW.type_update = 4 ) THEN
                              event := 'Cancel Delivery';
                          TYPE := '';
                          
                      END IF;
                      IF
                          ( NEW.type_update = 5 ) THEN
                              event := 'Request Outbound Delivery';
                          TYPE := '(by Bulk Actions)';
                          
                      END IF;
                      IF
                          ( NEW.type_update = 6 ) THEN
                              event := 'Cancel Delivery';
                          TYPE := '(by Bulk Actions)';
                          
                      END IF;
                      
                  END IF;
                  
              END IF;
              EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, before_changes, after_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
              parent_id,
              TG_OP,
              TG_TABLE_NAME,
              record_id,
              hstore_to_array ( changes ),
              hstore_to_array ( before_changes ),
              hstore_to_array ( after_changes ),
              creator_id,
              event,
              TYPE;
              
          END IF;
          
      END IF;
      IF
          ( TG_TABLE_NAME = 'orders' ) THEN
              event_status := 'Change';
          type_status := 'Status';
          IF
              hstore ( NEW ) -> type_update_status = '2' THEN
                  type_status := 'Status (by Bulk Actions)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '4' THEN
                  type_status := 'Status (by Export Collection List)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '3' THEN
                  EXECUTE'SELECT CONCAT($1 ,c.name, $2)  FROM order_carriers oc LEFT JOIN carrier c ON c.id = oc.carrier_id WHERE oc.order_id = $3::INT ORDER BY oc.id DESC LIMIT 1' INTO type_status USING 'Status (by ',
                  ')',
                  record_id;
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '5' THEN
                  type_status := 'Status (by Bulk Update 3PL Information)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '6' THEN
                  type_status := 'Status (by Scan to Finish Packaging Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '7' THEN
                  type_status := 'Status (by Import to Finish Packaging Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '8' THEN
                  type_status := 'Status (by Scan to Handover Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '9' THEN
                  type_status := 'Status (by Import to Handover Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '10' THEN
                  type_status := 'Status (by ReImport Cancel Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '11' THEN
                  type_status := 'Status (by ReImport Returned Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '12' THEN
                  type_status := 'Revert status';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '14' THEN
                  type_status := 'Mismatch COD';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' ) THEN
              IF
                  ( exist ( hstore ( NEW ) - hstore ( OLD ), 'status' ) ) THEN
                      EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, before_changes, after_changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                      parent_id,
                      'STATUS',
                      TG_TABLE_NAME,
                      record_id,
                      ARRAY [ NEW.status ],
                      ARRAY [ OLD.status ],
                      hstore_to_array ( hstore ( NEW ) ),
                      creator_id,
                      type_status,
                      event_status;
                  
              END IF;
              ELSE
              IF
                  ( exist ( hstore ( NEW ), 'status' ) ) THEN
                      EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10))' USING parent_table_name,
                      parent_id,
                      'STATUS',
                      TG_TABLE_NAME,
                      record_id,
                      ARRAY [ NEW.status ],
                      hstore_to_array ( hstore ( NEW ) ),
                      creator_id,
                      type_status,
                      event_status;
                  
              END IF;
              
          END IF;
          
      END IF;
      IF
          ( TG_TABLE_NAME = 'orders' AND exist ( hstore ( NEW ), 'customer_edd' ) ) THEN
          IF
              ( TG_OP <> 'INSERT' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'customer_edd' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  ARRAY [ NEW.customer_edd ],
                  ARRAY [ NEW.customer_edd ],
                  ARRAY [ OLD.customer_edd ],
                  creator_id,
                  'Change',
                  'Customer EDD';
              
          END IF;
          IF
              TG_OP = 'INSERT' THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  ARRAY [ NEW.customer_edd ],
                  ARRAY [ NEW.customer_edd ],
                  creator_id,
                  'Change',
                  'Customer EDD';
              
          END IF;
          
      END IF;
      RETURN NEW;
      
      END $BODY$
    LANGUAGE plpgsql VOLATILE
    COST 100`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE OR REPLACE FUNCTION "public"."fn_log_system"()
    RETURNS "pg_catalog"."trigger" AS $BODY$ DECLARE
          changes hstore;
      before_changes hstore;
      after_changes hstore;
      parent_table_name TEXT;
      record_id TEXT;
      record_id_column_name TEXT;
      parent_id TEXT;
      parent_id_column_name TEXT;
      parent_id_ref_column_name TEXT;
      ACTION TEXT;
      array_fields_remove TEXT [];
      array_fields_fee TEXT [];
      array_fields_customer TEXT [];
      array_fields_customer_proof_of_change TEXT [];
      array_fields_fee_proof_of_change TEXT [];
      creator_column_name TEXT;
      TYPE TEXT;
      event TEXT;
      type_status TEXT;
      event_status TEXT;
      creator_id TEXT;
      type_create TEXT;
      type_update_status TEXT;
      status INT;
      BEGIN
              type_create := 'type_create';
          type_update_status := 'type_update_status';
          ACTION := TG_OP;
          parent_table_name := TG_ARGV [ 0 ];
          creator_column_name := TG_ARGV [ 1 ];
          record_id_column_name := TG_ARGV [ 2 ];
          parent_id_column_name := TG_ARGV [ 3 ];-- name column parent_id in table item
          array_fields_fee := TG_ARGV [ 4 ]:: TEXT [];
          parent_id_ref_column_name := TG_ARGV [ 5 ];-- name column id parent table
          array_fields_customer := TG_ARGV [ 6 ]:: TEXT [];
          array_fields_customer_proof_of_change := TG_ARGV [ 7 ]:: TEXT [];
          array_fields_fee_proof_of_change := TG_ARGV [ 8 ]:: TEXT [];
          IF
              parent_table_name IS NULL 
              OR parent_table_name = 'null' THEN
                  parent_table_name := NULL;
              
          END IF;
          IF
              record_id_column_name IS NULL 
              OR record_id_column_name = 'null' THEN
                  record_id_column_name := 'id';
              
          END IF;
          IF
              parent_id_ref_column_name IS NULL 
              OR parent_id_ref_column_name = 'null' THEN
                  parent_id_ref_column_name := 'id';
              
          END IF;
          IF
              creator_column_name IS NULL 
              OR creator_column_name = 'null' THEN
                  creator_column_name := 'last_updated_by';
              
          END IF;
          IF
              array_fields_fee IS NULL THEN
                  array_fields_fee := ARRAY []:: TEXT [];
              
          END IF;
          IF
              array_fields_customer IS NULL THEN
                  array_fields_customer := ARRAY []:: TEXT [];
              
          END IF;
          IF
              TG_TABLE_NAME = 'orders' THEN
              IF
                  TG_OP = 'INSERT' THEN
                  IF
                      hstore ( NEW ) -> type_create = '1' THEN
                          event := 'Create';
                      TYPE := 'Sales Order';
                      ELSE event := 'Create';
                      TYPE := 'Sales Order (by Bulk Actions)';
                      
                  END IF;
                  ELSE event := 'Change';
                  TYPE := 'Fee';
                  
              END IF;
              
          END IF;
          IF
              TG_TABLE_NAME = 'order_products' THEN
                  event := 'Change';
              TYPE := 'Product';
              
          END IF;
          IF
              TG_TABLE_NAME = 'notes' THEN
                  event := 'Add';
              TYPE := 'Messages';
              
          END IF;
          IF
              TG_TABLE_NAME = 'order_carriers' THEN
                  event := 'Change';
              TYPE := 'Carrier Information';
              
          END IF;
          IF
              EXISTS ( SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = TG_TABLE_NAME AND COLUMN_NAME = creator_column_name ) THEN
              IF
                  TG_OP = 'DELETE' THEN
                      creator_id := hstore ( OLD ) -> creator_column_name;
                  ELSE creator_id := hstore ( NEW ) -> creator_column_name;
                  
              END IF;
              ELSE
              IF
                  creator_id IS NULL 
                  AND creator_column_name IS NOT NULL 
                  AND parent_table_name IS NOT NULL 
                  AND parent_id_ref_column_name IS NOT NULL THEN
                  EXECUTE'SELECT ' || QUOTE_IDENT( creator_column_name ) || ' "creator_id" FROM ' || QUOTE_IDENT( parent_table_name ) || ' WHERE ' || QUOTE_IDENT( parent_id_ref_column_name ) || '::TEXT = $1' INTO creator_id USING hstore ( CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END ) -> parent_id_column_name;
      
      END IF;
      
      END IF;
      RAISE NOTICE'query %',
      'SELECT ' || QUOTE_IDENT( creator_column_name ) || ' "creator_id" FROM ' || QUOTE_IDENT( parent_table_name ) || ' WHERE ' || QUOTE_IDENT( parent_id_column_name ) || '::TEXT = $1';
      RAISE NOTICE'parent_id_column_name %',
      parent_id_column_name;
      RAISE NOTICE'parent_id_ref_column_name %',
      parent_id_ref_column_name;
      RAISE NOTICE'parent_id %',
      hstore ( CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END ) -> parent_id_column_name;
      RAISE NOTICE'creator_column_name %',
      creator_column_name;
      RAISE NOTICE'creator_id %',
      creator_id;
      IF
          ( TG_OP = 'DELETE' ) THEN
              record_id := hstore ( OLD ) -> record_id_column_name;
          parent_id := hstore ( OLD ) -> parent_id_column_name;
          IF
              parent_id = 'null' THEN
                  parent_id := NULL;
              
          END IF;
          EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, before_changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9))' USING parent_table_name,
          parent_id,
          TG_OP,
          TG_TABLE_NAME,
          record_id,
          hstore_to_array ( hstore ( OLD ) ),
          creator_id,
          TYPE,
          event;
          ELSE record_id := hstore ( NEW ) -> record_id_column_name;
          parent_id := hstore ( NEW ) -> parent_id_column_name;
          IF
              parent_id = 'null' THEN
                  parent_id := NULL;
              
          END IF;
          after_changes := hstore ( NEW );
          IF
              ( TG_OP <> 'INSERT' ) THEN
                  before_changes := hstore ( OLD );
              changes := hstore ( NEW ) - hstore ( OLD );
              ELSE changes := hstore ( NEW );
              
          END IF;
          IF
              ( TG_TABLE_NAME = 'orders' AND TG_OP <> 'INSERT' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'warehouse_id' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  ARRAY [ NEW.warehouse_id ],
                  ARRAY [ NEW.warehouse_id ],
                  ARRAY [ OLD.warehouse_id ],
                  creator_id,
                  'Change',
                  'Warehouse';
              
          END IF;
          IF
              ( TG_TABLE_NAME = 'orders' AND TG_OP <> 'INSERT' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'return_warehouse_id' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  ARRAY [ NEW.return_warehouse_id ],
                  ARRAY [ NEW.return_warehouse_id ],
                  ARRAY [ OLD.return_warehouse_id ],
                  creator_id,
                  'Change',
                  'Return Warehouse';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND ( ARRAY_LENGTH( array_fields_customer, 1 ) > 0 ) AND ( changes ) ?| array_fields_customer ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  hstore_to_array ( slice ( changes, array_fields_customer_proof_of_change ) ),
                  hstore_to_array ( slice ( hstore ( NEW ), array_fields_customer_proof_of_change ) ),
                  hstore_to_array ( slice ( before_changes, array_fields_customer_proof_of_change ) ),
                  creator_id,
                  'Change',
                  'Recipient';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND ( ARRAY_LENGTH( array_fields_fee, 1 ) > 0 ) AND ( changes ) ?| array_fields_fee ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  hstore_to_array ( slice ( changes, array_fields_fee_proof_of_change ) ),
                  hstore_to_array ( slice ( hstore ( NEW ), array_fields_fee_proof_of_change ) ),
                  hstore_to_array ( slice ( before_changes, array_fields_fee_proof_of_change ) ),
                  creator_id,
                  'Change',
                  'Fee';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'internal_note' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  ARRAY [ NEW.internal_note ],
                  ARRAY [ NEW.internal_note ],
                  ARRAY [ OLD.internal_note ],
                  creator_id,
                  'Change',
                  'Internal Notes';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' AND TG_TABLE_NAME = 'orders' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'waybill_note' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  hstore_to_array ( hstore ( NEW ) ),
                  ARRAY [ NEW.waybill_note ],
                  ARRAY [ OLD.waybill_note ],
                  creator_id,
                  'Change',
                  'Waybill Note';
              
          END IF;
          IF
              ( TG_TABLE_NAME <> 'orders' ) THEN
              IF
                  ( ARRAY_LENGTH( array_fields_fee, 1 ) > 0 ) THEN
                      array_fields_remove := ( array_fields_remove || array_fields_fee );
                  
              END IF;
              changes := DELETE ( changes, array_fields_remove );
              after_changes := DELETE ( after_changes, array_fields_remove );
              before_changes := DELETE ( before_changes, array_fields_remove );
              ELSE before_changes := slice ( before_changes, array_fields_fee );
              changes := '';
              after_changes := slice ( after_changes, array_fields_fee );
              
          END IF;
          IF
              ( changes <> '' ) THEN
              IF
                  ( TG_TABLE_NAME = 'order_carriers' ) THEN
                  IF
                      ( exist ( hstore ( NEW ), 'type_update' ) ) THEN
                      IF
                          ( NEW.type_update = 2 ) THEN
                              event := 'Changed';
                          TYPE := 'Carrier Information (by Bulk Actions)';
                          
                      END IF;
                      IF
                          ( NEW.type_update = 3 ) THEN
                              event := 'Request Outbound Delivery';
                          TYPE := '';
                          
                      END IF;
                      IF
                          ( NEW.type_update = 4 ) THEN
                              event := 'Cancel Delivery';
                          TYPE := '';
                          
                      END IF;
                      IF
                          ( NEW.type_update = 5 ) THEN
                              event := 'Request Outbound Delivery';
                          TYPE := '(by Bulk Actions)';
                          
                      END IF;
                      IF
                          ( NEW.type_update = 6 ) THEN
                              event := 'Cancel Delivery';
                          TYPE := '(by Bulk Actions)';
                          
                      END IF;
                      
                  END IF;
                  
              END IF;
              EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, before_changes, after_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
              parent_id,
              TG_OP,
              TG_TABLE_NAME,
              record_id,
              hstore_to_array ( changes ),
              hstore_to_array ( before_changes ),
              hstore_to_array ( after_changes ),
              creator_id,
              event,
              TYPE;
              
          END IF;
          
      END IF;
      IF
          ( TG_TABLE_NAME = 'orders' ) THEN
              event_status := 'Change';
          type_status := 'Status';
          IF
              hstore ( NEW ) -> type_update_status = '2' THEN
                  type_status := 'Status (by Bulk Actions)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '4' THEN
                  type_status := 'Status (by Export Collection List)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '3' THEN
                  EXECUTE'SELECT CONCAT($1 ,c.name, $2)  FROM order_carriers oc LEFT JOIN carrier c ON c.id = oc.carrier_id WHERE oc.order_id = $3::INT ORDER BY oc.id DESC LIMIT 1' INTO type_status USING 'Status (by ',
                  ')',
                  record_id;
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '5' THEN
                  type_status := 'Status (by Bulk Update 3PL Information)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '6' THEN
                  type_status := 'Status (by Scan to Finish Packaging Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '7' THEN
                  type_status := 'Status (by Import to Finish Packaging Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '8' THEN
                  type_status := 'Status (by Scan to Handover Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '9' THEN
                  type_status := 'Status (by Import to Handover Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '10' THEN
                  type_status := 'Status (by ReImport Cancel Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '11' THEN
                  type_status := 'Status (by ReImport Returned Order)';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '12' THEN
                  type_status := 'Revert status';
              
          END IF;
          IF
              hstore ( NEW ) -> type_update_status = '14' THEN
                  type_status := 'Mismatch COD';
              
          END IF;
          IF
              ( TG_OP <> 'INSERT' ) THEN
              IF
                  ( exist ( hstore ( NEW ) - hstore ( OLD ), 'status' ) ) THEN
                      EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, before_changes, after_changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                      parent_id,
                      'STATUS',
                      TG_TABLE_NAME,
                      record_id,
                      ARRAY [ NEW.status ],
                      ARRAY [ OLD.status ],
                      hstore_to_array ( hstore ( NEW ) ),
                      creator_id,
                      type_status,
                      event_status;
                  
              END IF;
              ELSE
              IF
                  ( exist ( hstore ( NEW ), 'status' ) ) THEN
                      EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, creator_id, type, event) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10))' USING parent_table_name,
                      parent_id,
                      'STATUS',
                      TG_TABLE_NAME,
                      record_id,
                      ARRAY [ NEW.status ],
                      hstore_to_array ( hstore ( NEW ) ),
                      creator_id,
                      type_status,
                      event_status;
                  
              END IF;
              
          END IF;
          
      END IF;
      IF
          ( TG_TABLE_NAME = 'orders' AND exist ( hstore ( NEW ), 'customer_edd' ) ) THEN
          IF
              ( TG_OP <> 'INSERT' AND exist ( hstore ( NEW ) - hstore ( OLD ), 'customer_edd' ) ) THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, before_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10), ($11))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  ARRAY [ NEW.customer_edd ],
                  ARRAY [ NEW.customer_edd ],
                  ARRAY [ OLD.customer_edd ],
                  creator_id,
                  'Change',
                  'Customer EDD';
              
          END IF;
          IF
              TG_OP = 'INSERT' THEN
                  EXECUTE'INSERT INTO logs (parent_table_name, parent_id, action, table_name, record_id, changes, after_changes, creator_id, event, type) VALUES(($1), ($2), ($3), ($4), ($5), ($6), ($7), ($8), ($9), ($10))' USING parent_table_name,
                  parent_id,
                  TG_OP,
                  TG_TABLE_NAME,
                  record_id,
                  ARRAY [ NEW.customer_edd ],
                  ARRAY [ NEW.customer_edd ],
                  creator_id,
                  'Change',
                  'Customer EDD';
              
          END IF;
          
      END IF;
      RETURN NEW;
      
      END $BODY$
    LANGUAGE plpgsql VOLATILE
    COST 100`);
  }
}
