import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { identityConnection } from 'core/constants/database-connection.constant';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { User } from './entities/user.entity';
import { Roles } from './entities/roles.entity';
import { AuthModule } from './modules/auth/auth.module';
import { ProjectsModule } from './modules/projects/projects.module';
import { CountriesModule } from './modules/countries/countries.module';
import { BaseAuthModule } from 'core/auth/auth.module';
import { RedisCacheModule } from 'core/cache/redisCache.module';
import { HttpCacheInterceptor } from 'core/filters/http-cache/http-cache.filter';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { BusinessesModule } from './modules/business/businesses.module';
import { CompaniesModule } from './modules/companies/companies.module';
import { ChangeLogsModule } from './modules/change-logs/change-logs.module';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { RequestFilterModule } from './modules/request-filter/request-filter.module';
import { TerminusModule } from '@nestjs/terminus';
import { IdentityController } from './identity.controller';
import { IdentityService } from './identity.service';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { getRmqHost } from 'core/utils/loadEnv';
import { BullModule } from '@nestjs/bull';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath:
        'apps/identity-api/' + (process.env.NODE_ENV ? `.env.${process.env.NODE_ENV}` : '.env'),
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      ...(process.env.DATABASE_USERNAME_REPLICATE
        ? {
            replication: {
              master: {
                host: process.env.DATABASE_HOST,
                port: parseInt(process.env.DATABASE_PORT),
                username: process.env.DATABASE_USERNAME,
                password: process.env.DATABASE_PASSWORD,
                database: process.env.DATABASE_IDENTITY,
              },
              slaves: [
                {
                  host: process.env.DATABASE_HOST_REPLICATE,
                  port: parseInt(process.env.DATABASE_PORT_REPLICATE),
                  username: process.env.DATABASE_USERNAME_REPLICATE,
                  password: process.env.DATABASE_PASSWORD_REPLICATE,
                  database: process.env.DATABASE_IDENTITY,
                },
              ],
            },
          }
        : {
            host: process.env.DATABASE_HOST,
            port: parseInt(process.env.DATABASE_PORT),
            username: process.env.DATABASE_USERNAME,
            password: process.env.DATABASE_PASSWORD,
            database: process.env.DATABASE_IDENTITY,
          }),
      entities: [__dirname + '/entities/*.entity{ .ts,.js}'],
      name: identityConnection,
      logging: ['error'],
    }),
    RedisModule.forRoot({
      config: {
        password: process.env.REDIS_PASSWORD,
        db: 1,
        port: parseInt(process.env.REDIS_PORT),
        host: process.env.REDIS_HOST,
        keyPrefix: `ag-identity-cache-${process.env.REDIS_DB}`,
      },
    }),
    TypeOrmModule.forFeature([User, Roles], identityConnection),
    AuthModule,
    ProjectsModule,
    CountriesModule,
    BaseAuthModule.forRoot(),
    RedisCacheModule,
    BusinessesModule,
    CompaniesModule,
    ChangeLogsModule,
    RequestFilterModule,
    RabbitMQModule.forRootAsync(RabbitMQModule, {
      useFactory: () => ({
        registerHandlers: !process.env.CONSUMER || process.env.CONSUMER == 'true' ? true : false,
        uri: getRmqHost(),
        prefetchCount: process.env.CONSUMER_PRE_FETCH ? Number(process.env.CONSUMER_PRE_FETCH) : 20,
        exchanges: [
          {
            name: 'identity-service-countries',
            type: 'direct',
          },
          {
            name: 'identity-service-projects',
            type: 'direct',
          },
          {
            name: 'OrderService.Orders.OnPosUsers',
            type: 'fanout',
          },
          {
            name: 'IdentityService.Users.OnPosUsersInserted',
            type: 'fanout',
          },
          {
            name: 'IdentityService.Users.OnPosUsersLinked',
            type: 'fanout',
          },
          {
            name: 'identity-service-roles',
            type: 'direct',
          },
          {
            name: 'identity-service-logs',
            type: 'direct',
          },
          {
            name: 'identity-service-projects',
            type: 'direct',
          },
          {
            name: 'identity-service-companies',
            type: 'direct',
          },
          {
            name: 'identity-service-users',
            type: 'direct',
          },
          {
            name: 'identity-service-partner',
            type: 'direct',
          },
          {
            name: 'identity-service',
            type: 'direct',
          },
        ],
      }),
    }),
    MailerModule.forRoot({
      transport: {
        host: 'smtp.larksuite.com',
        secure: true,
        port: 465,
        tls: {
          rejectUnauthorized: false,
        },
        auth: {
          user: process.env.MAIL_USER,
          pass: process.env.MAIL_PASS,
        },
      },
      defaults: {
        from: '"NOREPLY - AGF" <<EMAIL>>',
      },
      template: {
        dir: __dirname + '/templates/mail',
        adapter: new HandlebarsAdapter(),
        options: {
          strict: true,
        },
      },
    }),
    TerminusModule,
    BullModule.forRoot({
      redis: {
        password: process.env.REDIS_PASSWORD,
        db: 1,
        port: parseInt(process.env.REDIS_PORT),
        host: process.env.REDIS_HOST,
      },
      prefix: `agbiz-identity-queue-${process.env.REDIS_DB}`,
    }),
  ],
  providers: [
    IdentityService,
    {
      provide: APP_INTERCEPTOR,
      useClass: HttpCacheInterceptor,
    },
  ],
  controllers: [IdentityController],
})
export class IdentityModule {}
