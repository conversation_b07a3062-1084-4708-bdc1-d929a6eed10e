import { AmqpConnection, default<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RabbitRP<PERSON> } from '@golevelup/nestjs-rabbitmq';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ChangeLogDto } from 'apps/identity-api/src/dtos/change-log.dto';
import { CreateProjectDto, UpdateProjectDto } from 'apps/identity-api/src/dtos/create-project.dto';
import { ChangeLog } from 'apps/identity-api/src/entities/change-logs.entity';
import { Market } from 'apps/identity-api/src/entities/market.entity';
import { Project } from 'apps/identity-api/src/entities/projects.entity';
import { UserScope } from 'apps/identity-api/src/entities/user-scope.entity';
import { User } from 'apps/identity-api/src/entities/user.entity';
import { ProjectStatus } from 'apps/identity-api/src/enums/project-status.enum';
import { ProjectsLogsFilter } from 'apps/identity-api/src/filters/projects-logs.filter';
import {
  CountUsersOfProjectDto,
  ProjectFromFfmFilter,
  ProjectsFilter,
} from 'apps/identity-api/src/filters/projects.filter';
import { Product } from 'apps/identity-api/src/read-entities/catalog/product.entity';
import { plainToClass, plainToInstance } from 'class-transformer';
import { identityConnection } from 'core/constants/database-connection.constant';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import {
  camelCase,
  differenceBy,
  findIndex,
  isEmpty,
  isNil,
  mapKeys,
  omit,
  pickBy,
  snakeCase,
  uniq,
} from 'lodash';
import { getConnection, ILike, In, IsNull, Repository, UpdateResult } from 'typeorm';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import { ProjectSubscriber } from '../subscribers/project.subscriber';
import { FulfillmentPartnerClient } from 'apps/identity-api/src/entities/fulfillment-partner-client.entity';
import { CommonStatus } from 'core/enums/common-status.enum';

@Injectable()
export class ProjectService {
  constructor(
    @InjectRepository(Project, identityConnection)
    private projectRepository: Repository<Project>,
    @InjectRepository(FulfillmentPartnerClient, identityConnection)
    private ffmPartnerClientRepo: Repository<FulfillmentPartnerClient>,
    @InjectRepository(ChangeLog, identityConnection)
    private logsRepo: Repository<ChangeLog>,
    private projectSubscriber: ProjectSubscriber,
    private redisCache: RedisCacheService,
    private amqpConnection: AmqpConnection,
  ) {}

  async findByIds(ids: number[], companyId: number): Promise<Project[]> {
    return await this.projectRepository.findByIds(ids, {
      where: { companyId },
    });
  }

  async findById(id: number, user): Promise<Project> {
    // const scopeCountryIds = user.profiles.flatMap(p => p[4].map(it => it[0]));

    const qb = this.projectRepository
      .createQueryBuilder('p')
      .where('p.id = :id', { id })
      .leftJoinAndSelect('p.scopes', 'scopes')
      .leftJoinAndSelect('p.leader', 'leader')
      .leftJoinAndSelect('p.markets', 'markets')
      .leftJoinAndSelect('markets.country', 'country')
      .leftJoinAndSelect('markets.fulfillmentPartner', 'ffmPartner')
      .leftJoin('markets.fulfillmentPartnerClient', 'ffmPartnerClient')
      .leftJoin('ffmPartnerClient.client', 'client')
      .addSelect(['ffmPartnerClient.id', 'ffmPartnerClient.clientId'])
      .addSelect(['client.id', 'client.name', 'client.email']);

    const project = await qb.getOne();
    project.settings = mapKeys(project.settings, (value, key) => camelCase(key));
    if (!project) throw new NotFoundException();
    return project;
  }

  async find(
    pagination: PaginationOptions,
    filter: ProjectsFilter = {},
  ): Promise<[Project[], number]> {
    const qb = this.projectRepository
      .createQueryBuilder('p')
      .leftJoin('p.markets', 'market')
      .addSelect(['market.id', 'market.countryId', 'market.isActivate'])
      .leftJoin('market.country', 'country')
      .addSelect(['country.id', 'country.name', 'country.flag'])
      .unScope('country');

    const { query, status, isGetDeleted } = filter;

    if (pagination) qb.take(pagination?.limit).skip(pagination?.skip);
    if (!!query) qb.andWhere({ name: ILike(`%${query}%`) });
    if (status) qb.andWhere({ status: In(status) });
    if (isGetDeleted) qb.withDeleted();
    qb.orderBy('p.id', 'DESC');
    return qb.getManyAndCount();
  }

  async countProjects(
    filter: ProjectsFilter = {},
    groupBy: string[],
    headers: Record<string, string>,
    request: Record<string, any>,
  ) {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new ForbiddenException();

    const { query, status, userIds } = filter;

    const mQuery = this.projectRepository
      .createQueryBuilder('p')
      .andWhere({ companyId: request?.user?.companyId });
    if (userIds) {
      mQuery
        .leftJoin('p.scopes', 'scopes')
        .andWhere('scopes.user_id IN (:...userIds)', { userIds });
    }
    if (!!query) mQuery.andWhere({ name: ILike(`%${query}%`) });
    if (status) mQuery.andWhere({ status: In(status) });

    mQuery.select('COUNT(DISTINCT(p.id))', 'count');
    for (const group of groupBy ?? []) {
      mQuery.addGroupBy(`p.${group}`);
      mQuery.addSelect(`p.${group}`, group);
    }

    const data = await mQuery.getRawMany();
    for (const item of data) {
      if (!isNil(item.status)) {
        item.status = ProjectStatus[item.status];
      }
      item.count = Number(item.count);
    }
    return data;
  }

  async create(body: CreateProjectDto, request?: Record<string, any>): Promise<Project> {
    const userId = request?.user?.id;
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const oldProject = await this.projectRepository.findOne({
      shortName: body?.shortName,
      companyId,
      deletedAt: IsNull(),
    });
    if (oldProject) {
      throw new BadRequestException(`Dự án ${oldProject.name} đã tồn tại`);
    }
    if (body.leaderId) {
      const mProject = await this.projectRepository.findOne({
        leaderId: body.leaderId,
      });
      if (mProject)
        throw new BadRequestException(
          `Người dùng này đang phụ trách dự án ${mProject.name}. Vui lòng chọn người phụ trách khác`,
        );
    }

    try {
      const data = plainToClass(Project, omit(body, ['markets', 'settings']));
      data.updatedBy = userId;
      data.companyId = companyId;
      if (body.settings && Object.keys(body.settings).length > 0) {
        data.settings = mapKeys(body.settings, (value, key) => snakeCase(key));
      }
      if (body.markets)
        data.markets = plainToInstance(Market, body.markets).map(it => ({
          ...it,
          companyId,
        }));

      const project = await this.projectRepository.save(data);
      await this.clearProjectCache();
      return project;
    } catch (error) {
      if (Number(error.code) === 23505) {
        throw new BadRequestException('Tên viết tắt đã từng tồn tại, vui lòng nhập tên khác');
      } else {
        throw new BadRequestException(error.detail);
      }
    }
  }

  async update(
    id: number,
    body: UpdateProjectDto,
    request?: Record<string, any>,
  ): Promise<Project> {
    const userId = request?.user?.id;
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();
    const oldRecord = await this.projectRepository
      .createQueryBuilder('p')
      .andWhere('p.id = :id', { id })
      .andWhere('p.companyId = :companyId', { companyId })
      .leftJoinAndSelect('p.markets', 'markets')
      .leftJoin('p.scopes', 'scopes')
      .addSelect('scopes.user_id')
      .getOne();
    if (!oldRecord) {
      throw new NotFoundException(`Project id ${id} không tồn tại`);
    }

    if (body.leaderId !== oldRecord.leaderId) {
      const mProject = await this.projectRepository.findOne({
        leaderId: body.leaderId,
      });
      if (mProject && mProject.id !== id)
        throw new BadRequestException(
          `Người dùng này đang phụ trách dự án ${mProject.name}. Vui lòng chọn người phụ trách khác`,
        );
    }

    if (body.markets) {
      const ffmClientIds = body.markets.map(it => it.fulfillmentPartnerClientId);
      const ffmClients = await this.ffmPartnerClientRepo.findByIds(ffmClientIds, {
        relations: ['client'],
      });
      const ffmClientsLookup: Record<number, FulfillmentPartnerClient> = ffmClients.reduce(
        (prev, it) => {
          prev[it.id] = it;
          return prev;
        },
        {},
      );

      for (const item of body.markets) {
        const ffmClient = ffmClientsLookup[item.fulfillmentPartnerClientId];
        if (!ffmClient) continue;

        if (!ffmClient.client?.countries.includes(String(item.countryId)))
          throw new BadRequestException(
            `Client ${ffmClient.client.name} is unavailable in country ${item.countryId}`,
          );
      }
    }

    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const data = plainToClass(Project, {
        ...oldRecord,
        ...omit(body, ['markets', 'userIds', 'settings']),
      });
      data.updatedBy = userId;
      if (body.settings && Object.keys(body.settings).length > 0) {
        data.settings = mapKeys(body.settings, (value, key) => snakeCase(key));
      }
      if (oldRecord.productIds && body.productIds && oldRecord.productIds !== body.productIds) {
        try {
          oldRecord.products = (
            await this.amqpConnection.request({
              exchange: 'catalog-service-products',
              routingKey: 'find-products-by-ids',
              payload: { ids: oldRecord.productIds },
              timeout: 10000,
            })
          ).data as Product[];
          data.products = (
            await this.amqpConnection.request({
              exchange: 'catalog-service-products',
              routingKey: 'find-products-by-ids',
              payload: { ids: data.productIds },
              timeout: 10000,
            })
          ).data as Product[];
        } catch (error) {
          throw error.detail;
        }
      }

      if (!isNil(body?.userIds)) {
        const scopes: UserScope[] = [];
        // await queryRunner.manager
        //   .createQueryBuilder()
        //   .delete()
        //   .from(UsersProjects)
        //   .where('projectId = :id', { id })
        //   .execute();

        // const params = body?.userIds.map((uid: number) =>
        //   plainToInstance(UsersProjects, { projectId: id, userId: uid }),
        // );

        // if (params.length > 0) await queryRunner.manager.save(params);
        // data.users = await queryRunner.manager.findByIds(User, body.userIds, {
        //   select: ['id', 'name'],
        // });
      }

      if (body.markets) {
        const markets: Market[] = [];
        const countryIds: number[] = [];
        for (const market of body.markets) {
          markets.push(plainToInstance(Market, { ...market, projectId: data.id }));
          countryIds.push(market.countryId);
        }
        // const removeMarkets = data.markets.filter(it => !countryIds.includes(it.countryId));
        const mMarkets = [...data.markets, ...markets].reduce((prev: Market[], item) => {
          item.companyId = companyId;
          const index = findIndex(prev, it => it.countryId === item.countryId);
          if (index !== -1) {
            prev[index] = {
              ...prev[index],
              ...pickBy(item, (value, key) => {
                if (key === 'isActivate') {
                  return value !== undefined;
                }
                return value !== undefined;
              }),
            };
          } else prev.push(item);
          return prev;
        }, []);
        data.markets = await queryRunner.manager.save(plainToInstance(Market, mMarkets));
        // await queryRunner.manager.remove(removeMarkets);
        // data.markets = differenceBy(data.markets, removeMarkets, 'countryId');
        // console.log(`data.markets after remove`, data.markets);
      }

      await queryRunner.manager.save(plainToInstance(Project, omit(data, ['markets'])));
      await queryRunner.commitTransaction();
      await this.clearProjectCache();
      await this.amqpConnection.sendMessage<ChangeLogDto<Project>>(
        'identity-service-logs',
        null,
        {
          entity: 'projects',
          before: oldRecord,
          after: data,
          excludeProps: this.projectSubscriber.getUnsubscribeProps(),
          creatorColName: 'updatedBy',
        },
        { routingKey: 'save-change-logs' },
      );

      return data;
    } catch (error) {
      console.log(`update project id ${id} error`, error);
      await queryRunner.rollbackTransaction();
      if (Number(error.code) === 23505) {
        throw new BadRequestException('Tên viết tắt đã được sử dụng cho dự án khác');
      } else {
        throw new BadRequestException(error.detail);
      }
    } finally {
      await queryRunner.release();
    }
  }

  async delete(id: number, request?: Record<string, any>): Promise<UpdateResult> {
    const companyId = request?.user?.companyId;
    if (!companyId) throw new UnauthorizedException();

    const oldProject = await this.projectRepository.findOne({
      where: { id, companyId },
    });
    if (!oldProject) {
      throw new NotFoundException('Project không tồn tại');
    }
    const res = await this.projectRepository.softDelete(oldProject.id);
    await this.clearProjectCache();
    return res;
  }

  async clearProjectCache() {
    return await this.redisCache.delWithPrefix('/projects');
  }

  async getProjectsLogs(
    filter: ProjectsLogsFilter = {},
    pagination?: PaginationOptions,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ) {
    const { projectIds } = filter;
    const sql = this.logsRepo
      .createQueryBuilder('logs')
      .andWhere(`logs.entity = 'projects'`)
      .orderBy('logs.createdAt', 'DESC');
    if (!isEmpty(projectIds)) sql.andWhere('logs.entityId IN (:...projectIds)', { projectIds });
    const logs = await sql.getMany();
    return logs;
  }

  @RabbitRPC({
    exchange: 'identity-service-projects',
    routingKey: 'get-project-by-id',
    queue: 'identity-get-project-by-id',
    errorHandler: defaultNackErrorHandler,
  })
  async getProjectById({ id }) {
    const data = await this.projectRepository.findOne(id);
    return data;
  }

  @RabbitRPC({
    exchange: 'identity-service-projects',
    routingKey: 'get-project-by-ids',
    queue: 'identity-get-project-by-ids',
    errorHandler: defaultNackErrorHandler,
  })
  async getProjectByIds({ ids, companyId }) {
    return this.findByIds(ids, companyId);
  }

  @RabbitRPC({
    exchange: 'identity-service-projects',
    routingKey: 'get-project-by-short-names',
    queue: 'identity-get-project-by-short-names',
    errorHandler: defaultNackErrorHandler,
  })
  async getProjectByShortNames({ names, companyId }) {
    return this.projectRepository.find({
      where: { shortName: In(uniq(names)), companyId },
    });
  }

  async getProjectFromFfm(filter: ProjectFromFfmFilter, headers, request): Promise<Project[]> {
    const { companyId } = request.user;
    const countryId = headers['country-ids']?.split(',')[0];

    const { clientIds, query } = filter;
    const qb = this.projectRepository
      .createQueryBuilder('p')
      .leftJoin('p.markets', 'market')
      .select(['p.id', 'p.name', 'p.shortName'])
      .where('market.fulfillment_partner_id = :companyId', { companyId })
      .andWhere('market.country_id = :countryId', { countryId })
      .unScope('p');

    if (!isEmpty(clientIds)) {
      qb.leftJoin('market.fulfillmentPartnerClient', 'fpc');
      qb.andWhere('fpc.client_id IN (:...clientIds)', { clientIds });
    }

    if (!!query) qb.andWhere({ name: ILike(`%${query}%`) });

    const projects = await qb.getMany();
    return projects;
  }

  async countUSersOfProject(filter: CountUsersOfProjectDto, headers: Record<string, string>) {
    const countryId = headers['country-ids'];
    if (!countryId) {
      throw new BadRequestException('Country ID is required');
    }
    const qb = this.projectRepository
      .createQueryBuilder('p')
      .leftJoin('data_set_scopes', 'dss', 'dss.entity_id = p.id')
      .leftJoin('data_sets', 'ds', 'dss.data_set_id = ds.id')
      .leftJoin('departments', 'd', 'ds.id = d.data_set_id')
      .leftJoin('user_profiles', 'up', 'd.id = up.department_id')
      .select(['p.id AS id', 'COUNT(*) AS "totalUsers"'])
      .where('up.status = :status AND dss.country_id = :countryId', {
        status: CommonStatus.activated,
        countryId,
      });
    if (!isEmpty(filter.projectIds)) {
      qb.andWhere('p.id IN (:...projectIds)', { projectIds: filter.projectIds });
    }
    qb.groupBy('p.id');
    const rs = await qb.getRawMany();
    return rs.map(r => {
      return {
        ...r,
        totalUsers: +r.totalUsers,
      };
    });
  }
}
