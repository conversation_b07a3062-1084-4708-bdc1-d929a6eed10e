import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { identityConnection } from 'core/constants/database-connection.constant';
import { Company } from '../../entities/company.entity';
import { Country } from '../../entities/country.entity';
import { FulfillmentApiKey } from '../../entities/fulfillment-api-key.entity';
import { Logs } from '../../entities/logs.entity';
import { Project } from '../../entities/projects.entity';
import { Roles } from '../../entities/roles.entity';
import { Team } from '../../entities/team.entity';
import { UserBank } from '../../entities/user-bank.entity';
import { UserScope } from '../../entities/user-scope.entity';
import { User } from '../../entities/user.entity';
import { firebaseAppProviders } from '../../firebase/providers/firebase-app.providers';
import { FirebaseAuthService } from '../../firebase/services/firebase-auth/firebase-auth.service';
import { BusinessesModule } from '../business/businesses.module';
import { CompaniesModule } from '../companies/companies.module';
import { CdnController } from './controllers/cdn.controller';
import { PancakeUsersController } from './controllers/pancake-users.controller';
import { PermissionController } from './controllers/permissions.controller';
import { RolesController } from './controllers/roles.controller';
import { UsersController } from './controllers/users.controller';
import { AuthService } from './services/auth.service';
import { PosUsersService } from './services/pos-users.service';
import { RolesService } from './services/roles.service';
import { TelegramService } from './services/telegram.service';
import { UsersService } from './services/users.service';
import { DataSetsController } from './controllers/data-sets.controller';
import { DataSetsService } from './services/data-sets.service';
import { DataSet } from '../../entities/data-set.entity';
import { DepartmentsController } from './controllers/departments.controller';
import { DepartmentsService } from './services/departments.service';
import { Department } from '../../entities/department.entity';
import { UserProfile } from '../../entities/user-profile.entity';
import { DataSetScope } from '../../entities/data-set-scope.entity';
import { SaleRolesController } from './controllers/sale-roles.controller';
import { UserProfilesController } from './controllers/user-profiles.controller';
import { UserProfilesService } from './services/user-profiles.service';
import { SaleRolesService } from './services/sale-roles.service';
import { UserDevice } from '../../entities/user-device.entity';
import { UserDevicesController } from './controllers/user-devices.controller';
import { Business } from 'apps/identity-api/src/entities/business.entity';
import { BullModule } from '@nestjs/bull';
import { IdentityProcessor } from './processor/identity.processor';

@Module({
  imports: [
    RabbitMQModule.externallyConfigured(RabbitMQModule, 0),
    TypeOrmModule.forFeature(
      [
        User,
        Roles,
        Country,
        Project,
        Company,
        Team,
        UserScope,
        Logs,
        FulfillmentApiKey,
        UserBank,
        DataSet,
        DataSetScope,
        Department,
        UserProfile,
        UserDevice,
        Business,
      ],
      identityConnection,
    ),
    PassportModule,
    JwtModule.registerAsync({
      useFactory: () => ({
        secret: process.env.SECRET_KEY_BASE,
        signOptions: {
          expiresIn: process.env.JWT_EXPIRES_IN,
          algorithm: 'HS256',
        },
      }),
    }),
    CompaniesModule,
    BusinessesModule,
    BullModule.registerQueue({ name: 'users' }),
  ],
  controllers: [
    UserDevicesController,
    UsersController,
    RolesController,
    CdnController,
    PermissionController,
    PancakeUsersController,
    DataSetsController,
    DepartmentsController,
    SaleRolesController,
    UserProfilesController,
  ],
  providers: [
    AuthService,
    RolesService,
    ...firebaseAppProviders,
    FirebaseAuthService,
    PosUsersService,
    UsersService,
    TelegramService,
    DataSetsService,
    DepartmentsService,
    UserProfilesService,
    SaleRolesService,
    IdentityProcessor,
  ],
  exports: [RolesService, UsersService],
})
export class AuthModule {}
