import {
  AmqpConnection,
  default<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@golevelup/nestjs-rabbitmq';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { LinkPancakeUserDto } from 'apps/identity-api/src/dtos/pancake-user.dto';
import { plainToClass, plainToInstance } from 'class-transformer';
import { identityConnection } from 'core/constants/database-connection.constant';
import { Brackets, getConnection, In, IsNull, MoreThan, Not, Repository } from 'typeorm';
import { PancakeUser } from '../../../dtos/pancake-order.dto';
import { User } from '../../../entities/user.entity';
import { UserStatus } from 'core/enums/user-status.enum';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import {
  NEW_USER_SCOPES,
  USER,
  USER_ACTIVE,
  USER_ROOT,
  USER_SCOPES,
  USER_SESSION,
  USER_WAREHOUSE,
} from 'core/cache/constants/prefix.constant';
import { CommonStatus } from 'core/enums/common-status.enum';
import { PaginationOptions } from 'core/decorators/pagination/pagination.model';
import { Logs } from 'apps/identity-api/src/entities/logs.entity';
import { UsersLogsFilter } from 'apps/identity-api/src/filters/users-logs.filter';
import { FulfillmentApiKey } from 'apps/identity-api/src/entities/fulfillment-api-key.entity';
import { CreateApiKeyDto } from 'apps/identity-api/src/dtos/create-api-key.dto';
import { UserBank } from 'apps/identity-api/src/entities/user-bank.entity';
import {
  ChangeActiveStatusDto,
  CreateUserBankDto,
  LoginUserAppDto,
  ResetPasswordDto,
  UpdatePasswordAppDto,
  UpdatePasswordDto,
  UpdateUserDto,
  VerifyCodeUpdatePasswordAppDto,
  VerifyCodeUserAppDto,
} from 'apps/identity-api/src/dtos/user.dto';
import * as moment from 'moment-timezone';
import { MailerService } from '@nestjs-modules/mailer';
import OtpUtils from 'core/utils/OtpUtils';
import * as crypto from 'crypto';
import { UserScope } from 'apps/identity-api/src/entities/user-scope.entity';
import { chunk, concat, flattenDeep, isArray, isEmpty, isNil, sum, toPairs } from 'lodash';
import { ClientReportDto } from 'apps/identity-api/src/dtos/client-report.dto';
import { UserType } from 'core/enums/user-type.enum';
import { TelegramService } from './telegram.service';
import { AuthService } from './auth.service';
import { Company } from 'apps/identity-api/src/entities/company.entity';
import { UsersFilter } from 'apps/identity-api/src/filters/users.filter';
import { DataSetScope } from 'apps/identity-api/src/entities/data-set-scope.entity';
import { DataSet } from 'apps/identity-api/src/entities/data-set.entity';
import { UserProfile } from 'apps/identity-api/src/entities/user-profile.entity';
import { Department } from 'apps/identity-api/src/entities/department.entity';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';
import { UserDevice } from '../../../entities/user-device.entity';
import * as QRCode from 'qrcode';
import { CreateDeviceDto, MakeCallDto } from '../../../dtos/device.dto';
import { UserDevicesFilter } from '../../../filters/user-devices.filter';
import { getRequestContext } from '../../../../../../core/hooks/request-context.hook';
import { FirebaseAuthService } from '../../../firebase/services/firebase-auth/firebase-auth.service';
import { UserDeviceStateEnum } from '../../../enums/user-device-state.enum';
import { UpdateUserDeviceDto } from '../../../dtos/update-user-device.dto';
import { UpdateMultipleOrdersDto } from '../../../../../order-api/src/dtos/update-multiple-orders.dto';
import { UpdateOrderDto } from '../../../../../order-api/src/dtos/update-order.dto';
import { SourceEntity } from '../../../../../order-api/src/enums/source-entity.enum';
import { UpdateMultipleUserDto } from '../../../dtos/update-mutilple-user.dto';
import { AuthUser } from 'core/interfaces/auth-user.interface';
import ExcelUtils from 'core/utils/ExcelUtils';
import StringUtils from 'core/utils/StringUtils';
import {
  BulkCreateAccountsDto,
  CreateAccountDto,
} from 'apps/identity-api/src/dtos/bulk-create-accounts.dto';
import { ErrorCode } from 'apps/identity-api/src/enums/error-code.enum';
import { Business } from 'apps/identity-api/src/entities/business.entity';
import { AuthService as AuthCoreService } from 'core/auth/services/auth.service';
import { BusinessType } from 'apps/identity-api/src/enums/business-type.enum';
import { GetDescendantsDto } from 'apps/identity-api/src/dtos/get-descendants.dto';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bullmq';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User, identityConnection)
    private userRepository: Repository<User>,
    @InjectRepository(UserProfile, identityConnection)
    private userProfileRepository: Repository<UserProfile>,
    @InjectRepository(Department, identityConnection)
    private departmentsRepository: Repository<Department>,
    @InjectRepository(UserScope, identityConnection)
    private userScopesRepo: Repository<UserScope>,
    @InjectRepository(DataSetScope, identityConnection)
    private dataSetScopesRepo: Repository<DataSetScope>,
    @InjectRepository(UserBank, identityConnection)
    private userBankRepository: Repository<UserBank>,
    @InjectRepository(FulfillmentApiKey, identityConnection)
    private apiKeyRepository: Repository<FulfillmentApiKey>,
    @InjectRepository(UserDevice, identityConnection)
    private userDeviceRepository: Repository<UserDevice>,
    @InjectRepository(Logs, identityConnection)
    private logsRepo: Repository<Logs>,
    @InjectRepository(Business, identityConnection)
    private businessRepo: Repository<Business>,
    private redisCache: RedisCacheService,
    private amqpConnection: AmqpConnection,
    private readonly mailerService: MailerService,
    private telegramService: TelegramService,
    private authService: AuthService,
    private authCoreService: AuthCoreService,
    private firebaseAuthService: FirebaseAuthService,
    @InjectRedis()
    private redis: Redis,
    @InjectQueue('users')
    private userQueue: Queue,
  ) {}

  onModuleInit() {
    this.checkLastActiveOfUsers();
  }

  async createFromPancakeUser(data: PancakeUser): Promise<User> {
    const { name, id, fb_id, avatar_url, phone_number } = data;

    const user = await this.userRepository.findOne({
      where: { pancakeId: id },
    });
    if (user) return user;

    return await this.userRepository.save(
      plainToInstance(User, {
        avatar: avatar_url,
        pancakeId: id,
        name,
        phone: phone_number,
      }),
    );
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'add-client-category',
    queue: 'identity-add-client-category',
    errorHandler: defaultNackErrorHandler,
  })
  async addClientCategory({ id, categoryId }) {
    if (!id) {
      return null;
    }
    const key = `${USER}.${id}.`;
    // const data = await this.redisCache.get(key);
    // if (data) {
    //   return data;
    // }
    const user = await this.userRepository.findOne({ id }, { relations: ['role'] });
    const categoryIds = user?.categoryIds ? user?.categoryIds : [];
    if (!categoryIds.includes(categoryId))
      await this.userRepository.update({ id }, { categoryIds: [...categoryIds, categoryId] });
    await this.redisCache.set(key, user);
    return user;
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'get-user',
    queue: 'identity-get-user',
    errorHandler: defaultNackErrorHandler,
  })
  async getUser({ id }) {
    if (!id) {
      return null;
    }
    const key = `${USER}.${id}.`;
    // const data = await this.redisCache.get(key);
    // if (data) {
    //   return data;
    // }
    const user = await this.userRepository.findOne({ id }, { relations: ['role'] });
    await this.redisCache.set(key, user);
    return user;
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'find-user-by-display',
    queue: 'identity-find-user-by-display',
    errorHandler: defaultNackErrorHandler,
  })
  async getUserByDisplay({ displayId }) {
    if (!displayId) {
      return null;
    }
    const key = `${USER}.${displayId}.`;
    const user = await this.userRepository.findOne({ displayId });
    await this.redisCache.set(key, user);
    return user;
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'check-active',
    queue: 'identity-check-active',
    errorHandler: defaultNackErrorHandler,
  })
  async checkActive({ id, sid }) {
    if (!id || `${Number(id)}` != `${id}`) {
      return 0;
    }

    const key = `${USER_ACTIVE}.${id}.`;
    const skey = `${USER_SESSION}.${id}.`;

    let data = await this.redisCache.get(key);
    const sdata = await this.redisCache.get(skey);
    if (data && sdata && sdata == sid) {
      return data;
    }

    const user = await this.userRepository.findOne(id, {
      relations: ['company'],
      select: ['status', 'company', 'type', 'sessionId'],
    });
    if (!sid || !user.sessionId || user.sessionId != sid) {
      return -1;
    }

    data =
      user?.type === UserType.admin ||
      (user?.status === UserStatus.active && user?.company?.status === CommonStatus.activated)
        ? 1
        : 0;

    await this.redisCache.set(key, data);
    await this.redisCache.set(skey, user.sessionId || '');
    return data;
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'check-new-scopes',
    queue: 'identity-check-new-scopes',
    errorHandler: defaultNackErrorHandler,
  })
  async checkNewScopes({ id }) {
    if (!id) return new Nack(false);

    const key = `${USER}.${id}`;
    let data: string = await this.redis.hget(key, NEW_USER_SCOPES);
    if (data) {
      try {
        const minified = JSON.parse(data);
        return minified;
      } catch (error) {
        console.log(`parse cached user scopes data err`, error);
      }
      return data;
    }

    const scopes = await this.dataSetScopesRepo
      .createQueryBuilder('scopes')
      .leftJoin(Department, 'd', 'd.dataSetId = scopes.dataSetId')
      .unScope('d')
      .innerJoin(
        UserProfile,
        'profile',
        'profile.dataSetId = scopes.dataSetId OR (d.id = profile.departmentId AND profile.dataSetId IS NULL)',
      )
      .where('profile.userId = :id')
      .setParameters({ id })
      .getMany();
    const minified = scopes.map(sc => [sc.entityId, sc.countryId]);
    data = JSON.stringify(minified);
    await this.redis
      .multi()
      .hset(key, NEW_USER_SCOPES, data)
      .expire(key, 5 * 60)
      .exec();
    return minified;
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'check-scopes',
    queue: 'identity-check-scopes',
    errorHandler: defaultNackErrorHandler,
  })
  async checkScopes({ id }) {
    if (!id) return new Nack(false);

    const key = `${USER_SCOPES}.${id}.`;
    let data: string = await this.redisCache.get(key);
    if (data) {
      try {
        const minified = JSON.parse(data);
        return minified;
      } catch (error) {
        console.log(`parse cached user scopes data err`, error);
      }
      return data;
    }

    const scopes = await this.userScopesRepo
      .createQueryBuilder('scope')
      .where('scope.userId = :id')
      .setParameters({ id })
      .getMany();
    const minified = scopes.map(sc => [sc.projectId, sc.countryIds]);
    data = JSON.stringify(minified);
    await this.redisCache.set(key, data);
    return minified;
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'check-warehouse',
    queue: 'identity-check-warehouse',
    errorHandler: defaultNackErrorHandler,
  })
  async checkWarehouse({ id }) {
    if (!id || `${Number(id)}` != `${id}`) {
      return [];
    }

    const key = `${USER_WAREHOUSE}.${id}.`;
    const data = await this.redisCache.get(key);

    if (concat(data ?? [], [])?.length > 0) {
      return data;
    }

    const user = await this.userRepository.findOne(id);
    await this.redisCache.set(key, user?.warehouses ?? []);
    return user?.warehouses ?? [];
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'check-root',
    queue: 'identity-check-root',
  })
  async checkRoot({ id }) {
    if (!id) return 0;

    const key = `${USER_ROOT}.${id}.`;
    let data = await this.redisCache.get(key);
    if (data) return data;
    const user = await this.userRepository.findOne({ id }, { select: ['status', 'type'] });
    data = user?.type === UserType.admin ? 1 : 0;
    await this.redisCache.set(key, data);
    return data;
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'check-client-web-hook',
    queue: 'identity-check-client-web-hook',
  })
  async checkClientWebhook(payload) {
    if (!payload?.clientId) return new Nack();

    const apiKey = await this.apiKeyRepository.findOne({
      clientId: payload?.clientId,
    });

    if (!apiKey) return new Nack();
    return apiKey;
  }

  async linkPancakeUser(data: LinkPancakeUserDto): Promise<User> {
    const { userId, pancakeId } = data;

    const user = await this.userRepository.findOne(userId, {
      where: {
        pancakeId: IsNull(),
        password: Not(IsNull()),
      },
    });
    if (!user) throw new BadRequestException('Người dùng được chọn để liên kết không hợp lệ');

    const pancakeUser = await this.userRepository.findOne({
      where: { pancakeId, password: IsNull(), status: UserStatus.pending },
    });

    if (!pancakeUser) throw new BadRequestException('Không tìm thấy người dùng pancake');

    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      await queryRunner.manager.delete(User, { pancakeId });
      await queryRunner.manager.update(User, { id: user.id }, { pancakeId });
      await queryRunner.commitTransaction();
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }

    user.pancakeId = pancakeId;

    this.amqpConnection.sendMessage('IdentityService.Users.OnPosUsersLinked', null, {
      beforeUserId: pancakeUser.id,
      afterLinkedUserId: user.id,
    });

    return user;
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'get-users-by-filter',
    queue: 'identity-get-users-by-filter',
    errorHandler: defaultNackErrorHandler,
  })
  async getUserByFilters(payload: { filter: UsersFilter; pagination?: PaginationOptions }) {
    const { filter, pagination } = payload;
    const qb = this.userRepository
      .createQueryBuilder('u')
      // .leftJoinAndSelect('u.scopes', 'scopes')
      // .leftJoinAndSelect('scopes.project', 'project')
      .leftJoin('u.profiles', 'profiles', `profiles.status = ${CommonStatus.activated}`)
      .unScope('profiles')
      .addSelect('profiles.id')
      .leftJoin('profiles.role', 'profileRole')
      .addSelect([
        'profileRole.id',
        'profileRole.name',
        'profileRole.status',
        'profileRole.moduleInCharge',
        'profileRole.dataAccessLevel',
      ]);
    if (pagination) qb.take(pagination.limit).skip(pagination.skip);
    const {
      query,
      companyIds,
      teamIds,
      projectIds,
      isOnline,
      ids,
      status,
      countryIds,
      role,
    } = filter;

    if (role) {
      qb.innerJoin('u.role', 'role', 'CAST(role.permission as BIGINT) & :role > 0', {
        role: `${sum(role.map(i => BigInt(i)))}`,
      });
    }

    if (query) {
      qb.andWhere(
        new Brackets(qb => {
          qb.where('cast(u.id as text) ILIKE :userId', {
            userId: `%${query}%`,
          })
            .orWhere('u.name ILIKE :name', { name: `%${query}%` })
            .orWhere('u.displayId ILIKE :name', { name: `%${query}%` })
            .orWhere('u.phone ILIKE :name', { phone: `%${query}%` })
            .orWhere('u.email ILIKE :name', { email: `%${query}%` });
        }),
      );
    }
    if (companyIds) {
      qb.andWhere('u.companyId IN (:...companyIds)', { companyIds });
    }
    if (!!teamIds) {
      qb.leftJoin('u.teams', 'teams');
      qb.andWhere('teams.id IN (:...teamIds)', { teamIds });
    }
    if (!isEmpty(projectIds) && !isEmpty(countryIds)) {
      const countryId = countryIds[0];
      const scopePairs = projectIds.map(pid => `('${countryId}', '${pid}')`);
      qb.leftJoin('profiles.department', 'd', 'profiles.dataSetId IS NULL')
        .innerJoin(
          DataSet,
          'dataSet',
          'dataSet.id = profiles.dataSetId OR dataSet.id = d.dataSetId',
        )
        .innerJoin('dataSet.scopes', 'scopes')
        .andWhere(`(scopes.country_id, scopes.entity_id) IN (${scopePairs.join(', ')})`)
        .addSelect(['scopes.entityId']);
    }
    if (ids) qb.andWhere('u.id IN (:...ids)', { ids });
    if (!isNil(isOnline)) qb.andWhere('u.isOnline = :isOnline', { isOnline });
    if (status) qb.andWhere('u.status IN (:...status)', { status });
    // if (!!countryIds)
    //   qb.andWhere('(scopes.country_ids && :countryIds OR scopes.id IS NULL)', {
    //     countryIds,
    //   });

    const [users, usersRaw] = await Promise.all([qb.getMany(), qb.getRawMany()]);

    users.forEach(user => {
      if (!user.projectIds) user.projectIds = [];
      user.profiles.forEach(profile => {
        usersRaw.forEach(u => {
          if (profile.id === u.profiles_id) user.projectIds.push(u.scopes_entity_id);
        });
      });
    });

    return users;
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'get-users-by-filter-with-count',
    queue: 'identity-get-users-by-filter-with-count',
    errorHandler: defaultNackErrorHandler,
  })
  async getUserByFiltersWithCount(payload: {
    filter: UsersFilter;
    pagination?: PaginationOptions;
  }) {
    const { filter, pagination } = payload;
    const qb = this.userRepository
      .createQueryBuilder('u')
      // .leftJoinAndSelect('u.scopes', 'scopes')
      // .leftJoinAndSelect('scopes.project', 'project')
      .leftJoin('u.profiles', 'profiles', `profiles.status = ${CommonStatus.activated}`)
      .unScope('profiles')
      .addSelect('profiles.id')
      .leftJoin('profiles.role', 'profileRole')
      .addSelect([
        'profileRole.id',
        'profileRole.name',
        'profileRole.status',
        'profileRole.moduleInCharge',
        'profileRole.dataAccessLevel',
      ]);
    if (pagination) qb.take(pagination.limit).skip(pagination.skip);
    const {
      query,
      companyIds,
      teamIds,
      projectIds,
      isOnline,
      ids,
      status,
      countryIds,
      role,
    } = filter;

    if (role) {
      qb.innerJoin('u.role', 'role', 'CAST(role.permission as BIGINT) & :role > 0', {
        role: `${sum(role.map(i => BigInt(i)))}`,
      });
    }

    if (query) {
      qb.andWhere(
        new Brackets(qb => {
          qb.where('cast(u.id as text) ILIKE :userId', {
            userId: `%${query}%`,
          })
            .orWhere('u.name ILIKE :name', { name: `%${query}%` })
            .orWhere('u.displayId ILIKE :name', { name: `%${query}%` })
            .orWhere('u.phone ILIKE :name', { phone: `%${query}%` })
            .orWhere('u.email ILIKE :name', { email: `%${query}%` });
        }),
      );
    }
    if (companyIds) {
      qb.andWhere('u.companyId IN (:...companyIds)', { companyIds });
    }
    if (!!teamIds) {
      qb.leftJoin('u.teams', 'teams');
      qb.andWhere('teams.id IN (:...teamIds)', { teamIds });
    }
    if (!isEmpty(projectIds) && !isEmpty(countryIds)) {
      const countryId = countryIds[0];
      const scopePairs = projectIds.map(pid => `('${countryId}', '${pid}')`);
      qb.leftJoin('profiles.department', 'd', 'profiles.dataSetId IS NULL')
        .innerJoin(
          DataSet,
          'dataSet',
          'dataSet.id = profiles.dataSetId OR dataSet.id = d.dataSetId',
        )
        .innerJoin('dataSet.scopes', 'scopes')
        .andWhere(`(scopes.country_id, scopes.entity_id) IN (${scopePairs.join(', ')})`)
        .addSelect(['scopes.entityId']);
    }
    if (ids) qb.andWhere('u.id IN (:...ids)', { ids });
    if (!isNil(isOnline)) qb.andWhere('u.isOnline = :isOnline', { isOnline });
    if (status) qb.andWhere('u.status IN (:...status)', { status });
    // if (!!countryIds)
    //   qb.andWhere('(scopes.country_ids && :countryIds OR scopes.id IS NULL)', {
    //     countryIds,
    //   });

    const [[users, count], usersRaw] = await Promise.all([qb.getManyAndCount(), qb.getRawMany()]);

    users.forEach(user => {
      if (!user.projectIds) user.projectIds = [];
      user.profiles.forEach(profile => {
        usersRaw.forEach(u => {
          if (profile.id === u.profiles_id) user.projectIds.push(u.scopes_entity_id);
        });
      });
    });
    return {
      users,
      count,
    };
  }

  @RabbitRPC({
    exchange: 'identity-service-roles',
    routingKey: 'get-users-by-ids',
    queue: 'identity-get-users-by-ids',
    errorHandler: defaultNackErrorHandler,
  })
  async getUsers({ ids, select }: { ids: number[]; select: string[] }) {
    const qb = this.userRepository.createQueryBuilder('u').whereInIds(ids);
    if (!isEmpty(select)) {
      for (const [index, selectKey] of Object.entries(select)) {
        if (Number(index) === 0) qb.select(`u.${selectKey}`);
        else qb.addSelect(`u.${selectKey}`);
      }
    }
    const user = await qb.getMany();
    return user;
  }

  async getUsersLogs(
    filter: UsersLogsFilter = {},
    pagination?: PaginationOptions,
    headers?: Record<string, any>,
    request?: Record<string, any>,
  ): Promise<[Logs[], number]> {
    const { userIds } = filter;
    const companyId = request?.user?.companyId;
    const qb = this.logsRepo
      .createQueryBuilder('logs')
      .leftJoinAndMapOne(
        'logs.userScope',
        UserScope,
        'scope',
        `(logs.tableName = 'user_scopes' AND logs.recordId = scope.id::TEXT)`,
      )
      .andWhere(
        new Brackets(subQb => {
          subQb.where(`logs.tableName = 'users'`);
          if (userIds)
            subQb.andWhere(`cast(logs.recordId as int) IN (:...userIds)`, {
              userIds,
            });
        }),
      )
      .orWhere(
        new Brackets(subQb => {
          subQb.where(`logs.parentTableName = 'users'`);
          if (userIds)
            subQb.andWhere(`cast(logs.parentId as int) IN (:...userIds)`, {
              userIds,
            });
        }),
      )
      .orderBy('logs.createdAt', 'DESC');
    if (pagination) {
      qb.take(pagination.limit).skip(pagination.skip);
    }
    if (companyId) {
      qb.leftJoin(
        User,
        'users',
        '(users.id::TEXT = logs.recordId OR users.id::TEXT = logs.parent_id)',
      ).andWhere('users.companyId = :companyId', { companyId });
    }
    return await qb.getManyAndCount();
  }

  async getApiKey(request: Record<string, any>): Promise<FulfillmentApiKey> {
    return await this.apiKeyRepository.findOne({
      clientId: request?.user?.id,
    });
  }

  async checkApiKey(data: CreateApiKeyDto): Promise<FulfillmentApiKey> {
    return await this.apiKeyRepository.findOne({
      clientId: data?.clientId,
      apiKey: data?.apiKey,
    });
  }

  async createApiKey(request: Record<string, any>): Promise<FulfillmentApiKey> {
    const user = await this.userRepository.findOne({
      id: request?.user?.id,
      type: UserType.customer,
    });

    if (!user) throw new NotFoundException();

    await this.apiKeyRepository.delete({
      clientId: user?.id,
    });

    const payload = {
      id: user?.id,
      companyId: user?.companyId,
    };

    return await this.apiKeyRepository.save({
      clientId: user?.id,
      apiKey: crypto.randomUUID(),
    });
  }

  async updateApiKey(
    data: CreateApiKeyDto,
    request: Record<string, any>,
  ): Promise<FulfillmentApiKey> {
    const user = await this.userRepository.findOne({
      id: request?.user?.id,
      type: UserType.customer,
    });

    if (!user) throw new NotFoundException();
    const apiKey = await this.checkApiKey(data);
    if (!apiKey) throw new NotFoundException();

    if (data?.webHook) apiKey.webHook = data?.webHook;
    return await this.apiKeyRepository.save({ ...apiKey, webHook: data?.webHook });
  }

  async getAccountBanks(id: string): Promise<UserBank[]> {
    return await this.userBankRepository.find({ userId: id });
  }

  async createAccountBank(id: string, data: CreateUserBankDto): Promise<UserBank> {
    const param = plainToClass(UserBank, data);
    param.userId = id;
    param.isDefault = false;

    return this.userBankRepository.save(param);
  }

  async updateAccountBank(id: number, uid: string, data: CreateUserBankDto): Promise<UserBank> {
    const bank = await this.userBankRepository.findOne({ id });
    if (!bank) {
      throw new BadRequestException('Not found account bank !');
    }

    const key = `${USER}.OTP.${uid}`;
    const otp: any = await this.redisCache.get(key);

    console.log(key, otp, data);

    if (
      !data?.otp ||
      !otp ||
      !otp?.expired ||
      data?.otp != otp?.otp ||
      otp?.expired < moment().valueOf()
    )
      throw new BadRequestException('Expired update account bank !');

    if (!!data?.isDefault) {
      await this.userBankRepository.update(
        {
          userId: uid,
        },
        {
          isDefault: false,
        },
      );
    }

    const param = plainToClass(UserBank, {
      ...bank,
      ...data,
    });

    return this.userBankRepository.save(param);
  }

  async deleteAccountBank(id: number, filters: any): Promise<any> {
    const old = await this.userBankRepository.findOne({ id });
    if (!old) {
      throw new BadRequestException('Not found account bank !');
    }
    if (old?.isDefault)
      throw new BadRequestException('Sorry. This account bank is default active !');

    const key = `${USER}.OTP.${id}`;
    const otp: any = await this.redisCache.get(key);

    if (
      !filters?.otp ||
      !otp ||
      !otp?.expired ||
      filters?.otp != otp?.otp ||
      otp?.expired < moment().valueOf()
    )
      throw new BadRequestException('Expired update account bank !');

    await this.userBankRepository.softDelete(id);
    return true;
  }

  async genOtpRequest(id: number): Promise<any> {
    const user = await this.userRepository.findOne({ id });

    if (!user) throw new BadRequestException('Bad request !');

    const key = `${USER}.OTP.${id}`;
    let otp: any = await this.redisCache.get(key);
    const expired = process.env.OTP_EXPIRED ?? 5;

    if (!otp || moment().valueOf() > otp.expired) {
      const newOtp = OtpUtils.generateOTP();
      otp = {
        otp: newOtp,
        expired: moment()
          .add(expired, 'minutes')
          .valueOf(),
      };
      await this.redisCache.set(key, otp);
    }

    await this.mailerService
      .sendMail({
        to: user?.email,
        template: 'otp',
        subject: 'PFG - OTP',
        context: {
          name: user?.name,
          otp: otp?.otp,
          expired: expired + ' minutes',
        },
      })
      .then(e => {
        console.log(e);
      })
      .catch(e => {
        console.log(e);
        throw new BadRequestException(`Can't sent email`);
      });

    return expired;
  }

  encodePassWord(password: string): string {
    return crypto
      .createHmac('sha256', process.env.PASSWORD_SECRET)
      .update(password)
      .digest('base64');
  }

  async updatePassword(id: string, data: UpdatePasswordDto) {
    const user = await this.userRepository.findOne(id);
    if (!user || data?.password != data?.rePassword)
      throw new BadRequestException('User not found');

    if (this.encodePassWord(data?.oldPassword) != user?.password)
      throw new BadRequestException('Old password is incorrect');
    user.updatedAt = new Date();
    user.password = this.encodePassWord(data?.password);
    const sid = this.authService.randomStr(32);
    const skey = `${USER_SESSION}.${user.id}.`;

    user.sessionId = sid;
    await this.redisCache.set(skey, sid);
    return this.userRepository.save(user);
  }

  async clientReport(context?: ClientReportDto): Promise<any> {
    await this.telegramService.reportBug(context);
    // await this.mailerService
    // .sendMail({
    //   to: context.fromAG ? '<EMAIL>' : '<EMAIL>',
    //   template: 'client-report',
    //   subject: 'Athena Group - Client Report',
    //   context,
    // })
    // .then(e => {
    // })
    // .catch(e => {
    //   console.log('sent mail error', StringUtils.getString(e));
    //   throw new BadRequestException(`Can't sent email `);
    // });
    return 'Sent email successfully';
  }

  async forgetPassword(data, request?: Record<string, any>): Promise<User> {
    if (!data?.email) throw new BadRequestException('Email không được để trống');
    let user = new User();

    if (!request?.user?.companyId) {
      const hostName = this.authService.getHostName(request);
      user = await this.userRepository
        .createQueryBuilder('u')
        .leftJoin(Company, 'c', 'u.companyId = c.id')
        .andWhere('LOWER(u.email) = :email', { email: data?.email })
        .andWhere('u.status = :status', { status: UserStatus.active })
        .andWhere(":hostName = ANY(string_to_array(c.domain_name, ','))", {
          hostName,
        })
        .getOne();
    } else {
      user = await this.userRepository.findOne({
        email: data?.email,
        companyId: request?.user?.companyId,
        status: UserStatus.active,
      });
    }
    if (!user?.id) throw new BadRequestException('Email không tồn tại hoặc tài khoản đã bị khoá');

    const code = this.encodePassWord(user?.id + '|' + moment().valueOf());

    await this.mailerService
      .sendMail({
        to: user?.email,
        template: 'reset-password',
        subject: 'Athena Group - Reset password',
        context: {
          name: user?.name,
          url: request.headers.origin + '/auth/forgot?code=' + code,
        },
      })
      .then(async e => {
        // console.log(e);
        await this.userRepository.update(
          {
            id: user?.id,
          },
          {
            reset: {
              code,
              expiryDate: moment().add('1', 'h'),
            },
          },
        );
      })
      .catch(e => {
        console.log(e);
        throw new BadRequestException(`Can't sent email to ${data.email}`);
      });
    return user;
  }

  async resetPassword(data: ResetPasswordDto) {
    if (data?.password != data?.rePassword)
      throw new BadRequestException('Password verify wrong !');

    const user = await this.userRepository
      .createQueryBuilder()
      .where('reset @> :reset', {
        reset: { code: data?.code },
      })
      .getOne();

    if (!user) throw new BadRequestException('Verify code expired');

    if (moment().isAfter(moment(Object(user?.reset)?.expiryDate)))
      throw new BadRequestException('Expired action !');

    const hashPassWord = this.encodePassWord(data?.password);

    user.updatedAt = new Date();
    user.password = hashPassWord;
    user.reset = null;
    const sid = this.authService.randomStr(32);
    const skey = `${USER_SESSION}.${user.id}.`;

    user.sessionId = sid;
    await this.redisCache.set(skey, sid);

    return this.userRepository.save(user);
  }

  async changeActiveStatusMobile(body: ChangeActiveStatusDto, id: number): Promise<User> {
    const user = await this.userRepository.findOne({ id });
    if (!user) throw new BadRequestException('Nguời dùng không tồn tại!');

    const status = body?.isOnline === true ? 'Online' : 'Offline';
    if (user?.isOnline === body?.isOnline)
      throw new BadRequestException(`Tài khoản đang ở trang thái ${status}!`);

    user.isOnline = body?.isOnline;
    user.updatedBy = id;

    const result = await this.userRepository.save(user);
    return result;
  }

  async changeActiveStatus(
    body: ChangeActiveStatusDto,
    id: number,
    updatedBy?: number,
  ): Promise<User> {
    const user = await this.userRepository.findOne({ id });
    if (!user) throw new BadRequestException('Nguời dùng không tồn tại!');

    const status = body?.isOnline === true ? 'Online' : 'Offline';
    if (user?.isOnline === body?.isOnline)
      throw new BadRequestException(`Tài khoản đang ở trang thái ${status}!`);
    if (body?.isOnline) {
      await this.redis.set(`last-active-of-user:${user.id}`, new Date().getTime());
    }
    user.isOnline = body?.isOnline;
    user.updatedBy = updatedBy;

    const result = await this.userRepository.save(user);
    return result;
  }

  async fetchCategory(): // body: ChangeActiveStatusDto,
  // id: number,
  // updatedBy?: number,
  Promise<any> {
    const users = await this.userRepository
      .createQueryBuilder('u')
      .where('industry is not null')
      .getMany();

    if (isEmpty(users)) return new Nack();

    const categories: any = await this.amqpConnection.request({
      exchange: 'ffm-catalog-service',
      routingKey: 'ffm-find-category',
      payload: {},
      timeout: 10000,
    });

    for (const item of users) {
      const categoryIds = categories?.data
        ?.filter(x => item?.industry.includes(x?.name))
        .map(x => x?.id);
      item.categoryIds = categoryIds;
    }

    await this.userRepository.save(users);
    return users;
  }

  // async getProfileCount(filter: UserProfilesFilter) {
  //   const { userIds } = filter;
  //   const qb = this.userProfileRepo.createQueryBuilder('p');
  //   qb.leftJoinAndSelect('p.user', 'u');
  //   if (!isEmpty(userIds)) qb.andWhere('u.id IN (:...userIds)', { userIds });
  //   qb.select('u.id', 'userId')
  //     .addSelect('count(p.id)')
  //     .addGroupBy('u.id');
  //   return qb.getRawMany();
  // }

  async getQrCode(
    userId: number,
    companyId: number,
  ): Promise<{ code: string; secret: string; image: string }> {
    const code = crypto.randomBytes(12).toString('hex');
    const key = `code.qr.${code}`;
    const current = await this.redis.get(key);
    if (current) {
      return this.getQrCode(userId, companyId);
    }
    const secret = crypto.randomBytes(20).toString('hex');
    await this.redis.set(key, JSON.stringify([userId, companyId, secret]), 'EX', 120);
    const qr = await QRCode.toDataURL(`${code}|${secret}`);
    return {
      code,
      secret,
      image: qr,
    };
  }

  async callDevice(userId: number, id: string, body: MakeCallDto) {
    await this.amqpConnection.publish('order-service', 'save-log-call-action', {
      userId: userId,
      leadId: body.leadId,
      phoneNumber: body.phoneNumber,
    });
    const device = await this.userDeviceRepository.findOne({
      where: {
        userId,
        id,
        pingAt: MoreThan(new Date(Date.now() - 20000)),
        status: UserDeviceStateEnum.CALL_STATE_IDLE,
      },
      select: ['token', 'simSlot'],
    });
    if (!device?.token) {
      throw new BadRequestException('Thiết bị không khả dụng');
    }

    if (!isNil(body.simSlot) && (isEmpty(device.simSlot) || isNil(device.simSlot[body.simSlot]))) {
      throw new BadRequestException('Sim không khả dụng');
    }

    const res = await this.firebaseAuthService.sendMessage(device.token, {
      data: {
        leadId: String(body.leadId),
        phoneNumber: body.phoneNumber,
        simSlot: !isNil(body.simSlot) ? String(body.simSlot) : undefined,
      },
      // notification: {
      //   title: 'Thực hiện cuộc gọi',
      // },
      // android: {
      //   priority: 'high',
      //   notification: {
      //     priority: 'max',
      //     tag: 'call',
      //     sticky: false,
      //     defaultVibrateTimings: true,
      //   },
      // },
    });

    return res;
  }

  async pingDevice(userId: number, id: string, body: UpdateUserDeviceDto) {
    const res = await this.userDeviceRepository.update(
      {
        userId,
        id,
      },
      {
        pingAt: new Date(),
        status: body.state,
        simSlot: body.simSlot,
      },
    );
    return res.affected;
  }

  async addDevice(data: CreateDeviceDto) {
    const { code, secret, token } = data;
    const key = `code.qr.${code}`;
    const current = await this.redis.get(key);
    if (!current) {
      throw new BadRequestException('Mã đăng nhập đã hết hạn');
    }
    const [userId, , secretCode] = JSON.parse(current);
    if (secret !== secretCode) {
      throw new BadRequestException('Yêu cầu không hợp lệ');
    }
    const deviceKey = `code.device.${code}`;
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['name', 'avatar'],
    });
    await this.redis.set(deviceKey, JSON.stringify(data), 'EX', 120);
    return { user };
  }

  async waitForQr(code: string, userId: number) {
    const key = `code.qr.${code}`;
    const deviceKey = `code.device.${code}`;
    while (1) {
      const current = await this.redis.get(key);
      if (!current) {
        throw new BadRequestException('Mã đăng nhập đã hết hạn');
      }
      const [saveUserId] = JSON.parse(current);
      if (userId !== saveUserId) {
        throw new BadRequestException('Yêu cầu không hợp lệ');
      }
      const device = await this.redis.get(deviceKey);
      if (device) {
        return { device: JSON.parse(device) };
      }
      await global.sleep(1000);
    }
  }

  async approvalDevice(code: string, userId: number) {
    const key = `code.qr.${code}`;
    const deviceKey = `code.device.${code}`;
    const current = await this.redis.get(key);
    if (!current) {
      throw new BadRequestException('Mã đăng nhập đã hết hạn');
    }
    const [saveUserId, companyId] = JSON.parse(current);
    if (userId !== saveUserId) {
      throw new BadRequestException('Yêu cầu không hợp lệ');
    }
    const device = await this.redis.get(deviceKey);
    if (!device) {
      throw new BadRequestException('Đăng nhập đã hết hạn');
    }
    const { imei, name, token }: CreateDeviceDto = JSON.parse(device);
    return this.userDeviceRepository.save({
      id: imei,
      name,
      userId,
      code,
      token,
      lastLoginAt: new Date(),
      companyId,
      deletedAt: null,
    } as UserDevice);
  }

  async waitApproval(data: CreateDeviceDto) {
    const { code, secret, imei } = data;
    const key = `code.qr.${code}`;
    const deviceKey = `code.device.${code}`;
    while (1) {
      const current = await this.redis.get(key);
      if (!current) {
        throw new BadRequestException('Mã đăng nhập đã hết hạn');
      }
      const [userId, , secretKey] = JSON.parse(current);
      if (secret !== secretKey) {
        throw new BadRequestException('Yêu cầu không hợp lệ');
      }
      const pendingDevice = await this.redis.get(deviceKey);
      if (!pendingDevice) {
        throw new BadRequestException('Yêu cầu không hợp lệ');
      }
      const device = await this.userDeviceRepository.findOne({
        where: { id: imei, userId, code },
        withDeleted: true,
      });
      if (device) {
        await this.redis.del(key);
        await this.redis.del(deviceKey);
        const user = await this.authService.findById(userId);
        user.token = await this.authService.getJwtToken(user);
        return user;
      }
      await global.sleep(1000);
    }
  }

  async getDevices(filters: UserDevicesFilter) {
    const { userIds, status, query } = filters;
    const { companyId } = getRequestContext().data.req.user;
    const qb = this.userDeviceRepository
      .createQueryBuilder('ud')
      .leftJoinAndSelect('ud.user', 'u', 'u.company_id = :companyId', { companyId });
    if (!isEmpty(userIds)) {
      qb.andWhere('u.id IN (:...userIds)', { userIds });
    }
    if (!isNil(status)) {
      qb.andWhere('ud.status = :status', { status });
    }
    if (!isEmpty(query)) {
      qb.andWhere('ud.name ILIKE :query', { query: `%${query}%` });
    }
    qb.orderBy('ud.updatedAt');
    return qb.getManyAndCount();
  }

  async getDevice(deviceId: string) {
    return this.userDeviceRepository.findOne(deviceId);
  }

  async removeDevice(deviceId: string, request: Record<string, any>) {
    const record = await this.getDevice(deviceId);
    if (!record) throw new NotFoundException();

    const result = await this.userDeviceRepository.update(
      { id: deviceId },
      { deletedAt: new Date(), updatedBy: request?.user?.id },
    );
    return result.affected > 0;
    return;
  }

  async updateMultipleUsers(data: UpdateMultipleUserDto, updatedBy: number) {
    if (isEmpty(data)) throw new BadRequestException('Thông tin cập nhật tài khoản không hợp lệ');

    const { userIds, ...rest } = data;

    const users = await this.userRepository.find({
      where: { id: In(userIds) },
    });
    const usersNeededUpdate = users.filter(user => user.status !== data.status);

    const validUsers = [];
    const invalidUsers = [];

    const userGroups = chunk(usersNeededUpdate, 5);

    for (const group of userGroups) {
      await Promise.all(
        group.map(async user => {
          try {
            const updateData = plainToInstance(UpdateUserDto, { id: user.id, ...rest, updatedBy });
            const updatedUser = await this.userRepository.save(updateData);
            if (updatedUser) validUsers.push({ userId: user.id });
          } catch (error) {
            const reason = error.response?.response?.message
              ? isArray(error.response?.response?.message)
                ? error.response?.response?.message.join(', ')
                : error.response?.response?.message
              : error.message;
            const code = error.response?.code || error.response?.response?.code;
            invalidUsers.push({
              userId: user.id,
              reason,
              code,
            });
          }
        }),
      );
    }

    return {
      validUsers,
      invalidUsers,
    };
  }

  async verifyBulkCreateAccountsFileData(
    buffer: Buffer,
    user: AuthUser,
    headers: Record<string, string>,
  ) {
    const companyId = Number(user.companyId);

    const data = ExcelUtils.read(buffer, 0);
    const colNames = {
      index: 'STT',
      name: 'Tên hiển thị',
      email: 'Email',
      password: 'Mật khẩu tạm',
    };
    const recordsInvalid: {
      index: string;
      name: string;
      email: string;
      password: string;
      errs: {
        err: string;
        message: string;
      }[];
    }[] = [];
    const recordsVaid: {
      index: string;
      name: string;
      email: string;
      password: string;
    }[] = [];
    const emails = data
      .map(it => {
        return it[colNames.email] ? it[colNames.email].toLowerCase() : undefined;
      })
      .filter(it => it !== undefined);
    const usersWithEmailsInFile = await this.userRepository
      .createQueryBuilder('u')
      .withDeleted()
      .where('u.email IN (:...emails) AND u.company_id = :companyId', { emails, companyId })
      .getMany();
    const usersWithEmailsInFileHashMap: Record<string, any> = {};
    usersWithEmailsInFile.forEach(u => {
      usersWithEmailsInFileHashMap[u.email.toLowerCase()] = true;
    });
    const hashmapEmailsInFile: Record<string, string> = {};
    for (const it of data) {
      if (
        !it[colNames.index] &&
        !it[colNames.name] &&
        !it[colNames.email] &&
        !it[colNames.password]
      ) {
        continue;
      }

      const errs: {
        err: string;
        message: string;
        index?: string;
      }[] = [];

      if (!it[colNames.index]) {
        errs.push({
          err: ErrorCode.COG_0010,
          message: 'Cột STT trống',
        });
      }
      if (!it[colNames.name]) {
        errs.push({
          err: ErrorCode.COG_0008,
          message: 'Cột tên hiển thị trống',
        });
      }
      if (
        !it[colNames.email] ||
        (it[colNames.email] && !StringUtils.validateEmail(it[colNames.email].toLowerCase()))
      ) {
        errs.push({
          err: ErrorCode.STD_0003,
          message: 'Email để trống hoặc sai định dạng',
        });
      }
      if (
        !it[colNames.password] ||
        (it[colNames.password] && it[colNames.password].toString().trim().length < 8)
      ) {
        errs.push({
          err: ErrorCode.STD_0013,
          message: 'Chưa nhập hoặc mật khẩu phải chứa ít nhất 8 ký tự',
        });
      }

      if (it[colNames.email] && it[colNames.index]) {
        if (hashmapEmailsInFile[it[colNames.email].toLowerCase()] === undefined) {
          hashmapEmailsInFile[it[colNames.email].toLowerCase()] = it[colNames.index].toString();
        } else {
          errs.push({
            err: ErrorCode.COG_0009,
            message: 'Email trùng với 1 email khác trong file excel',
            index: hashmapEmailsInFile[it[colNames.email].toLowerCase()],
          });
        }
      }

      if (
        it[colNames.email] &&
        StringUtils.validateEmail(it[colNames.email].toLowerCase()) &&
        usersWithEmailsInFileHashMap[it[colNames.email].toLowerCase()]
      ) {
        errs.push({
          err: ErrorCode.STD_0012,
          message: 'Email đã tồn tại trên hệ thống',
        });
      }

      if (errs.length > 0) {
        recordsInvalid.push({
          index: it[colNames.index]?.toString() || '',
          name: it[colNames.name]?.toString() || '',
          email: it[colNames.email] || '',
          password: it[colNames.password]?.toString() || '',
          errs,
        });
      } else {
        recordsVaid.push({
          index: it[colNames.index].toString() || '',
          name: it[colNames.name]?.toString() || '',
          email: it[colNames.email],
          password: it[colNames.password]?.toString() || '',
        });
      }
    }

    return {
      recordsVaid,
      recordsInvalid,
    };
  }

  async bulkCreateAccounts(user: AuthUser, body: BulkCreateAccountsDto) {
    const companyId = Number(user.companyId);

    const recordsInvalid: {
      index: string;
      name: string;
      email: string;
      password: string;
      errs: {
        err: string;
        message: string;
      }[];
    }[] = [];
    const emails = body.data.map(it => {
      return it.email.toLowerCase();
    });
    const usersWithEmailsInFile = await this.userRepository
      .createQueryBuilder('u')
      .withDeleted()
      .where('u.email IN (:...emails) AND u.company_id = :companyId', { emails, companyId })
      .getMany();
    const usersWithEmailsInFileHashMap: Record<string, any> = {};
    usersWithEmailsInFile.forEach(u => {
      usersWithEmailsInFileHashMap[u.email.toLowerCase()] = true;
    });
    const dataValid: CreateAccountDto[] = [];
    for (const data of body.data) {
      if (usersWithEmailsInFileHashMap[data.email.toLowerCase()]) {
        recordsInvalid.push({
          index: data.index,
          name: data.name,
          email: data.email,
          password: data.password,
          errs: [
            {
              err: ErrorCode.STD_0012,
              message: 'Email đã tồn tại trên hệ thống',
            },
          ],
        });
      } else {
        dataValid.push(data);
      }
    }

    const newUsers = dataValid.map(it => {
      return plainToInstance(User, {
        ...it,
        password: this.encodePassWord(it.password),
        updatedBy: user?.id,
        status: UserStatus.active,
        companyId,
      });
    });

    const connection = getConnection(identityConnection);
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.insert(User, newUsers);
      await queryRunner.commitTransaction();
      return { recordsInvalid, recordsValidCount: dataValid.length };
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException('Some account');
    } finally {
      await queryRunner.release();
    }
  }

  async loginMobile(body: LoginUserAppDto, resendCode = false) {
    const user = await this.userRepository.findOne({
      where: {
        email: body.email,
      },
      order: {
        id: 'ASC', // Sort by ID in ascending order (smallest first)
      },
    });
    if (!user)
      throw new BadRequestException({
        errorCode: ErrorCode.AGA_0001,
        errorMessage: 'Email không tồn tại',
      });

    if (user.status === UserStatus.deactivated)
      throw new BadRequestException({
        errorCode: ErrorCode.AGA_0002,
        errorMessage: 'Tài khoản bị deactivated',
      });

    if (
      resendCode &&
      user.lastTimeGenerateCode &&
      new Date(user.lastTimeGenerateCode).getTime() + 15 * 60 * 1000 > new Date().getTime()
    )
      throw new BadRequestException({
        errorCode: ErrorCode.AGA_0003,
        errorMessage: 'Limit thời gian 15p mới đc retry',
      });

    const code = this.generateCode();
    user.code = code;
    user.lastTimeGenerateCode = new Date();

    // Set expiration time (e.g., 15 minutes from now)
    const expirationTime = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes in the future
    const expirationTimestamp = Math.floor(expirationTime.getTime() / 1000); // Convert to Unix timestamp (in seconds)
    await this.userRepository.save(user);

    this.mailerService
      .sendMail({
        to: user.email,
        template: 'login-app',
        subject: 'LOGIN APP',
        context: {
          code,
        },
      })
      .then(e => {
        console.log(e);
      })
      .catch(e => {
        console.log(`loginMobile send email ERROR: ${e.message}`);
      });
    return { result: true };
  }

  async resendCodeMobile(body: LoginUserAppDto) {
    return await this.loginMobile(body, true);
  }

  generateCode(): string {
    const code = Math.floor(100000 + Math.random() * 900000); // Generates a random number from 100000 to 999999
    return code.toString();
  }

  async verifyMobileCode(body: VerifyCodeUserAppDto) {
    const { code, email } = body;
    const user = await this.userRepository.findOne({
      where: {
        status: UserStatus.active,
        email: email,
      },
      order: {
        id: 'ASC', // Sort by ID in ascending order (smallest first)
      },
    });
    if (!user) throw new NotFoundException();
    if (email === process.env.ACC_REVIEW_APP || '') {
      const otpReviewApp = process.env.OTP_REVIEW_APP || '123123';
      if (code !== otpReviewApp) {
        throw new BadRequestException({
          errorCode: ErrorCode.AGA_0006,
          errorMessage: 'Mã không hợp lệ. Vui lòng kiểm tra và thử lại',
        });
      }
    } else {
      // Check if the code is expired
      const expirationTime = 15 * 60 * 1000; // 15 minutes in milliseconds
      const currentTime = new Date().getTime();
      const codeGenerationTime = new Date(user.lastTimeGenerateCode).getTime();

      if (user.code !== code) {
        throw new BadRequestException({
          errorCode: ErrorCode.AGA_0006,
          errorMessage: 'Mã không hợp lệ. Vui lòng kiểm tra và thử lại',
        });
      }

      if (codeGenerationTime + expirationTime < currentTime) {
        throw new BadRequestException({
          errorCode: ErrorCode.AGA_0005,
          errorMessage: 'Code đã hết hạn, vui lòng lấy lại code',
        });
      }
    }

    const token = await this.authService.genJwtTokenMobile(user);
    user.lastTimeGenerateCode = null;
    user.isOnline = true;
    await this.userRepository.save(user);
    return { token };
  }

  async getListBusiness(user: { email: string }) {
    const companyIds = await this.userRepository.find({
      select: ['companyId'],
      where: { email: user.email, status: UserStatus.active },
    });
    if (companyIds.length === 0) return [];

    const businesses = await this.businessRepo
      .createQueryBuilder('b')
      .innerJoin('companies', 'c', 'c.business_id = b.id')
      .where('c.id IN (:...companyIds)', { companyIds: companyIds.map(i => i.companyId) })
      .andWhere('b.type = :type', { type: BusinessType.sale })
      .andWhere('c.status = :status', { status: CommonStatus.activated })
      .select([
        'b.id',
        'b.name',
        'c.id as "companyId"',
        'c.name as "companyName"',
        'c.logo as "companyLogo"',
      ])
      .getRawMany();

    return businesses.map(biz => ({
      id: biz.companyId,
      name: biz.companyName,
      logo: biz.companyLogo,
    }));
  }

  async selectBusiness(id: number, user: { email: string }) {
    const mUser = await this.userRepository
      .createQueryBuilder('u')
      .innerJoin('companies', 'c', 'c.id = u.companyId')
      .innerJoin('businesses', 'b', 'b.id = c.business_id')
      .where('b.type = :type', { type: BusinessType.sale })
      .andWhere('c.id = :id', { id })
      .andWhere('u.email = :email', { email: user.email })
      .andWhere('u.status = :status', { status: UserStatus.active })
      .getOne();

    if (!mUser) throw new BadRequestException({ errorMessage: 'Bạn không thể chọn biz này' });
    if (!mUser.sessionId) {
      const sid = this.authService.randomStr(32);
      mUser.sessionId = sid;
      await this.userRepository.save(mUser);
    }
    const token = await this.authService.getJwtToken(mUser);
    return { token };
  }

  async checkDescendant(userIdDescendant: number, userId: number): Promise<boolean> {
    const data = await this.userProfileRepository
      .createQueryBuilder('profile')
      .innerJoin('profile.department', 'department')
      .where('profile.userId = :userId', { userId })
      .andWhere(`department.leader_profile_id = profile.id`)
      .select(['profile.id', 'profile.user_id', 'department.id'])
      .getRawMany();
    if (isEmpty(data)) return false;

    const departmentIds = data.map(it => it.department_id);
    const departmentDescendant = await this.departmentsRepository
      .createQueryBuilder('d')
      .innerJoin('departments_closure', 'dc', `dc.id_descendant = d.id`)
      .where('dc.id_ancestor IN (:...departmentIds)', { departmentIds })
      .select(['dc.id_descendant as id_descendant'])
      .getRawMany();
    const departmenIdstDescendant = departmentDescendant.map(it => it.id_descendant);
    const userIdsDescendant = await this.userProfileRepository.find({
      where: {
        departmentId: In(departmenIdstDescendant),
      },
      select: ['userId'],
    });
    return userIdsDescendant.some(it => it.userId == userIdDescendant);
  }

  async getLinkChangePasswordMobile(userId: number) {
    const user = await this.userRepository.findOne({ id: userId, status: UserStatus.active });
    if (!user) throw new BadRequestException('User không tồn tại');

    const code = this.generateCode();
    user.code = code;
    user.lastTimeGenerateChangePasswordCode = new Date();

    // Set expiration time (e.g., 15 minutes from now)
    const expirationTime = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes in the future
    const expirationTimestamp = Math.floor(expirationTime.getTime() / 1000); // Convert to Unix timestamp (in seconds)
    await this.userRepository.save(user);

    this.mailerService
      .sendMail({
        to: user.email,
        template: 'reset-password-mobile',
        subject: 'RESET PASSWORD',
        context: {
          code,
        },
      })
      .then(e => {
        console.log(e);
      })
      .catch(e => {
        console.log(`change password send email ERROR: ${e.message}`);
      });
    return { result: true };
  }

  async changePasswordMobile(body: UpdatePasswordAppDto, userId: number) {
    const { code, password } = body;
    const user = await this.userRepository.findOne({ id: userId, status: UserStatus.active });
    if (!user) throw new BadRequestException('User không tồn tại');

    // Check if the code is expired
    const expirationTime = 15 * 60 * 1000; // 15 minutes in milliseconds
    const currentTime = new Date().getTime();
    const codeGenerationTime = new Date(user.lastTimeGenerateChangePasswordCode).getTime();

    if (codeGenerationTime + expirationTime < currentTime) {
      throw new BadRequestException({
        errorCode: ErrorCode.AGA_0004,
        errorMessage: 'Code đã hết hạn, vui lòng lấy lại code',
      });
    }

    if (user.code !== code)
      throw new BadRequestException({
        errorCode: ErrorCode.AGA_0006,
        errorMessage: 'Mã không hợp lệ. Vui lòng kiểm tra và thử lại',
      });

    user.lastUpdatedPassword = new Date();
    user.password = this.encodePassWord(password);
    user.lastTimeGenerateChangePasswordCode = null;
    user.code = null;
    const sid = this.authService.randomStr(32);
    const skey = `${USER_SESSION}.${user.id}.`;

    user.sessionId = sid;
    await this.redisCache.set(skey, sid);
    return this.userRepository.save(user);
  }

  async verifyCodeChangePassword(body: VerifyCodeUpdatePasswordAppDto, userId: number) {
    const { code } = body;
    const user = await this.userRepository.findOne({ id: userId, status: UserStatus.active });
    if (!user) throw new BadRequestException('User không tồn tại');

    // Check if the code is expired
    const expirationTime = 15 * 60 * 1000; // 15 minutes in milliseconds
    const currentTime = new Date().getTime();
    const codeGenerationTime = new Date(user.lastTimeGenerateChangePasswordCode).getTime();

    if (codeGenerationTime + expirationTime < currentTime) {
      throw new BadRequestException({
        errorCode: ErrorCode.AGA_0004,
        errorMessage: 'Code đã hết hạn, vui lòng lấy lại code',
      });
    }

    if (user.code !== code)
      throw new BadRequestException({
        errorCode: ErrorCode.AGA_0006,
        errorMessage: 'Mã không hợp lệ. Vui lòng kiểm tra và thử lại',
      });
    return {};
  }

  async deactivateAccount(userId: number) {
    const user = await this.userRepository.findOne({ id: userId, status: UserStatus.active });
    if (!user) throw new BadRequestException('User không tồn tại');

    user.status = UserStatus.deactivated;
    user.sessionId = null;
    await this.userRepository.save(user);
    return {};
  }

  async deactivateAccountByEmail(email: string) {
    const mUser = await this.userRepository
      .createQueryBuilder('u')
      .innerJoin('companies', 'c', 'c.id = u.companyId')
      .innerJoin('businesses', 'b', 'b.id = c.business_id')
      .where('b.type = :type', { type: BusinessType.sale })
      .andWhere('u.email = :email', { email })
      .select(['u.id'])
      .getMany();
    if (mUser.length === 0) throw new BadRequestException('User không tồn tại');

    await this.userRepository.update(
      {
        email,
        id: In(mUser.map(it => it.id)),
      },
      {
        status: UserStatus.deactivated,
        sessionId: null,
      },
    );
    return {};
  }

  async getDescendants(userId: number, query: GetDescendantsDto) {
    const { data: dids } = await this.amqpConnection.request<Record<string, any>>({
      exchange: 'identity-service',
      routingKey: 'check-user-descendants-v2',
      payload: { id: userId, countryId: query.countryId },
      timeout: 10000,
    });
    return {
      marketerIds: dids[0],
      saleIds: dids[1],
      carePageIds: dids[2],
    };
  }

  async checkLastActiveOfUsers() {
    const jobName = 'check-last-active-of-user';
    try {
      const repeatable = await this.userQueue.getRepeatableJobs();
      for (const job1 of repeatable) {
        if (job1.id !== jobName) {
          continue;
        }
        await this.userQueue.removeRepeatableByKey(job1.key);
      }
      const queue = await this.userQueue.add(
        jobName,
        {},
        {
          attempts: 3,
          repeat: {
            every: 5 * 60 * 1000,
            tz: 'Asia/Ho_Chi_Minh',
          },
          jobId: jobName,
          removeOnComplete: true,
          removeOnFail: false,
        },
      );
      console.log(
        jobName +
          ' will run at ' +
          moment(queue.timestamp + queue.opts.delay)
            .tz('Asia/Ho_Chi_Minh')
            .format('DD/MM/yyyy HH:mm'),
      );
    } catch (e) {
      console.log('Active queue check last active of users error:', e?.stack);
    }
  }

  async checkLastActiveOfUser() {
    const keys = await this._scanLogLastActiveUser();
    for (const key of keys) {
      const userId = +key.split(':')[1];
      const lastActive = parseInt(await this.redis.get(`last-active-of-user:${userId}`), 10);
      if (new Date().getTime() - lastActive > 20 * 60 * 1000) {
        const user = await this.userRepository
          .createQueryBuilder('u')
          .leftJoinAndSelect('u.company', 'company')
          .leftJoinAndSelect('company.business', 'business')
          .where('u.id = :userId', { userId })
          .getOne();
        const businessType = user?.company?.business?.type;
        if (user && businessType === BusinessType.sale) {
          user.isOnline = false;
          const sid = this.authService.randomStr(32);
          const skey = `${USER_SESSION}.${user.id}.`;

          user.sessionId = sid;
          await this.redisCache.set(skey, sid);
          await this.userRepository.save(user);
        }
        console.log(`Auto processing offline user with id ${userId}`);
        await this.redis.del(`last-active-of-user:${userId}`);
      }
    }
  }

  async _scanLogLastActiveUser() {
    let cursor = '0';
    let keys: string[] = [];
    do {
      const [newCursor, newKeys] = await this.redis.scan(
        cursor,
        'MATCH',
        '*last-active-of-user:*',
        'COUNT',
        100,
      );
      cursor = newCursor;
      keys = [...keys, ...newKeys];
    } while (cursor !== '0');

    return keys;
  }

  async logout(userId: number) {
    const user = await this.userRepository.findOne({ id: userId });
    if (user) {
      user.isOnline = false;
      const sid = this.authService.randomStr(32);
      const skey = `${USER_SESSION}.${user.id}.`;

      user.sessionId = sid;
      await this.redisCache.set(skey, sid);
      await this.userRepository.save(user);
    }
    return {};
  }
}
