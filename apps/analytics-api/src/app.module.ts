import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  catalogFfmConnection,
  identityConnection,
  messageConnection,
  orderConnection,
  orderFfmConnection,
} from 'core/constants/database-connection.constant';
import { BaseAuthModule } from 'core/auth/auth.module';
import { RedisCacheModule } from 'core/cache/redisCache.module';
import { HttpCacheInterceptor } from 'core/filters/http-cache/http-cache.filter';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AnalyticsModule } from './modules/analytics/analytics.module';
import 'core/extensions/typeorm-virtual-column.extention';
import { BullModule } from '@nestjs/bull';
import { QueueUIModule } from './modules/queue-ui/queue-ui.module';
import { AppController } from './app.controller';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { PREFIX_DASHBOARD_CACHE } from './constants/dashboard-cache.constant';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath:
        'apps/analytics-api/' + (process.env.NODE_ENV ? `.env.${process.env.NODE_ENV}` : '.env'),
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_MESSAGE_HOST,
      port: parseInt(process.env.DATABASE_MESSAGE_PORT),
      username: process.env.DATABASE_MESSAGE_USERNAME,
      password: process.env.DATABASE_MESSAGE_PASSWORD,
      database: process.env.DATABASE_MESSAGE,
      entities: [__dirname + '/entities/message/*.entity{ .ts,.js}'],
      name: messageConnection,
      logging: ['error'],
      extra: {
        max: 50,
      },
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_FFM_HOST,
      port: parseInt(process.env.DATABASE_PORT),
      username: process.env.DATABASE_FFM_USERNAME,
      password: process.env.DATABASE_FFM_PASSWORD,
      database: process.env.DATABASE_ORDER_FFM,
      entities: [__dirname + '/entities/ffm-order/*.entity{ .ts,.js}'],
      name: orderFfmConnection,
      logging: ['error'],
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST,
      port: parseInt(process.env.DATABASE_PORT),
      username: process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_FFM_CATALOG,
      entities: [__dirname + '/entities/ffm-catalog/*.entity{ .ts,.js}'],
      name: catalogFfmConnection,
      logging: ['error'],
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST,
      port: parseInt(process.env.DATABASE_PORT),
      username: process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_IDENTITY,
      entities: [__dirname + '/entities/identity/*.entity{ .ts,.js}'],
      name: identityConnection,
      logging: ['error'],
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST,
      port: parseInt(process.env.DATABASE_PORT),
      username: process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_ORDER,
      entities: [__dirname + '/entities/order/*.entity{ .ts,.js}'],
      name: orderConnection,
      logging: ['error'],
      extra: {
        max: 30,
      },
    }),
    BaseAuthModule.forRoot(),
    BullModule.forRoot({
      redis: {
        password: process.env.REDIS_PASSWORD,
        db: 1,
        port: parseInt(process.env.REDIS_PORT),
        host: process.env.REDIS_HOST,
      },
      prefix: `analytics-queue-${process.env.REDIS_DB}`,
    }),
    RedisModule.forRoot({
      config: {
        password: process.env.REDIS_PASSWORD,
        db: Number(process.env.REDIS_DB_NUMBER) || 0,
        port: parseInt(process.env.REDIS_PORT),
        host: process.env.REDIS_HOST,
        keyPrefix: `${PREFIX_DASHBOARD_CACHE}-${process.env.REDIS_DB}`,
      },
    }),
    QueueUIModule,
    AnalyticsModule,
    RedisCacheModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: HttpCacheInterceptor,
    },
  ],
  controllers: [AppController],
})
export class AppModule {}
