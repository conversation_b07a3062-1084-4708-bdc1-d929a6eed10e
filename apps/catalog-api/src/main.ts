import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { TransformInterceptor } from 'core/interceptors';
import { AppModule } from './app.module';
import { Logger } from 'core/interceptors/logger.interceptors';
import 'core/extensions';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors({
    origin: [/https?:\/\/.*$/],
    credentials: true,
    allowedHeaders: '*',
  });
  app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true }));
  app.useGlobalInterceptors(
    new TransformInterceptor(),
    process.env.SEQ_KEY ? new Logger('catalog-api') : undefined,
  );

  const options = new DocumentBuilder()
    .setTitle('Node Backend')
    .setDescription('Backend writing in Node.js')
    .setVersion('0.1.0')
    .addServer(process.env.BASE_API_URL || '')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('swagger', app, document);
  try {
    await app.startAllMicroservices();
    await app.listen(process.env.WEBSITES_PORT || process.env.PORT || 3000);
  } catch (e) {
    console.log('e', e);
    await app.close();
  }
}

bootstrap();
