import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { catalogConnection, identityConnection, orderConnection } from 'core/constants/database-connection.constant';
import { BaseAuthModule } from 'core/auth/auth.module';
import { CatalogModule } from './modules/catalog/catalog.module';
import { ConfigModule } from '@nestjs/config';
import 'core/extensions/typeorm-virtual-column.extention';
import { AppController } from './app.controller';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath:
        'apps/catalog-api/' + (process.env.NODE_ENV ? `.env.${process.env.NODE_ENV}` : '.env'),
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      ...(process.env.DATABASE_USERNAME_REPLICATE
        ? {
            replication: {
              master: {
                host: process.env.DATABASE_HOST,
                port: parseInt(process.env.DATABASE_PORT),
                username: process.env.DATABASE_USERNAME,
                password: process.env.DATABASE_PASSWORD,
                database: process.env.DATABASE_CATALOG,
              },
              slaves: [
                {
                  host: process.env.DATABASE_HOST_REPLICATE,
                  port: parseInt(process.env.DATABASE_PORT_REPLICATE),
                  username: process.env.DATABASE_USERNAME_REPLICATE,
                  password: process.env.DATABASE_PASSWORD_REPLICATE,
                  database: process.env.DATABASE_CATALOG,
                },
              ],
            },
          }
        : {
            host: process.env.DATABASE_HOST,
            port: parseInt(process.env.DATABASE_PORT),
            username: process.env.DATABASE_USERNAME,
            password: process.env.DATABASE_PASSWORD,
            database: process.env.DATABASE_CATALOG,
          }),
      entities: [__dirname + '/entities/*.entity{ .ts,.js}'],
      name: catalogConnection,
      logging: ['error'],
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST_REPLICATE || process.env.DATABASE_HOST,
      port: parseInt(process.env.DATABASE_PORT_REPLICATE || process.env.DATABASE_PORT),
      username: process.env.DATABASE_USERNAME_REPLICATE || process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD_REPLICATE || process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_IDENTITY,
      entities: [__dirname + '/entities-identity/*.entity{ .ts,.js}'],
      name: identityConnection,
      logging: ['error'],
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST_REPLICATE || process.env.DATABASE_HOST,
      port: parseInt(process.env.DATABASE_PORT_REPLICATE || process.env.DATABASE_PORT),
      username: process.env.DATABASE_USERNAME_REPLICATE || process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD_REPLICATE || process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_ORDER,
      entities: [__dirname + '/entities-order/*.entity{ .ts,.js}'],
      name: orderConnection,
      logging: ['error'],
    }),
    CatalogModule,
    BaseAuthModule.forRoot(),
    RabbitMQModule.forRootAsync(RabbitMQModule, {
      useFactory: () => ({
        registerHandlers: (!process.env.CONSUMER || process.env.CONSUMER == 'true') ? true : false,
        exchanges: [
          {
            name: 'CatalogService.Orders.StatusChanged',
            type: 'fanout',
          },
          {
            name: 'OrderService.Orders.StatusChanged',
            type: 'fanout',
          },
          {
            name: 'CatalogService.Orders.ReturnSheetCreated',
            type: 'fanout',
          },
          {
            name: 'OrderService.Orders.ReturnSheetValidated',
            type: 'fanout',
          },
          {
            name: 'CatalogService.Orders.StockFilled',
            type: 'fanout',
          },
          {
            name: 'CatalogService.Product.SyncPancake',
            type: 'direct',
          },
          {
            name: 'catalog-service-variants',
            type: 'direct',
          },
          {
            name: 'catalog-service-products',
            type: 'direct',
          },
          {
            name: 'shopify-webhook-event',
            type: 'direct',
          },
        ],
        uri: process.env.RMQ_HOST,
        prefetchCount: 20,
        connectionInitOptions: { wait: false },
      }),
    }),
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule {}
