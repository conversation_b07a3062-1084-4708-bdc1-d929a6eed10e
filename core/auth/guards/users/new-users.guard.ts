import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AuthService } from '../../services/auth.service';
import { intersection, isEmpty, isNil, isNull } from 'lodash';
import { SystemType, SystemTypes } from 'core/auth/decorators/auth/new-auth.decorator';

@Injectable()
export class UsersGuard implements CanActivate {
  constructor(private readonly reflector: Reflector, private readonly authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const disableScopeGuard =
      this.reflector.get<string>('disable-scope-guard', context.getHandler()) === 'true';

    const system = this.reflector.get<SystemType>('system', context.getHandler());
    const requiredPermissions = this.reflector.get<SystemTypes[SystemType][]>(
      'permissions',
      context.getHandler(),
    );

    let request;
    switch (context.getType<string>()) {
      case 'http':
        request = context.switchToHttp().getRequest();
        break;
      case 'graphql':
        const ctx = GqlExecutionContext.create(context);
        request = ctx.getContext().req;
        break;
    }

    const isActive = await this.authService.checkActive(request.user.id, request.user.sid);
    if (!isActive) return false;
    if (isActive == -1) throw new UnauthorizedException('Session not match');

    const profiles = await this.authService.checkUserProfiles(
      request.user.id,
      requiredPermissions,
      disableScopeGuard ? undefined : request.headers['country-ids'],
      request.headers['project-ids']?.split(','),
    );
    request.user.profiles = profiles;

    // console.log(`system`, system);
    // console.log(`permissions`, requiredPermissions);
    if (disableScopeGuard) return true;

    if (isEmpty(profiles)) return false;

    const descendants = await this.authService.checkUserDescendants(request.user.id);
    request.user.descendants = {
      marketerIds: descendants[0],
      saleIds: descendants[1],
      carePageIds: descendants[2],
    };

    // save last active of user
    if (request?.user?.service === 'sale') {
      await this.authService.saveLastActiveOfUser(request.user.id);
    }

    // const scopesData = profiles[2]
    // const scopes = scopesData?.map(it => ({
    //   projectId: it[0],
    //   countryId: it[1],
    // }));
    // request.user.scopes = scopes;
    // console.log(`scopesData`, scopesData);
    // if (isEmpty(scopesData)) return false;
    // const headerProjectIds = request.headers['project-ids'];
    // const headerCountryId = request.headers['country-ids'];

    // let hasAllProjects = false;

    // const projectIds = scopes.reduce((prev, item) => {
    //   if (!isNull(item.countryId) && String(item.countryId) !== headerCountryId) return prev;
    //   if (item.projectId === null) hasAllProjects = true;
    //   prev.push(String(item.projectId));
    //   return prev;
    // }, []);

    // if (!hasAllProjects && isEmpty(projectIds)) return false;

    // request.headers['project-ids'] = hasAllProjects
    //   ? headerProjectIds
    //   : !isNil(headerProjectIds)
    //   ? intersection(headerProjectIds.split(','), projectIds).join(',')
    //   : projectIds.join(',');

    // request.query.projectIds = hasAllProjects
    //   ? request.query.projectIds
    //   : !isNil(request.query.projectIds)
    //   ? intersection(request.query.projectIds, projectIds)
    //   : projectIds;

    return isActive > 0;
  }
}
