import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AuthService } from '../../services/auth.service';
import { intersection, isEmpty, isNil } from 'lodash';

@Injectable()
export class UsersGuard implements CanActivate {
  constructor(private readonly reflector: Reflector, private readonly authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    let request;
    switch (context.getType<string>()) {
      case 'http':
        request = context.switchToHttp().getRequest();
        break;
      case 'graphql':
        const ctx = GqlExecutionContext.create(context);
        request = ctx.getContext().req;
        break;
    }

    const isActive = await this.authService.checkActive(request.user.id, request.user.sid);
    if (!isActive) return false;
    if (isActive == -1) throw new UnauthorizedException('Session not match');

    const scopesData = await this.authService.checkScopes(request.user.id);
    // console.log(`scopesData`, scopesData);
    if (!isEmpty(scopesData)) {
      const scopes = scopesData.map(it => ({
        projectId: it[0],
        countryIds: it[1],
      }));
      const headerProjectIds = request.headers['project-ids'];
      const headerCountryId = request.headers['country-ids'];
      const projectIds = scopes.reduce((prev, item) => {
        if (!item.countryIds.includes(headerCountryId)) return prev;
        prev.push(String(item.projectId));
        return prev;
      }, []);
      request.headers['project-ids'] = !isNil(headerProjectIds)
        ? intersection(headerProjectIds.split(','), projectIds).join(',')
        : projectIds.join(',');
    }

    // save last active of user
    if (request?.user?.service === 'sale') {
      await this.authService.saveLastActiveOfUser(request.user.id);
    }

    return isActive > 0;
  }
}
