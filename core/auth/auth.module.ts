import { DynamicModule } from '@nestjs/common';
import { JwtStrategy } from './strategies/jwt.strategy';
import { GraphqlJwtAuthGuard } from './guards/graphql-jwt-auth/graphql-jwt-auth.guard';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { AuthService } from './services/auth.service';
import { getRmqHost } from 'core/utils/loadEnv';
import { RedisModule } from '@liaoliaots/nestjs-redis';

export class BaseAuthModule {
  public static forRoot(): DynamicModule {
    return {
      module: BaseAuthModule,
      global: true,
      imports: [
        RabbitMQModule.forRootAsync(RabbitMQModule, {
          useFactory: () => ({
            uri: getRmqHost(),
            prefetchCount: 2,
            registerHandlers: false,
          }),
        }),
        RedisModule.forRoot({
          config: {
            password: process.env.REDIS_PASSWORD,
            db: 1,
            port: parseInt(process.env.REDIS_PORT),
            host: process.env.REDIS_HOST,
            keyPrefix: `ag-identity-cache-${process.env.REDIS_DB}`,
          },
        }),
      ],
      exports: [AuthService],
      providers: [JwtStrategy, GraphqlJwtAuthGuard, AuthService],
    };
  }
}
